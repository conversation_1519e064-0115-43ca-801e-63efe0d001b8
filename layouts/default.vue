<template>
  <div class="app-layout">
    <!-- 桌面版側邊欄 -->
    <ClientOnly>
      <AppSidebar
        :current-view="currentView"
        :locale="locale"
        @update:view="updateView"
        @change-locale="changeLocale"
        @login="showLoginModal = true"
      />
    </ClientOnly>
    
    <!-- 主內容區域 -->
    <main class="main-content">
      <div class="content-wrapper">
        <slot />
      </div>
    </main>

    <!-- 手機版底部導航 -->
    <ClientOnly>
      <MobileBottomNav @navigate="handleMobileNavigation" @login="showLoginModal = true" />
    </ClientOnly>

    <!-- 登入彈窗 -->
    <LoginDialog 
      v-if="showLoginModal" 
      v-model="showLoginModal" 
      @login-success="handleLoginSuccess"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch, provide } from 'vue';
import { useRouter, useRoute } from '#app';
import { useCardStore } from '~/stores/card';
import { useDeckStore } from '~/stores/deck';
import { useAuthStore } from '~/stores/auth';
import { useI18n } from 'vue-i18n';
import LoginDialog from '~/components/LoginDialog.vue';
import MobileBottomNav from '~/components/MobileBottomNav.vue';

const route = useRoute();
const { locale } = useI18n();
const cardStore = useCardStore();
const deckStore = useDeckStore();
const userStore = useAuthStore();

const showLoginModal = ref(false);
const currentViewRef = ref('home');

// 根据当前路由计算当前视图
const currentView = computed(() => currentViewRef.value);

// 提供顯示Tier表的方法給子組件
const showTierList = ref(false);
provide('showTierList', showTierList);

// 根據路由更新當前視圖
watch(() => route.path, (path) => {
  if (!path) return;
  
  const normalizedPath = path.replace(`/${locale.value}`, '');
  
  if (normalizedPath === '/') {
    currentViewRef.value = 'home';
  } else if (normalizedPath === '/series') {
    currentViewRef.value = 'series';
  } else if (normalizedPath.startsWith('/series/')) {
    currentViewRef.value = 'series';
  } else if (normalizedPath.includes('/deck')) {
    currentViewRef.value = 'deck';
  } else if (normalizedPath.includes('/social')) {
    currentViewRef.value = 'social';
  } else if (normalizedPath.includes('/experience')) {
    currentViewRef.value = 'experience';
  } else if (normalizedPath.includes('/Goodsgroup')) {
    currentViewRef.value = 'Goodsgroup';
  } else if (normalizedPath.includes('/tierlist')) {
    currentViewRef.value = 'tierlist';
  }
}, { immediate: true });

// 更新視圖並導航
const updateView = (view) => {
  if (view === 'home') {
    navigateTo('/');
  } else {
    navigateTo(`/${view}`);
  }
};

// 更改語言
const changeLocale = (newLocale) => {
  locale.value = newLocale;
};

// 處理手機版導航
const handleMobileNavigation = (route) => {
  navigateTo(route);
};

// 處理登入成功
const handleLoginSuccess = () => {
  showLoginModal.value = false;
};
</script>

<style>
/* 應用程式佈局 */
.app-layout {
  display: flex;
  height: 100vh;
  height: 100dvh; /* 使用動態視窗高度 */
  background-color: #0f1726;
  overflow: hidden;
}

/* 主內容區域 */
.main-content {
  flex: 1;
  overflow: auto;
  padding-left: 0;
  width: 100%;
  position: relative;
}

@media (min-width: 768px) {
  .main-content {
    padding-left: 16rem; /* 256px */
  }
}

/* 內容包裝器 - 關鍵改動 */
.content-wrapper {
  width: 100%;
  overflow-x: hidden;
  /* 移除固定的 padding-bottom，改由 CSS 處理 */
}

/* 桌面版不需要底部間距 */
@media (min-width: 768px) {
  .content-wrapper {
    height: 100%;
    padding-bottom: 0;
  }
}

/* 基础容器样式 - 簡化並移除可能導致問題的設定 */
.container {
  max-width: 100%;
  margin: 0 auto;
  overflow-x: hidden;
}

/* 基础样式 */
body,
html {
  margin: 0;
  padding: 0;
  height: 100vh;
  height: 100dvh;
  width: 100%;
  background-color: #0f1726;
  overflow-x: hidden;
}

/* 手機版優化 - 移除複雜的設定 */
@media (max-width: 767px) {
  .container {
    width: 100%;
    max-width: 100vw;
    overflow-x: hidden;
    padding: 0 0.75rem;
  }

  /* 調整卡片網格布局 */
  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 8px;
  }

  /* 調整按鈕大小 */
  button {
    padding: 8px 12px;
    font-size: 14px;
  }

  /* 調整輸入框大小 */
  input, select, textarea {
    padding: 8px 12px;
    font-size: 14px;
  }

  /* 調整標題大小 */
  h1 {
    font-size: 24px;
  }
  h2 {
    font-size: 20px;
  }
  h3 {
    font-size: 18px;
  }

  /* 調整間距 */
  .space-y-4 > * + * {
    margin-top: 12px;
  }
  .space-y-6 > * + * {
    margin-top: 16px;
  }
}
</style>