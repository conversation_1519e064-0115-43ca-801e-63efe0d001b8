<template>
  <div v-if="canInstall" class="pwa-install-button">
    <button
      @click="installPWA"
      class="w-full flex items-center justify-center gap-2 px-4 py-2 bg-gradient-to-r from-[#1e90ff] to-[#00bfff] text-white rounded-lg hover:shadow-lg hover:shadow-[rgba(30,144,255,0.3)] transition-all duration-300"
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
        <polyline points="7 10 12 15 17 10"/>
        <line x1="12" y1="15" x2="12" y2="3"/>
      </svg>
      <span class="hidden md:block">安裝應用程式</span>
    </button>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const canInstall = ref(false)
let deferredPrompt = null

onMounted(() => {
  // 監聽 beforeinstallprompt 事件
  window.addEventListener('beforeinstallprompt', (e) => {
    // 阻止 Chrome 67 及更早版本自動顯示安裝提示
    e.preventDefault()
    // 保存事件，以便稍後觸發
    deferredPrompt = e
    // 更新 UI 通知用戶可以安裝 PWA
    canInstall.value = true
  })

  // 監聽 appinstalled 事件
  window.addEventListener('appinstalled', () => {
    // 安裝完成後隱藏安裝按鈕
    canInstall.value = false
    deferredPrompt = null
  })
})

const installPWA = async () => {
  if (!deferredPrompt) return

  // 顯示安裝提示
  deferredPrompt.prompt()
  
  // 等待用戶響應
  const { outcome } = await deferredPrompt.userChoice
  
  // 根據用戶的選擇更新 UI
  if (outcome === 'accepted') {
    // 用戶接受了安裝
  } else {
    // 用戶拒絕了安裝
  }
  
  // 清除保存的提示
  deferredPrompt = null
  canInstall.value = false
}
</script>

<style scoped>
.pwa-install-button {
  width: 100%;
}
</style> 