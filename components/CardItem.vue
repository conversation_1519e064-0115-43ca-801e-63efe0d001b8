<template>
  <div class="relative group">
    <img 
      :src="card.image" 
      :alt="card.name" 
      class="w-full rounded-lg cursor-pointer" 
      @click="$emit('view-details', card)"
      @error="handleImageError"
    />
    <div 
      class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
    >
      <button 
        v-if="inDeck"
        class="bg-red-600 rounded-full w-8 h-8 flex items-center justify-center hover:bg-red-700"
        @click="$emit('remove-card')"
      >
        <component :is="X" :size="16" />
      </button>
      <button 
        v-else
        class="bg-green-600 rounded-full w-8 h-8 flex items-center justify-center hover:bg-green-700"
        @click="$emit('add-card', card)"
      >
        <span class="text-white text-xl">+</span>
      </button>
    </div>
    <div class="flex justify-between items-center mt-1">
      <p class="truncate">{{ card.name }}</p>
      <button 
        class="text-blue-400 hover:text-blue-300"
        @click="$emit('view-details', card)"
      >
        <component :is="Info" :size="16" />
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { Info, X } from 'lucide-vue-next';

// 定义默认图片
const defaultCardImage = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjNjY2IiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgY2xhc3M9Imx1Y2lkZSBsdWNpZGUtaW1hZ2UiPjxyZWN0IHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgeD0iMyIgeT0iMyIgcng9IjIiIHJ5PSIyIi8+PGNpcmNsZSBjeD0iOC41IiBjeT0iOC41IiByPSIxLjUiLz48cGF0aCBkPSJtMjEgMTUtMy45LTMuOS1yZWN0IHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgeD0iMyIgeT0iMyIgcng9IjIiIHJ5PSIyIi8+PHBhdGggZD0iTTE1IDEybC0zIDMtMyAzIDktOSIvPjwvc3ZnPg==';

const props = defineProps({
  card: Object,
  inDeck: Boolean
});

// 处理图片加载错误
const handleImageError = (e) => {
  // console.warn('Failed to load card image, using default');
  e.target.src = defaultCardImage;
  e.target.onerror = null; // 防止无限循环
};

defineEmits(['view-details', 'add-card', 'remove-card']);
</script> 