<template>
  <div class="deck-section">
    <div v-if="loading" class="loading-container">
      <el-skeleton style="width: 100%" :count="3">
        <template #template>
          <div style="padding: 14px; margin-bottom: 16px">
            <el-skeleton-item variant="text" style="width: 100%" />
          </div>
        </template>
      </el-skeleton>
    </div>
    <div v-else-if="!data || !data.length" class="empty-state">
      <i class="el-icon-document" />
      <p>目前尚無符合條件的牌組</p>
    </div>
    <div v-else>
      <!-- 桌面版表格視圖 -->
      <div class="hidden md:block deck-table-view">
        <div class="deck-table-header">
          <span class="deck-table-col env">環境</span>
          <span class="deck-table-col title">標題</span>
          <span class="deck-table-col desc">說明</span>
          <span class="deck-table-col type">類型</span>
          <span class="deck-table-col author">作者</span>
          <span class="deck-table-col time">時間</span>
        </div>
        <div v-for="deck in data" :key="deck.id" class="deck-table-row" @click="handleDeckClick(deck)">
          <span class="deck-table-col env">{{ deck.env_title || '無' }}</span>
          <span class="deck-table-col title" @click.stop="handleTitleClick(deck.title)">{{ deck.title }}</span>
          <span class="deck-table-col desc">{{ deck.desc }}</span>
          <span class="deck-table-col type">
            <span class="type-badge" :class="{ 'is-general': deck.is_identify === 1, 'is-tournament': deck.is_identify === 2 }">
              {{ deck.is_identify === 1 ? '一般' : deck.is_identify === 2 ? '比賽' : '未知' }}
            </span>
          </span>
          <span class="deck-table-col author">{{ deck.user_info?.nickname || '未知' }}</span>
          <span class="deck-table-col time">{{ formatTime(deck.create_time) }}</span>
        </div>
      </div>
      
      <!-- 手機版緊湊列表 -->
      <div class="md:hidden deck-mobile-view">
      <div v-for="deck in data" :key="deck.id" class="deck-mobile-item" @click="handleDeckClick(deck)">
        <div class="flex items-center gap-3 min-h-[80px] p-3">
          <!-- 類型顏色標示 -->
          <div class="w-1 h-16 rounded-full flex-shrink-0" :class="getTypeBgClass(deck.is_identify)"></div>
          
          <!-- 主要內容 -->
          <div class="flex-1 min-w-0">
            <h3 class="font-medium text-white text-sm mb-1 line-clamp-1">{{ deck.title }}</h3>
            <p class="text-xs text-gray-400 mb-1">{{ deck.env_title || '無' }} • {{ deck.user_info?.nickname || '未知' }}</p>
            <p class="text-xs text-gray-500 line-clamp-2 leading-relaxed">{{ deck.desc || '無說明' }}</p>
          </div>
          
          <!-- 時間 -->
          <div class="text-xs text-gray-500 ml-2 flex-shrink-0">
            {{ formatTime(deck.create_time) }}
          </div>
        </div>
      </div>
      </div>
    </div>

    <div v-if="data && data.length" class="pagination-container">
      <el-pagination
        background
        layout="prev, pager, next"
        :total="totalItems"
        :page-size="pageSize"
        :current-page="currentPage"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

const props = defineProps({
  loading: Boolean,
  data: Array,
  totalItems: Number,
  pageSize: Number,
  currentPage: Number
});

const emit = defineEmits(['page-change', 'deck-click', 'title-click']);

const handlePageChange = (page) => {
  emit('page-change', page);
};

const handleDeckClick = (deck) => {
  emit('deck-click', deck);
};

const handleTitleClick = (title) => {
  emit('title-click', title);
};

// 獲取類型背景顏色（手機版列表用）
const getTypeBgClass = (isIdentify) => {
  switch (isIdentify) {
    case 1: return 'bg-blue-500'    // 一般
    case 2: return 'bg-orange-500'  // 比賽
    default: return 'bg-gray-500'   // 其他
  }
}

const formatTime = (timestamp) => {
  if (!timestamp) return '未知';
  
  let date;
  
  // 檢查是否為字符串格式的日期時間
  if (typeof timestamp === 'string' && timestamp.includes('-')) {
    date = new Date(timestamp);
  } else {
    // 處理Unix時間戳（數字）
    date = new Date(timestamp * 1000);
  }
  
  if (isNaN(date.getTime())) {
    return '未知';
  }
  
  return date.toLocaleDateString();
};
</script>

<style scoped>
.deck-section {
  margin-top: 20px;
  width: 100%;
}

/* 手機版列表樣式 */
.deck-mobile-view {
  background-color: rgba(20, 30, 48, 0.7);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(30, 144, 255, 0.2);
  backdrop-filter: blur(8px);
  width: 100%;
}

.deck-mobile-item {
  border-bottom: 1px solid rgba(30, 144, 255, 0.1);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.deck-mobile-item:last-child {
  border-bottom: none;
}

.deck-mobile-item:hover {
  background-color: rgba(30, 144, 255, 0.1);
}

/* 文字截斷樣式 */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.loading-container, 
.empty-state {
  background-color: rgba(20, 30, 48, 0.5);
  border-radius: 16px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(30, 144, 255, 0.1);
  backdrop-filter: blur(4px);
  width: 100%;
}

.empty-state i {
  font-size: 48px;
  color: rgba(255, 255, 255, 0.3);
  margin-bottom: 16px;
}

.empty-state p {
  color: rgba(255, 255, 255, 0.6);
  font-size: 16px;
}

.deck-table-view {
  background-color: rgba(20, 30, 48, 0.7);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(30, 144, 255, 0.2);
  backdrop-filter: blur(8px);
  transition: all 0.3s ease;
  width: 100%;
}

.deck-table-header,
.deck-table-row {
  display: flex;
  align-items: center;
  padding: 16px 24px;
  width: 100%;
}

.deck-table-header {
  background: linear-gradient(90deg, rgba(12, 20, 35, 0.95), rgba(20, 40, 80, 0.95));
  font-weight: 600;
  color: #fff;
  letter-spacing: 0.5px;
  border-bottom: 2px solid rgba(30, 144, 255, 0.4);
  position: sticky;
  top: 0;
  z-index: 1;
  font-size: 14px;
  text-transform: uppercase;
}

.deck-table-row {
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1px solid rgba(30, 144, 255, 0.15);
  position: relative;
  color: rgba(255, 255, 255, 0.85);
}

.deck-table-row:last-child {
  border-bottom: none;
}

.deck-table-row:hover {
  background: linear-gradient(90deg, rgba(30, 144, 255, 0.05), rgba(30, 144, 255, 0.1));
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.deck-table-row::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 0;
  background: linear-gradient(to bottom, #1e90ff, #00bfff);
  transition: width 0.3s ease;
}

.deck-table-row:hover::after {
  width: 4px;
}

.deck-table-col {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0 12px;
  line-height: 1.5;
}

.deck-table-col.env {
  width: 12%;
  color: rgba(255, 255, 255, 0.7);
  font-style: italic;
}

.deck-table-col.title {
  width: 20%;
  font-weight: 600;
  color: #1e90ff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.deck-table-col.title:hover {
  color: #00bfff;
  text-decoration: underline;
  transform: scale(1.02);
}

.deck-table-col.desc {
  width: 38%;
  color: rgba(255, 255, 255, 0.8);
}

.deck-table-col.type {
  width: 10%;
  text-align: center;
}

.deck-table-col.author {
  width: 10%;
  color: rgba(255, 255, 255, 0.7);
}

.deck-table-col.time {
  width: 10%;
  color: rgba(255, 255, 255, 0.6);
  font-size: 13px;
}

.type-badge {
  display: inline-block;
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-align: center;
  min-width: 50px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.type-badge.is-tournament {
  background: linear-gradient(135deg, rgba(30, 144, 255, 0.15), rgba(30, 144, 255, 0.3));
  color: #1e90ff;
  border: 1px solid rgba(30, 144, 255, 0.4);
}

.type-badge.is-general {
  background: linear-gradient(135deg, rgba(100, 100, 100, 0.15), rgba(100, 100, 100, 0.3));
  color: #ccc;
  border: 1px solid rgba(150, 150, 150, 0.4);
}

.pagination-container {
  margin-top: 24px;
  display: flex;
  justify-content: center;
  width: 100%;
}

/* 為元素加入渲染動畫 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.deck-table-row {
  animation: fadeIn 0.3s ease-out forwards;
  animation-delay: calc(0.05s * var(--index, 0));
  opacity: 0;
}

.deck-table-row:nth-child(1) { --index: 1; }
.deck-table-row:nth-child(2) { --index: 2; }
.deck-table-row:nth-child(3) { --index: 3; }
.deck-table-row:nth-child(4) { --index: 4; }
.deck-table-row:nth-child(5) { --index: 5; }
.deck-table-row:nth-child(6) { --index: 6; }
.deck-table-row:nth-child(7) { --index: 7; }
.deck-table-row:nth-child(8) { --index: 8; }
.deck-table-row:nth-child(9) { --index: 9; }
.deck-table-row:nth-child(10) { --index: 10; }
.deck-table-row:nth-child(n+11) { --index: 11; }

@media (max-width: 768px) {
  .deck-table-header,
  .deck-table-row {
    flex-direction: column;
    align-items: flex-start;
    padding: 16px;
  }

  .deck-table-col {
    width: 100% !important;
    margin-bottom: 8px;
    padding: 4px 0;
  }

  .deck-table-col.title {
    font-size: 18px;
    margin-bottom: 12px;
  }

  .deck-table-col.type {
    position: absolute;
    top: 16px;
    right: 16px;
  }
}
</style> 