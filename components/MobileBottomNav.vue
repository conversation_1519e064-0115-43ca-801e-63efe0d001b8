<template>
  <div class="mobile-bottom-nav">
    <!-- 二級選單背景遮罩 -->
    <div 
      v-if="showSubMenu"
      class="submenu-overlay"
      @click="closeSubMenu"
    ></div>

    <!-- 二級選單 -->
    <div 
      v-if="showSubMenu"
      class="submenu-container"
      :class="{ 'submenu-show': showSubMenu }"
    >
      <div class="submenu-content">
        <div class="submenu-header">
          <h3 class="submenu-title">{{ subMenuTitle }}</h3>
          <button @click="closeSubMenu" class="submenu-close">
            <component :is="X" :size="20" class="text-gray-400" />
          </button>
        </div>
        <div class="submenu-options">
          <button 
            v-for="option in subMenuOptions"
            :key="option.key"
            @click="selectSubOption(option)"
            class="submenu-option"
          >
            <component :is="option.icon" :size="20" class="text-[#1e90ff]" />
            <span>{{ option.label }}</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 底部導航欄 -->
    <nav class="bottom-nav">
      <button 
        v-for="item in navItems"
        :key="item.key"
        @click="handleNavClick(item)"
        class="nav-item"
        :class="{ 'active': isActive(item.key) }"
      >
        <component 
          :is="item.icon" 
          :size="20" 
          :class="isActive(item.key) ? 'text-[#1e90ff]' : 'text-gray-400'"
        />
        <span 
          class="nav-label"
          :class="isActive(item.key) ? 'text-[#1e90ff]' : 'text-gray-400'"
        >
          {{ item.label }}
        </span>
      </button>
    </nav>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useRoute } from 'vue-router';
import { useAuthStore } from '~/stores/auth';
import { Home, BookOpen, User, MessageCircle, Layers, Users, BookOpen as Experience, Activity, Award, Plus, X } from 'lucide-vue-next';

const route = useRoute();
const authStore = useAuthStore();
const emit = defineEmits(['navigate', 'login']);

// 主要導航項目
const navItems = [
  {
    key: 'home',
    label: '首頁',
    icon: Home,
    route: '/'
  },
  {
    key: 'series',
    label: '系列',
    icon: BookOpen,
    route: '/series'
  },
  {
    key: 'deck',
    label: '我的',
    icon: User,
    route: '/deck'
  },
  {
    key: 'social',
    label: '動態',
    icon: MessageCircle,
    hasSubMenu: true
  },
  {
    key: 'goodsgroup',
    label: '牌組',
    icon: Layers,
    hasSubMenu: true
  }
];

// 二級選單狀態
const showSubMenu = ref(false);
const currentSubMenu = ref('');

// 二級選單配置
const subMenuConfig = {
  social: {
    title: '動態',
    options: [
      {
        key: 'social-platform',
        label: '社交平台',
        icon: Users,
        route: '/social'
      },
      {
        key: 'experience',
        label: '心得文章',
        icon: Experience,
        route: '/experience'
      }
    ]
  },
  goodsgroup: {
    title: '牌組',
    options: [
      {
        key: 'goodsgroup-main',
        label: '牌組專區',
        icon: Activity,
        route: '/Goodsgroup'
      },
      {
        key: 'tierlist',
        label: '環境T表',
        icon: Award,
        route: '/tierlist'
      },
      {
        key: 'new-deck',
        label: '新建牌組',
        icon: Plus,
        route: '/Goodsgroup/add?group_id=0&is_identify=1&game_id=1'
      }
    ]
  }
};

// 計算當前二級選單
const subMenuTitle = computed(() => {
  return subMenuConfig[currentSubMenu.value]?.title || '';
});

const subMenuOptions = computed(() => {
  return subMenuConfig[currentSubMenu.value]?.options || [];
});

// 判斷是否為當前活躍項目
const isActive = (key) => {
  const path = route.path;
  
  switch (key) {
    case 'home':
      return path === '/';
    case 'series':
      return path.startsWith('/series');
    case 'deck':
      return path.startsWith('/deck');
    case 'social':
      return path.startsWith('/social') || path.startsWith('/experience');
    case 'goodsgroup':
      return path.startsWith('/Goodsgroup') || path.startsWith('/tierlist');
    default:
      return false;
  }
};

// 處理導航點擊
const handleNavClick = (item) => {
  if (item.hasSubMenu) {
    currentSubMenu.value = item.key;
    showSubMenu.value = true;
  } else {
    // 如果是「我的」頁面且用戶未登入，則觸發登入
    if (item.key === 'deck' && !authStore.isAuthenticated) {
      emit('login');
      return;
    }
    emit('navigate', item.route);
    navigateTo(item.route);
  }
};

// 選擇二級選單選項
const selectSubOption = (option) => {
  // 如果是新建牌組且用戶未登入，則觸發登入
  if (option.key === 'new-deck' && !authStore.isAuthenticated) {
    closeSubMenu();
    emit('login');
    return;
  }
  
  emit('navigate', option.route);
  navigateTo(option.route);
  closeSubMenu();
};

// 關閉二級選單
const closeSubMenu = () => {
  showSubMenu.value = false;
  currentSubMenu.value = '';
};
</script>

<style scoped>
.mobile-bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 50;
}

/* 底部導航欄 */
.bottom-nav {
  display: flex;
  background-color: #061224;
  border-top: 1px solid rgba(30, 144, 255, 0.15);
  padding: 0.5rem 0;
  box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1);
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.5rem 0.25rem;
  transition: all 0.2s ease;
  background: none;
  border: none;
  cursor: pointer;
}

.nav-item:hover {
  background-color: rgba(30, 144, 255, 0.1);
}

.nav-item.active {
  background-color: rgba(30, 144, 255, 0.15);
}

.nav-label {
  font-size: 0.75rem;
  margin-top: 0.25rem;
  font-weight: 500;
}

/* 二級選單遮罩 */
.submenu-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 45;
}

/* 二級選單容器 */
.submenu-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #061224;
  border-top: 1px solid rgba(30, 144, 255, 0.2);
  transform: translateY(100%);
  transition: transform 0.3s ease;
  z-index: 50;
}

.submenu-show {
  transform: translateY(0);
}

.submenu-content {
  padding: 1rem;
  max-height: 50vh;
  overflow-y: auto;
}

.submenu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(30, 144, 255, 0.15);
}

.submenu-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: white;
}

.submenu-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: background-color 0.2s ease;
}

.submenu-close:hover {
  background-color: rgba(30, 144, 255, 0.1);
}

.submenu-options {
  display: grid;
  gap: 0.75rem;
}

.submenu-option {
  display: flex;
  align-items: center;
  padding: 1rem;
  background-color: rgba(30, 144, 255, 0.1);
  border: 1px solid rgba(30, 144, 255, 0.2);
  border-radius: 0.5rem;
  color: white;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
}

.submenu-option:hover {
  background-color: rgba(30, 144, 255, 0.2);
  border-color: rgba(30, 144, 255, 0.4);
}

.submenu-option span {
  margin-left: 0.75rem;
}

/* 隱藏桌面版 */
@media (min-width: 768px) {
  .mobile-bottom-nav {
    display: none;
  }
}
</style>