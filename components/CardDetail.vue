<template>
    <div class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div class="bg-gray-800 rounded-lg max-w-2xl w-full p-4 relative">
        <button 
          class="absolute top-2 right-2 text-gray-400 hover:text-white"
          @click="$emit('close')"
        >
          <component :is="X" :size="24" />
        </button>
        
        <div class="flex flex-col md:flex-row">
          <div class="md:w-1/3 flex justify-center mb-4 md:mb-0">
            <img :src="card.image" :alt="card.name" class="rounded-lg w-full max-w-xs" />
          </div>
          
          <div class="md:w-2/3 md:pl-6">
            <h3 class="text-2xl font-bold mb-2">{{ card.name }}</h3>
            
            <div v-if="card.type === $t('card.monster')" class="space-y-2 mb-4">
              <p><span class="font-bold">{{ $t('card.type') }}:</span> {{ card.type }}</p>
              <p><span class="font-bold">{{ $t('card.attribute') }}:</span> {{ card.attribute }}</p>
              <p><span class="font-bold">{{ $t('card.level') }}:</span> {{ card.level }}</p>
              <p><span class="font-bold">{{ $t('card.atk') }}:</span> {{ card.atk }}</p>
              <p><span class="font-bold">{{ $t('card.def') }}:</span> {{ card.def }}</p>
            </div>
            <div v-else class="mb-4">
              <p><span class="font-bold">{{ $t('card.type') }}:</span> {{ card.type }}</p>
            </div>
            
            <div class="border-t border-gray-700 pt-4">
              <p><span class="font-bold">{{ $t('card.effect') }}:</span></p>
              <p class="mt-2">{{ card.desc }}</p>
            </div>
            
            <div class="mt-6 flex space-x-3">
              <button 
                class="bg-blue-600 px-4 py-2 rounded hover:bg-blue-700"
                @click="addToDeck"
              >
                {{ $t('card.addToDeck') }}
              </button>
              <button 
                class="bg-gray-700 px-4 py-2 rounded hover:bg-gray-600"
                @click="$emit('close')"
              >
                {{ $t('common.close') }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { X } from 'lucide-vue-next';
  
  const props = defineProps({
    card: Object
  });
  
  const emit = defineEmits(['close', 'add-to-deck']);
  
  const addToDeck = () => {
    emit('add-to-deck', props.card);
    emit('close');
  };
  </script>