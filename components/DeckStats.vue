<template>
    <div class="flex flex-wrap gap-2 mb-6">
      <span class="bg-gray-700 px-3 py-1 rounded">
        {{ $t('card.monster') }}: {{ monsterCount }}
      </span>
      <span class="bg-gray-700 px-3 py-1 rounded">
        {{ $t('card.spell') }}: {{ spellCount }}
      </span>
      <span class="bg-gray-700 px-3 py-1 rounded">
        {{ $t('card.trap') }}: {{ trapCount }}
      </span>
      <span class="bg-gray-700 px-3 py-1 rounded">
        {{ $t('deck.total') }}: {{ cards.length }}/40
      </span>
    </div>
  </template>
  
  <script setup>
  import { computed } from 'vue';
  
  const props = defineProps({
    cards: Array
  });
  
  const monsterCount = computed(() => {
    return props.cards.filter(c => c.type === '怪獸' || c.type === '怪兽' || c.type === 'Monster').length;
  });
  
  const spellCount = computed(() => {
    return props.cards.filter(c => c.type === '魔法' || c.type === 'Spell').length;
  });
  
  const trapCount = computed(() => {
    return props.cards.filter(c => c.type === '陷阱' || c.type === 'Trap').length;
  });
  </script>