<template>
  <div v-if="modelValue" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-center justify-center min-h-screen p-4 text-center sm:p-0">
      <!-- 背景遮罩 -->
      <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity" aria-hidden="true" @click="close"></div>

      <!-- 對話框主體 -->
      <div class="relative bg-white rounded-lg w-full max-w-md p-6 overflow-hidden shadow-xl transform transition-all">
        <!-- 關閉按鈕 -->
        <button @click="close" class="absolute top-4 right-4 text-gray-400 hover:text-gray-500">
          <span class="sr-only">{{ $t("common.close") }}</span>
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        <!-- 標籤切換（僅在登入或註冊時顯示） -->
        <div v-if="activeTab !== 'forgot' && activeTab !== 'googleRegister'" class="flex border-b border-gray-200 mb-6">
          <button @click="activeTab = 'login'" class="flex-1 py-3 text-center relative" :class="{
            'text-orange-500': activeTab === 'login',
            'text-gray-500 hover:text-gray-700': activeTab !== 'login',
          }">
            {{ $t("auth.login") }}
            <div class="absolute bottom-0 left-0 w-full h-0.5 bg-orange-500 transform transition-transform duration-200"
              :class="{ 'scale-x-100': activeTab === 'login', 'scale-x-0': activeTab !== 'login' }"></div>
          </button>
          <button @click="activeTab = 'register'" class="flex-1 py-3 text-center relative" :class="{
            'text-orange-500': activeTab === 'register',
            'text-gray-500 hover:text-gray-700': activeTab !== 'register',
          }">
            {{ $t("auth.register") }}
            <div class="absolute bottom-0 left-0 w-full h-0.5 bg-orange-500 transform transition-transform duration-200"
              :class="{ 'scale-x-100': activeTab === 'register', 'scale-x-0': activeTab !== 'register' }"></div>
          </button>
        </div>

        <!-- 登入表單 -->
        <form v-if="activeTab === 'login'" @submit.prevent="handleLogin" class="space-y-4">
          <!-- 電子郵件輸入框 -->
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 mb-1">{{ $t("auth.email") }}</label>
            <input id="email" v-model="loginForm.email" type="email" required
              class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              :class="{ 'border-red-500': loginErrors.email }" />
            <p v-if="loginErrors.email" class="mt-1 text-sm text-red-500">{{ loginErrors.email }}</p>
          </div>

          <!-- 密碼輸入框 -->
          <div>
            <label for="password" class="block text-sm font-medium text-gray-700 mb-1">{{ $t("auth.password") }}</label>
            <div class="relative">
              <input id="password" v-model="loginForm.password" :type="showLoginPassword ? 'text' : 'password'" required
                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                :class="{ 'border-red-500': loginErrors.password }" />
              <button type="button" @click="showLoginPassword = !showLoginPassword"
                class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <svg class="h-5 w-5 text-gray-400" :class="{ 'text-orange-500': showLoginPassword }" fill="none"
                  viewBox="0 0 24 24" stroke="currentColor">
                  <path v-if="showLoginPassword" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path v-if="showLoginPassword" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                </svg>
              </button>
            </div>
            <p v-if="loginErrors.password" class="mt-1 text-sm text-red-500">{{ loginErrors.password }}</p>
          </div>

          <!-- 記住我選項與忘記密碼 -->
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <input id="remember" v-model="loginForm.remember" type="checkbox"
                class="h-4 w-4 text-orange-500 focus:ring-orange-500 border-gray-300 rounded" />
              <label for="remember" class="ml-2 block text-sm text-gray-700">{{ $t("auth.remember") }}</label>
            </div>
            <button type="button" class="text-sm text-orange-500 hover:text-orange-600" @click="handleForgotPassword">
              {{ $t("auth.forgotPassword") }}
            </button>
          </div>

          <!-- 登入按鈕 -->
          <button type="submit"
            class="w-full py-2 px-4 border border-transparent rounded-lg shadow-sm text-white bg-orange-500 hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
            :disabled="loading">
            <span v-if="loading" class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" />
                <path class="opacity-75" fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
              </svg>
              {{ $t("auth.loggingIn") }}
            </span>
            <span v-else>{{ $t("auth.login") }}</span>
          </button>

          <!-- 第三方登入選項 -->
          <div class="mt-6">
            <p class="text-center text-sm text-gray-500 mb-4">{{ $t("auth.orLoginWith") }}</p>
            <div class="flex justify-center space-x-4">
              <!-- Google 登入按鈕 -->
              <button type="button" @click="handleGoogleLogin"
                class="flex items-center px-4 py-2 bg-white text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-red-500">
                <img src="/icons/icLoginGoogle.png" alt="Google登入" class="h-5 w-5 mr-2" />
                {{ $t("auth.loginWithGoogle") }}
              </button>
            </div>
          </div>
        </form>

        <!-- 註冊表單 -->
        <form v-else-if="activeTab === 'register'" @submit.prevent="handleRegister" class="space-y-4">
          <!-- 電子郵件輸入框 -->
          <div>
            <label for="register-email" class="block text-sm font-medium text-gray-700 mb-1">{{ $t("auth.email")
              }}</label>
            <input id="register-email" v-model="registerForm.email" type="email" required
              class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              :class="{ 'border-red-500': registerErrors.email }" />
            <p v-if="registerErrors.email" class="mt-1 text-sm text-red-500">{{ registerErrors.email }}</p>
          </div>

          <!-- 密碼輸入框 -->
          <div>
            <label for="register-password" class="block text-sm font-medium text-gray-700 mb-1">{{ $t("auth.password")
              }}</label>
            <div class="relative">
              <input id="register-password" v-model="registerForm.password"
                :type="showRegisterPassword ? 'text' : 'password'" required
                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                :class="{ 'border-red-500': registerErrors.password }" />
              <button type="button" @click="showRegisterPassword = !showRegisterPassword"
                class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <svg class="h-5 w-5 text-gray-400" :class="{ 'text-orange-500': showRegisterPassword }" fill="none"
                  viewBox="0 0 24 24" stroke="currentColor">
                  <path v-if="showRegisterPassword" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path v-if="showRegisterPassword" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                </svg>
              </button>
            </div>
            <p v-if="registerErrors.password" class="mt-1 text-sm text-red-500">{{ registerErrors.password }}</p>
          </div>

          <!-- 確認密碼輸入框 -->
          <div>
            <label for="confirm-password" class="block text-sm font-medium text-gray-700 mb-1">{{
              $t("auth.confirmPassword") }}</label>
            <div class="relative">
              <input id="confirm-password" v-model="registerForm.confirmPassword"
                :type="showRegisterPassword ? 'text' : 'password'" required
                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                :class="{ 'border-red-500': registerErrors.confirmPassword }" />
              <button type="button" @click="showRegisterPassword = !showRegisterPassword"
                class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <svg class="h-5 w-5 text-gray-400" :class="{ 'text-orange-500': showRegisterPassword }" fill="none"
                  viewBox="0 0 24 24" stroke="currentColor">
                  <path v-if="showRegisterPassword" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path v-if="showRegisterPassword" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                </svg>
              </button>
            </div>
            <p v-if="registerErrors.confirmPassword" class="mt-1 text-sm text-red-500">{{ registerErrors.confirmPassword
              }}</p>
          </div>

          <!-- 暱稱輸入框 -->
          <div>
            <label for="nickname" class="block text-sm font-medium text-gray-700 mb-1">{{ $t("auth.nickname") }}</label>
            <input id="nickname" v-model="registerForm.nickname" type="text" required
              class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              :class="{ 'border-red-500': registerErrors.nickname }" />
            <p v-if="registerErrors.nickname" class="mt-1 text-sm text-red-500">{{ registerErrors.nickname }}</p>
          </div>

          <!-- 海外用戶按鈕 -->
          <div>
            <button type="button" @click="toggleOverseas"
              class="w-full py-2 px-4 border border-orange-500 rounded-lg text-orange-500 hover:bg-orange-50 focus:outline-none focus:ring-2 focus:ring-orange-500">
              {{ isOverseas ? $t("auth.switchLocal") : $t("auth.switchOverseas") }}
            </button>
          </div>

          <!-- 手機號碼輸入框與國碼選擇 -->
          <div>
            <label for="tel" class="block text-sm font-medium text-gray-700 mb-1">{{ $t("auth.tel") }}</label>
            <div class="flex space-x-2">
              <select v-if="isOverseas" v-model="registerForm.country" @change="updateCountryCode"
                class="w-1/3 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                <option value="" disabled>選擇國家</option>
                <option value="日本">日本 (+81)</option>
                <option value="中國">中國 (+86)</option>
                <option value="香港">香港 (+852)</option>
                <option value="澳門">澳門 (+853)</option>
                <option value="馬來西亞">馬來西亞 (+60)</option>
                <option value="新加坡">新加坡 (+65)</option>
                <option value="印尼">印尼 (+62)</option>
                <option value="菲律賓">菲律賓 (+63)</option>
                <option value="越南">越南 (+84)</option>
              </select>
              <input id="tel" v-model="registerForm.tel" type="tel" required
                :placeholder="isOverseas ? '請輸入手機號碼' : '請輸入10位手機號碼'"
                class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                :class="{ 'border-red-500': registerErrors.tel }" />
            </div>
            <p v-if="registerErrors.tel" class="mt-1 text-sm text-red-500">{{ registerErrors.tel }}</p>
            <p class="mt-1 text-sm text-gray-500">{{ isOverseas ? $t("auth.confirmTelOverseas") : $t("auth.confirmTel")
              }}</p>
          </div>

          <!-- 生日輸入框 -->
          <div>
            <label for="birthday" class="block text-sm font-medium text-gray-700 mb-1">{{ $t("auth.birthday") }}</label>
            <input id="birthday" v-model="registerForm.birthday" type="date"
              class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              :class="{ 'border-red-500': registerErrors.birthday }" />
            <p v-if="registerErrors.birthday" class="mt-1 text-sm text-red-500">{{ registerErrors.birthday }}</p>
            <p class="mt-1 text-sm text-red-500">{{ $t("auth.ifNot") }}</p>
          </div>

          <!-- 驗證碼輸入框與按鈕 -->
          <div class="flex space-x-2">
            <div class="flex-1">
              <label for="verification-code" class="block text-sm font-medium text-gray-700 mb-1">{{
                $t("auth.verificationCode") }}</label>
              <input id="verification-code" v-model="registerForm.code" type="text" required
                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                :class="{ 'border-red-500': registerErrors.code }" />
              <p v-if="registerErrors.code" class="mt-1 text-sm text-red-500">{{ registerErrors.code }}</p>
            </div>
            <div class="flex items-end">
              <button type="button" @click="getVerificationCode" :disabled="codeCountdown > 0 && codeCountdown < 60"
                class="py-2 px-4 bg-orange-500 text-white rounded-lg hover:bg-orange-600 disabled:bg-gray-400">
                {{ codeCountdown === 0 || codeCountdown === 60 ? $t("auth.getCode") : `${codeCountdown}s` }}
              </button>
            </div>
          </div>

          <!-- 同意條款 -->
          <div class="flex items-center">
            <input id="agree" v-model="registerForm.agree" type="checkbox"
              class="h-4 w-4 text-orange-500 focus:ring-orange-500 border-gray-300 rounded" />
            <label for="agree" class="ml-2 block text-sm text-gray-700">
              {{ $t("auth.agree") }}
              <a href="#" class="text-orange-500 hover:underline" @click.prevent="showPolicy">{{ $t("auth.policy")
                }}</a>
            </label>
          </div>

          <!-- 註冊按鈕 -->
          <button type="submit"
            class="w-full py-2 px-4 border border-transparent rounded-lg shadow-sm text-white bg-orange-500 hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
            :disabled="loading">
            <span v-if="loading" class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" />
                <path class="opacity-75" fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
              </svg>
              註冊中，請稍候
            </span>
            <span v-else>註冊</span>
          </button>
        </form>

        <!-- 忘記密碼表單 -->
        <form v-else-if="activeTab === 'forgot'" @submit.prevent="handleForgotPasswordSubmit" class="space-y-4">
          <!-- 電子郵件輸入框 -->
          <div>
            <label for="forgot-email" class="block text-sm font-medium text-gray-700 mb-1">{{ $t("auth.email")
              }}</label>
            <input id="forgot-email" v-model="forgotForm.email" type="email" required
              class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              :class="{ 'border-red-500': forgotErrors.email }" />
            <p v-if="forgotErrors.email" class="mt-1 text-sm text-red-500">{{ forgotErrors.email }}</p>
          </div>

          <!-- 驗證碼輸入框與按鈕 -->
          <div class="flex space-x-2">
            <div class="flex-1">
              <label for="forgot-code" class="block text-sm font-medium text-gray-700 mb-1">{{
                $t("auth.verificationCode") }}</label>
              <input id="forgot-code" v-model="forgotForm.code" type="text" required
                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                :class="{ 'border-red-500': forgotErrors.code }" />
              <p v-if="forgotErrors.code" class="mt-1 text-sm text-red-500">{{ forgotErrors.code }}</p>
            </div>
            <div class="flex items-end">
              <button type="button" @click="getForgotVerificationCode"
                :disabled="forgotCodeCountdown > 0 && forgotCodeCountdown < 60"
                class="py-2 px-4 bg-orange-500 text-white rounded-lg hover:bg-orange-600 disabled:bg-gray-400">
                {{ forgotCodeCountdown === 0 || forgotCodeCountdown === 60 ? $t("auth.getCode") :
                  `${forgotCodeCountdown}秒` }}
              </button>
            </div>
          </div>

          <!-- 新密碼輸入框 -->
          <div>
            <label for="forgot-password" class="block text-sm font-medium text-gray-700 mb-1">{{ $t("請輸入新密碼") }}</label>
            <div class="relative">
              <input id="forgot-password" v-model="forgotForm.password" :type="showForgotPassword ? 'text' : 'password'"
                required
                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                :class="{ 'border-red-500': forgotErrors.password }" />
              <button type="button" @click="showForgotPassword = !showForgotPassword"
                class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <svg class="h-5 w-5 text-gray-400" :class="{ 'text-orange-500': showForgotPassword }" fill="none"
                  viewBox="0 0 24 24" stroke="currentColor">
                  <path v-if="showForgotPassword" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path v-if="showForgotPassword" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                </svg>
              </button>
            </div>
            <p v-if="forgotErrors.password" class="mt-1 text-sm text-red-500">{{ forgotErrors.password }}</p>
          </div>

          <!-- 確認新密碼輸入框 -->
          <div>
            <label for="forgot-confirm-password" class="block text-sm font-medium text-gray-700 mb-1">{{
              $t("auth.confirmPassword") }}</label>
            <div class="relative">
              <input id="forgot-confirm-password" v-model="forgotForm.confirmPassword"
                :type="showForgotPassword ? 'text' : 'password'" required
                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                :class="{ 'border-red-500': forgotErrors.confirmPassword }" />
              <button type="button" @click="showForgotPassword = !showForgotPassword"
                class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <svg class="h-5 w-5 text-gray-400" :class="{ 'text-orange-500': showForgotPassword }" fill="none"
                  viewBox="0 0 24 24" stroke="currentColor">
                  <path v-if="showForgotPassword" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path v-if="showForgotPassword" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                </svg>
              </button>
            </div>
            <p v-if="forgotErrors.confirmPassword" class="mt-1 text-sm text-red-500">{{ forgotErrors.confirmPassword }}
            </p>
          </div>

          <!-- 提交按鈕 -->
          <button type="submit"
            class="w-full py-2 px-4 border border-transparent rounded-lg shadow-sm text-white bg-orange-500 hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
            :disabled="loading">
            <span v-if="loading" class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" />
                <path class="opacity-75" fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
              </svg>
              {{ $t("請稍後") }}
            </span>
            <span v-else>{{ $t("確認送出") }}</span>
          </button>

          <!-- 返回登入鏈接 -->
          <div class="text-center">
            <button type="button" class="text-sm text-orange-500 hover:text-orange-600" @click="activeTab = 'login'">
              {{ $t("返回") }}
            </button>
          </div>
        </form>

        <!-- Google 新用戶註冊表單 -->
        <form v-else-if="activeTab === 'googleRegister'" @submit.prevent="handleGoogleRegister" class="space-y-4">
          <!-- 提示 -->
          <div class="text-center mb-4">
            <p class="text-lg font-medium text-gray-700">{{ $t("auth.welcomeGoogleUser") }}</p>
            <p class="text-sm text-gray-500">{{ $t("auth.completeGoogleRegistration") }}</p>
          </div>

          <!-- 電子郵件（只讀） -->
          <div>
            <label for="google-email" class="block text-sm font-medium text-gray-700 mb-1">{{ $t("auth.email")
              }}</label>
            <input id="google-email" v-model="googleRegisterForm.email" type="email" readonly
              class="w-full px-4 py-2 border border-gray-300 rounded-lg bg-gray-100" />
          </div>

          <!-- 暱稱輸入框 -->
          <div>
            <label for="google-nickname" class="block text-sm font-medium text-gray-700 mb-1">{{ $t("auth.nickname")
              }}</label>
            <input id="google-nickname" v-model="googleRegisterForm.nickname" type="text" required
              class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              :class="{ 'border-red-500': googleRegisterErrors.nickname }" />
            <p v-if="googleRegisterErrors.nickname" class="mt-1 text-sm text-red-500">{{ googleRegisterErrors.nickname
              }}</p>
          </div>

          <!-- 海外用戶按鈕 -->
          <div>
            <button type="button" @click="toggleOverseas"
              class="w-full py-2 px-4 border border-orange-500 rounded-lg text-orange-500 hover:bg-orange-50 focus:outline-none focus:ring-2 focus:ring-orange-500">
              {{ isOverseas ? $t("auth.switchLocal") : $t("auth.switchOverseas") }}
            </button>
          </div>

          <!-- 手機號碼輸入框與國碼選擇 -->
          <div>
            <label for="google-tel" class="block text-sm font-medium text-gray-700 mb-1">{{ $t("auth.tel") }}</label>
            <div class="flex space-x-2">
              <select v-if="isOverseas" v-model="googleRegisterForm.country" @change="updateCountryCode"
                class="w-1/3 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                <option value="" disabled>選擇國家</option>
                <option value="日本">日本 (+81)</option>
                <option value="中國">中國 (+86)</option>
                <option value="香港">香港 (+852)</option>
                <option value="澳門">澳門 (+853)</option>
                <option value="馬來西亞">馬來西亞 (+60)</option>
                <option value="新加坡">新加坡 (+65)</option>
                <option value="印尼">印尼 (+62)</option>
                <option value="菲律賓">菲律賓 (+63)</option>
                <option value="越南">越南 (+84)</option>
              </select>
              <input id="google-tel" v-model="googleRegisterForm.tel" type="tel" required
                :placeholder="isOverseas ? '請輸入手機號碼' : '請輸入10位手機號碼'"
                class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                :class="{ 'border-red-500': googleRegisterErrors.tel }" />
            </div>
            <p v-if="googleRegisterErrors.tel" class="mt-1 text-sm text-red-500">{{ googleRegisterErrors.tel }}</p>
            <p class="mt-1 text-sm text-gray-500">{{ isOverseas ? $t("auth.confirmTelOverseas") : $t("auth.confirmTel")
              }}</p>
          </div>

          <!-- 生日輸入框（可選） -->
          <div>
            <label for="google-birthday" class="block text-sm font-medium text-gray-700 mb-1">{{ $t("auth.birthday")
              }}</label>
            <input id="google-birthday" v-model="googleRegisterForm.birthday" type="date"
              class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              :class="{ 'border-red-500': googleRegisterErrors.birthday }" />
            <p v-if="googleRegisterErrors.birthday" class="mt-1 text-sm text-red-500">{{ googleRegisterErrors.birthday
              }}</p>
            <p class="mt-1 text-sm text-red-500">{{ $t("auth.ifNot") }}</p>
          </div>

          <!-- 同意條款 -->
          <div class="flex items-center">
            <input id="google-agree" v-model="googleRegisterForm.agree" type="checkbox"
              class="h-4 w-4 text-orange-500 focus:ring-orange-500 border-gray-300 rounded" />
            <label for="google-agree" class="ml-2 block text-sm text-gray-700">
              {{ $t("auth.agree") }}
              <a href="#" class="text-orange-500 hover:underline" @click.prevent="showPolicy">{{ $t("auth.policy")
                }}</a>
            </label>
          </div>

          <!-- 提交按鈕 -->
          <button type="submit"
            class="w-full py-2 px-4 border border-transparent rounded-lg shadow-sm text-white bg-orange-500 hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
            :disabled="loading">
            <span v-if="loading" class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" />
                <path class="opacity-75" fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
              </svg>
              {{ $t("auth.registering") }}
            </span>
            <span v-else>{{ $t("auth.completeRegistration") }}</span>
          </button>
        </form>

        <!-- 錯誤提示 -->
        <div v-if="error" class="mt-4 p-3 rounded-lg bg-red-50 text-red-500 text-sm">{{ error }}</div>
      </div>
    </div>
  </div>

  <!-- 隱私政策彈窗 -->
  <el-dialog v-model="showPrivacyDialog" title="隱私政策與服務條款" width="60%" :before-close="handleClosePrivacyDialog">
    <div class="privacy-content max-h-96 overflow-y-auto" v-html="privacyContent"></div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="showPrivacyDialog = false">{{ $t("common.close") }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { ElLoading, ElMessage } from "element-plus";
import { useAuthStore } from "~/stores/auth";
import privacyText from "~/assets/terms_privacy_content.txt?raw";

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(["close", "login-success", "update:modelValue"]);
const authStore = useAuthStore();

// 狀態管理
const activeTab = ref("login");
const loading = ref(false);
const error = ref("");
const showLoginPassword = ref(false);
const showRegisterPassword = ref(false);
const codeCountdown = ref(60);
const showPrivacyDialog = ref(false);
const privacyContent = ref("");
let countdownTimer = null;
const showForgotPassword = ref(false);
const forgotCodeCountdown = ref(60);
let forgotCountdownTimer = null;
const isOverseas = ref(false);
const googleCredential = ref("");

// 表單數據
const loginForm = ref({
  email: "",
  password: "",
  remember: false,
});

const registerForm = ref({
  email: "",
  code: "",
  password: "",
  confirmPassword: "",
  nickname: "",
  tel: "",
  birthday: "",
  agree: false,
  key: "",
  country: "",
});

const forgotForm = ref({
  email: "",
  code: "",
  password: "",
  confirmPassword: "",
  key: "",
});

const googleRegisterForm = ref({
  email: "",
  notificationEmail: "",
  nickname: "",
  tel: "",
  birthday: "",
  agree: false,
  country: "",
});

// 表單錯誤
const loginErrors = ref({});
const registerErrors = ref({});
const forgotErrors = ref({});
const googleRegisterErrors = ref({});

// 初始化第三方登入 SDK
onMounted(() => {

  // 初始化 Google SDK
  const script = document.createElement("script");
  script.src = "https://accounts.google.com/gsi/client";
  script.async = true;
  script.defer = true;
  script.onload = () => {
    const config = useRuntimeConfig();
    google.accounts.id.initialize({
      client_id: config.public.googleClientId || import.meta.env.VITE_GOOGLE_CLIENT_ID,
      callback: handleGoogleCallback,
    });
  };
  document.head.appendChild(script);

  // 自動填入本地記憶的帳密
  const savedEmail = localStorage.getItem("savedEmail");
  const savedPassword = localStorage.getItem("savedPassword");
  if (savedEmail && savedPassword) {
    loginForm.value.email = savedEmail;
    loginForm.value.password = savedPassword;
    loginForm.value.remember = true;
  }

  // 隱私內容
  privacyContent.value = privacyText.replace(/\n/g, "<br>");

  // 檢查是否有登入錯誤
  const urlParams = new URLSearchParams(window.location.search);
  const errorMsg = urlParams.get('error');
  if (errorMsg) {
    error.value = decodeURIComponent(errorMsg);
    // 清理 URL 參數
    window.history.replaceState({}, document.title, window.location.pathname);
  }
});

// 驗證登入表單
const validateLoginForm = () => {
  const errors = {};
  if (!loginForm.value.email) errors.email = "請輸入電子郵件";
  else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(loginForm.value.email))
    errors.email = "請輸入有效的電子郵件地址";
  if (!loginForm.value.password) errors.password = "請輸入密碼";
  else if (loginForm.value.password.length < 6)
    errors.password = "密碼長度必須大於 6 個字符";
  loginErrors.value = errors;
  return Object.keys(errors).length === 0;
};

// 驗證註冊表單
const validateRegisterForm = () => {
  const errors = {};
  if (!registerForm.value.email) errors.email = "請輸入電子郵件";
  else if (!/^[A-Za-z0-9._@+]+$/.test(registerForm.value.email))
    errors.email = "Email 僅能包含字母、數字、@ 和 .";
  if (!registerForm.value.code) errors.code = "請輸入驗證碼";
  if (!registerForm.value.password) errors.password = "請輸入密碼";
  else if (!/^[a-zA-Z0-9]{6,12}$/.test(registerForm.value.password))
    errors.password = "密碼必須為6到12個字符，僅允許英文和數字";
  if (!registerForm.value.confirmPassword)
    errors.confirmPassword = "請確認密碼";
  else if (registerForm.value.password !== registerForm.value.confirmPassword)
    errors.confirmPassword = "密碼輸入不一致";
  if (!registerForm.value.nickname) errors.nickname = "請輸入暱稱";
  else if (!/^[\u4e00-\u9fa5a-zA-Z0-9]{1,20}$/.test(registerForm.value.nickname))
    errors.nickname = "暱稱只能包含20個字內的中文、英文或數字";
  if (!registerForm.value.tel) errors.tel = "請輸入手機號碼";
  else if (isOverseas.value) {
    if (!/^[0-9]{6,15}$/.test(registerForm.value.tel))
      errors.tel = "海外手機號碼必須為6到15位數字";
    if (!registerForm.value.country) errors.tel = "請選擇國家";
  } else {
    if (!/^[0-9]{10}$/.test(registerForm.value.tel))
      errors.tel = "請輸入有效的10位手機號碼";
  }
  if (!registerForm.value.agree) errors.agree = "請閱讀並同意相關政策";
  registerErrors.value = errors;
  return Object.keys(errors).length === 0;
};

// 驗證忘記密碼表單
const validateForgotForm = () => {
  const errors = {};
  if (!forgotForm.value.email) errors.email = "請輸入電子郵件";
  else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(forgotForm.value.email))
    errors.email = "請輸入有效的電子郵件地址";
  if (!forgotForm.value.code) errors.code = "請輸入驗證碼";
  if (!forgotForm.value.password) errors.password = "請輸入密碼";
  else if (!/^[a-zA-Z0-9]{6,12}$/.test(forgotForm.value.password))
    errors.password = "密碼格式錯誤";
  if (!forgotForm.value.confirmPassword) errors.confirmPassword = "請確認密碼";
  else if (forgotForm.value.password !== forgotForm.value.confirmPassword)
    errors.confirmPassword = "密碼輸入不一致";
  forgotErrors.value = errors;
  return Object.keys(errors).length === 0;
};

// 驗證 Google 註冊表單
const validateGoogleRegisterForm = () => {
  const errors = {};
  if (!googleRegisterForm.value.nickname) errors.nickname = "請輸入暱稱";
  else if (!/^[\u4e00-\u9fa5a-zA-Z0-9]{1,20}$/.test(googleRegisterForm.value.nickname))
    errors.nickname = "暱稱只能包含20個字內的中文、英文或數字";
  if (!googleRegisterForm.value.tel) errors.tel = "請輸入手機號碼";
  else if (isOverseas.value) {
    if (!/^[0-9]{6,15}$/.test(googleRegisterForm.value.tel))
      errors.tel = "海外手機號碼必須為6到15位數字";
    if (!googleRegisterForm.value.country) errors.tel = "請選擇國家";
  } else {
    if (!/^[0-9]{10}$/.test(googleRegisterForm.value.tel))
      errors.tel = "請輸入有效的10位手機號碼";
  }
  if (!googleRegisterForm.value.agree) errors.agree = "請閱讀並同意相關政策";
  googleRegisterErrors.value = errors;
  return Object.keys(errors).length === 0;
};

// 處理登入
const handleLogin = async () => {
  if (!validateLoginForm()) return;

  try {
    loading.value = true;
    error.value = "";
    const response = await $fetch("/api/Login/login", {
      method: "POST",
      body: {
        email: loginForm.value.email,
        pwd: loginForm.value.password,
        type: "4"
      }
    });
    console.debug("Login response:", response);

    if (response.code === 200 && response.data) {
      const userData = {
        user_id: response.data.user_id,
        nickname: response.data.nickname,
        headimg: response.data.headimg,
        user_status: response.data.user_status,
        shop_id: response.data.shop_id,
        user_type: Number(response.data.user_type),
        shop_type: response.data.shop_type,
        token: response.data.token,
      };

      if (!userData.nickname || !userData.token) {
        throw new Error("登入數據不完整");
      }

      authStore.setAuth({
        token: userData.token,
        user: userData,
      });

      emit("login-success", userData);
      emit("close");
      emit("update:modelValue", false);
    } else {
      error.value = response.msg || "登入失敗，請檢查帳號密碼";
    }
  } catch (err) {
    console.error("Login error:", err);
    error.value = "登入時發生錯誤，請稍後再試";
  } finally {
    loading.value = false;
  }
};

// 獲取驗證碼
const getVerificationCode = async () => {
  if (!registerForm.value.email) {
    registerErrors.value.email = "請輸入電子郵件";
    return;
  }

  try {
    loading.value = true;
    error.value = "";
    const verifyResponse = await $fetch("/api/Login/getVerify", {
      method: "POST",
      body: { type: 3 }
    });
    registerForm.value.key = verifyResponse.data.key;
    await $fetch("/api/Login/sendEmailCode", {
      method: "POST",
      body: {
        key: registerForm.value.key,
        email: registerForm.value.email
      }
    });
    error.value = "驗證碼已發送";

    codeCountdown.value = 60;
    countdownTimer = setInterval(() => {
      codeCountdown.value--;
      if (codeCountdown.value <= 0) {
        clearInterval(countdownTimer);
      }
    }, 1000);
  } catch (err) {
    console.error("Get verification code error:", err);
    error.value = "獲取驗證碼失敗，請稍後再試";
  } finally {
    loading.value = false;
  }
};

// 切換海外用戶
const toggleOverseas = () => {
  isOverseas.value = !isOverseas.value;
  registerForm.value.tel = "";
  registerForm.value.country = "";
  googleRegisterForm.value.tel = "";
  googleRegisterForm.value.country = "";
};

// 更新國碼
const updateCountryCode = () => { };

// 處理註冊
const handleRegister = async () => {
  if (!validateRegisterForm()) return;

  let loadingInstance = null;

  try {
    loading.value = true;
    error.value = "";

    const payload = {
      key: registerForm.value.key,
      email: registerForm.value.email,
      code: registerForm.value.code,
      pwd: registerForm.value.password,
      tel: registerForm.value.tel,
      birthday: registerForm.value.birthday || "",
      nickname: registerForm.value.nickname,
      type: isOverseas.value ? 2 : 1,
      country: isOverseas.value ? registerForm.value.country : "",
    };

    loadingInstance = ElLoading.service({
      lock: true,
      text: "註冊中，請稍候...",
      background: "rgba(0, 0, 0, 0.7)",
    });

    const response = await $fetch("/api/Login/register", {
      method: "POST",
      body: payload
    });

    loadingInstance.close();

    if (response.code === 200) {
      ElMessage.success({
        message: "註冊成功，正在登入...",
        duration: 2000,
      });

      await new Promise((resolve) => setTimeout(resolve, 1000));

      try {
        const loginResponse = await $fetch("/api/Login/login", {
          method: "POST",
          body: {
            email: registerForm.value.email,
            pwd: registerForm.value.password,
            type: "4"
          }
        });

        if (loginResponse.code === 200 && loginResponse.data) {
          const userData = {
            user_id: loginResponse.data.user_id,
            nickname: loginResponse.data.nickname,
            headimg: loginResponse.data.headimg,
            user_status: loginResponse.data.user_status,
            shop_id: loginResponse.data.shop_id,
            user_type: Number(loginResponse.data.user_type),
            shop_type: loginResponse.data.shop_type,
            token: loginResponse.data.token,
          };

          authStore.setAuth({
            token: userData.token,
            user: userData,
          });

          ElMessage.success({
            message: "登入成功！歡迎 " + userData.nickname,
            duration: 2000,
          });

          emit("login-success", userData);
          emit("close");
          emit("update:modelValue", false);
        } else {
          ElMessage.warning({
            message: "註冊成功但自動登入失敗，請手動登入",
            duration: 3000,
          });
          activeTab.value = "login";
          loginForm.value.email = registerForm.value.email;
        }
      } catch (loginErr) {
        console.error("自動登入異常:", loginErr);
        ElMessage.warning({
          message: "註冊成功但自動登入異常，請手動登入",
          duration: 3000,
        });
        activeTab.value = "login";
        loginForm.value.email = registerForm.value.email;
      }
    } else {
      ElMessage.error({
        message: response.msg || "註冊失敗，請稍後再試",
        duration: 3000,
      });
      error.value = response.msg || "註冊失敗";
    }
  } catch (err) {
    console.error("註冊異常:", err);
    const errorMsg =
      err.response?.data?.msg || err.message || "註冊過程中出現錯誤，請稍後再試";
    ElMessage.error({
      message: errorMsg,
      duration: 3000,
    });
    error.value = errorMsg;
  } finally {
    if (loadingInstance) loadingInstance.close();
    loading.value = false;
  }
};

// 處理忘記密碼
const handleForgotPassword = () => {
  activeTab.value = "forgot";
  error.value = "";
  forgotForm.value = {
    email: loginForm.value.email || "",
    code: "",
    password: "",
    confirmPassword: "",
    key: "",
  };
};

// 獲取忘記密碼驗證碼
const getForgotVerificationCode = async () => {
  if (!forgotForm.value.email) {
    forgotErrors.value.email = "請輸入電子郵件";
    return;
  }

  try {
    loading.value = true;
    error.value = "";
    const verifyResponse = await $fetch("/api/Login/getVerify", {
      method: "POST",
      body: { type: 2 }
    });
    forgotForm.value.key = verifyResponse.data.key;
    await $fetch("/api/Login/sendEmailCode", {
      method: "POST",
      body: {
        key: forgotForm.value.key,
        email: forgotForm.value.email
      }
    });
    ElMessage.success("驗證碼已發送");

    forgotCodeCountdown.value = 60;
    forgotCountdownTimer = setInterval(() => {
      forgotCodeCountdown.value--;
      if (forgotCodeCountdown.value <= 0) {
        clearInterval(forgotCountdownTimer);
      }
    }, 1000);
  } catch (err) {
    console.error("Get forgot verification code error:", err);
    error.value = "獲取驗證碼失敗";
  } finally {
    loading.value = false;
  }
};

// 處理忘記密碼提交
const handleForgotPasswordSubmit = async () => {
  if (!validateForgotForm()) return;

  try {
    loading.value = true;
    error.value = "";

    const response = await $fetch("/api/Login/forgetPwd", {
      method: "POST",
      body: {
        password: forgotForm.value.password,
        key: forgotForm.value.key,
        code: forgotForm.value.code,
        email: forgotForm.value.email
      }
    });

    if (response.code === 200) {
      ElMessage.success("密碼重置成功");
      activeTab.value = "login";
      loginForm.value.email = forgotForm.value.email;
      loginForm.value.password = forgotForm.value.password;

      forgotForm.value = {
        email: "",
        code: "",
        password: "",
        confirmPassword: "",
        key: "",
      };
    } else {
      error.value = response.msg || "密碼重置失敗";
    }
  } catch (err) {
    console.error("Forgot password error:", err);
    error.value = "密碼重置時發生錯誤";
  } finally {
    loading.value = false;
  }
};

// 處理 Google 登入回調
const handleGoogleCallback = (response) => {
  if (!response || !response.credential) {
    error.value = "無法從 Google 取得登入憑證，請重試或使用其他登入方式";
    return;
  }

  // 從 credential 中解析出郵箱信息
  const payload = JSON.parse(atob(response.credential.split('.')[1]));
  const email = payload.email;

  processGoogleLogin(response.credential, email);
};

// 異步處理 Google 登入
const processGoogleLogin = async (credential, email) => {
  loading.value = true;
  error.value = "";
  googleCredential.value = credential;

  try {
    const response = await $fetch("/api/Login/googleLogin", {
      method: "POST",
      body: {
        credential,
        email
      }
    });

    if (response && response.code === 200 && response.data != null) {
      if (response.data.isNewUser) {
        activeTab.value = "googleRegister";
        googleRegisterForm.value.email = response.data.email || "";
        googleRegisterForm.value.notificationEmail = response.data.email || "";
        googleRegisterForm.value.nickname = response.data.name || "";
      } else {
        if (
          !response.data.user_id ||
          !response.data.nickname ||
          !response.data.token
        ) {
          throw new Error("後端返回的用戶數據不完整");
        }

        const userData = {
          user_id: response.data.user_id,
          nickname: response.data.nickname,
          headimg: response.data.headimg || "",
          user_status: response.data.user_status || 0,
          shop_id: response.data.shop_id || null,
          user_type: Number(response.data.user_type) || 0,
          shop_type: response.data.shop_type || "",
          token: response.data.token,
        };

        authStore.setAuth({
          token: userData.token,
          user: userData,
        });

        ElMessage.success("Google 登入成功！");
        emit("login-success", userData);
        emit("close");
        emit("update:modelValue", false);
      }
    } else {
      error.value = response?.msg || "Google 登入失敗";
    }
  } catch (err) {
    console.error("Google login error:", err);
    error.value = err.message || "Google 登入時發生錯誤";
  } finally {
    loading.value = false;
  }
};

// 處理 Google 註冊提交
const handleGoogleRegister = async () => {
  if (!validateGoogleRegisterForm()) return;

  try {
    loading.value = true;
    error.value = "";

    const registrationData = {
      email: googleRegisterForm.value.email,
      phone: googleRegisterForm.value.tel,
      birthday: googleRegisterForm.value.birthday || "",
      nickname: googleRegisterForm.value.nickname,
      credential: googleCredential.value,
      type: isOverseas.value ? 2 : 1
    };

    if (isOverseas.value && googleRegisterForm.value.country) {
      registrationData.country = googleRegisterForm.value.country;
    }

    const response = await $fetch("/api/Login/completeGoogleRegistration", {
      method: "POST",
      body: registrationData
    });

    if (response.code === 200 && response.data) {
      const userData = {
        user_id: response.data.user_id,
        nickname: response.data.nickname,
        headimg: response.data.headimg,
        user_status: response.data.user_status,
        shop_id: response.data.shop_id,
        user_type: Number(response.data.user_type),
        shop_type: response.data.shop_type,
        token: response.data.token,
      };

      authStore.setAuth({
        token: userData.token,
        user: userData,
      });

      ElMessage.success("註冊並登入成功！");
      emit("login-success", userData);
      emit("close");
      emit("update:modelValue", false);
    } else {
      error.value = response.msg || "完成註冊失敗";
    }
  } catch (err) {
    console.error("Complete Google Registration error:", err);
    error.value = "完成註冊時發生錯誤";
  } finally {
    loading.value = false;
  }
};

// 處理 Google 登入按鈕點擊
const handleGoogleLogin = () => {
  google.accounts.id.prompt();
};

// 顯示隱私政策
const showPolicy = () => {
  showPrivacyDialog.value = true;
};

// 關閉隱私政策彈窗
const handleClosePrivacyDialog = () => {
  showPrivacyDialog.value = false;
};

// 關閉對話框
const close = () => {
  if (countdownTimer) clearInterval(countdownTimer);
  if (forgotCountdownTimer) clearInterval(forgotCountdownTimer);
  error.value = "";
  loginErrors.value = {};
  registerErrors.value = {};
  forgotErrors.value = {};
  googleRegisterErrors.value = {};
  googleRegisterForm.value = {
    email: "",
    notificationEmail: "",
    nickname: "",
    tel: "",
    birthday: "",
    agree: false,
    country: "",
  };
  showPrivacyDialog.value = false;
  emit("close");
  emit("update:modelValue", false);
};
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

input[type="password"] {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

input[type="date"] {
  -webkit-appearance: none;
  appearance: none;
}

input[type="date"]::-webkit-calendar-picker-indicator {
  background: transparent;
  padding: 8px;
  cursor: pointer;
  position: absolute;
  right: 0;
  opacity: 0.5;
}

input[type="date"]::-webkit-calendar-picker-indicator:hover {
  opacity: 1;
}

.privacy-content {
  padding: 16px;
  line-height: 1.6;
}
</style>