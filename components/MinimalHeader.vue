<template>
  <div class="absolute top-0 right-0 w-full z-10 p-4 pointer-events-none">
    <div class="flex justify-end">
      <!-- 登入狀態/按鈕 - 這個元素需要可點擊，所以使用 pointer-events-auto -->
      <div class="pointer-events-auto">
        <div v-if="isLoggedIn" class="flex items-center">
          <div class="mr-2 text-sm text-gray-300">{{ username }}</div>
          <div class="w-8 h-8 bg-[rgba(30,144,255,0.15)] rounded-full overflow-hidden border border-[rgba(30,144,255,0.3)]">
            <img v-if="avatarUrl" :src="avatarUrl" alt="用戶頭像" class="w-full h-full object-cover" />
          </div>
          <div class="relative ml-1">
            <button class="p-1 hover:bg-[rgba(30,144,255,0.15)] rounded transition-all duration-300" @click="toggleUserMenu">
              <component :is="ChevronDown" :size="16" class="text-gray-400" />
            </button>

            <div v-if="showUserMenu"
              class="absolute right-0 mt-1 bg-[#0c2442] rounded shadow-lg shadow-[rgba(0,0,0,0.4)] z-20 w-40 border border-[rgba(30,144,255,0.2)]"
              @mouseleave="showUserMenu = false">
              <div class="px-4 py-2 cursor-pointer hover:bg-[rgba(30,144,255,0.15)] text-left transition-all duration-300"
                @click="handleUserAction('profile')">
                {{ $t('header.profile') }}
              </div>
              <div class="px-4 py-2 cursor-pointer hover:bg-[rgba(30,144,255,0.15)] text-left transition-all duration-300"
                @click="handleUserAction('settings')">
                {{ $t('header.settings') }}
              </div>
              <div
                class="px-4 py-2 cursor-pointer hover:bg-[rgba(30,144,255,0.15)] text-left border-t border-[rgba(30,144,255,0.2)] transition-all duration-300"
                @click="handleUserAction('logout')">
                {{ $t('header.logout') }}
              </div>
            </div>
          </div>
        </div>
        <button v-else
          class="px-4 py-2 bg-gradient-to-r from-[#1e90ff] to-[#00bfff] text-white rounded shadow-md shadow-[rgba(30,144,255,0.3)] hover:shadow-lg hover:shadow-[rgba(30,144,255,0.4)] hover:-translate-y-1 transition-all duration-300"
          @click="$emit('login')">
          {{ $t('header.login') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { ChevronDown } from 'lucide-vue-next';

const props = defineProps({
  isLoggedIn: {
    type: Boolean,
    default: false
  },
  username: {
    type: String,
    default: ''
  },
  avatarUrl: {
    type: String,
    default: null
  }
});

const emit = defineEmits(['login', 'logout', 'user-action']);

const showUserMenu = ref(false);

const toggleUserMenu = () => {
  showUserMenu.value = !showUserMenu.value;
};

const handleUserAction = (action) => {
  showUserMenu.value = false;

  if (action === 'logout') {
    emit('logout');
  } else {
    emit('user-action', action);
  }
};
</script> 