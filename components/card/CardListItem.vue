<template>
  <div class="card-list-item">
    <div class="card-col-image">
      <div class="card-image-container">
        <img :src="card.imageUrl" alt="卡片圖片">
        <div v-if="card.banType > 0" class="ban-indicator" :class="getBanTypeClass(card.banType)">{{ card.banType }}</div>
      </div>
    </div>
    <div class="card-col-name">{{ card.name }}</div>
    <div v-for="i in 4" :key="i" class="card-col-count">
      {{ i <= card.percentages.length ? formatPercentage(card.percentages[i-1]) : '-' }}
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  card: {
    type: Object,
    required: true
  }
});

const getBanTypeClass = (banType) => {
  switch(banType) {
    case 1: return 'banned';
    case 2: return 'limited-1';
    case 3: return 'limited-2';
    default: return '';
  }
};

const formatPercentage = (value) => {
  if (value === undefined || value === null) return '-';
  if (value === 0) return '0%';
  return Number(value).toFixed(1) + '%';
};
</script>

<style scoped>
.card-list-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid rgba(30, 144, 255, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative; 
}

.card-list-item:hover {
  background: rgba(30, 144, 255, 0.1);
}

.card-image-container {
  position: relative;
  width: 50px;
  height: 70px;
}

.card-image-container img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 4px;
}

.ban-indicator {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: white;
  border: 2px solid red;
  color: red;
  font-size: 12px;
  font-weight: bold;
  display: flex;
  justify-content: center;
  align-items: center;
  line-height: 1;
}

.ban-indicator.banned {
  background-color: white;
  border: 2px solid red;
  color: red;
}

.ban-indicator.limited-1 {
  background-color: white;
  border: 2px solid red;
  color: red;
}

.ban-indicator.limited-2 {
  background-color: white;
  border: 2px solid red;
  color: red;
}

.card-col-image {
  width: 60px;
  text-align: center;
}

.card-col-name {
  flex: 1;
  margin-left: 8px;
}

.card-col-count {
  width: 60px;
  text-align: center;
}
</style> 