<template>
  <div class="card-container trend-chart-card">
    <div class="chart-container">
      <div v-if="loading && !chartData" class="loading-container">
        <el-skeleton-item variant="circle" style="width: 100px; height: 100px" />
      </div>
      <div v-else-if="error" class="error-state">
        <i class="el-icon-warning" style="color: #ff4d4f; font-size: 32px;" />
        <p style="color: #ff4d4f;">{{ error }}</p>
      </div>
      <div v-else-if="!chartData" class="empty-state">
        <i class="el-icon-data-analysis" />
        <p>目前尚無趨勢數據</p>
      </div>
      <div v-else class="chart-wrapper" ref="chartContainer">
        <canvas ref="chartRef" style="width: 100%; height: 100%;" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed, nextTick, onUnmounted } from 'vue';
import Chart from 'chart.js/auto';

const props = defineProps({
  loading: Boolean,
  data: Object,
  error: String
});

const emit = defineEmits(['load']);
const chartRef = ref(null);
const chartContainer = ref(null);
let chartInstance = null;
let resizeObserver = null;

// 加入 chartData 計算屬性
const chartData = computed(() => props.data);

const renderChart = () => {
  if (!props.data || !chartRef.value) return;
  
  // 确保DOM已更新，再渲染图表
  nextTick(() => {
    try {
      // 清除之前的圖表實例
      if (chartInstance) {
        chartInstance.destroy();
        chartInstance = null;
      }

      const canvas = chartRef.value;
      const ctx = canvas.getContext('2d');
      
      // 確保canvas尺寸正確
      const container = chartContainer.value;
      if (container) {
        canvas.width = container.clientWidth;
        canvas.height = container.clientHeight;
      }

      const { labels, datasets } = props.data;

      chartInstance = new Chart(ctx, {
        type: 'line',
        data: {
          labels,
          datasets
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          layout: {
            padding: {
              right: 20
            }
          },
          scales: {
            x: {
              title: {
                display: true,
                text: '日期',
                color: '#FFFFFF'
              },
              ticks: {
                maxTicksLimit: 20,
                color: '#FFFFFF'
              },
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              }
            },
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: '使用率 (%)',
                color: '#FFFFFF'
              },
              ticks: {
                color: '#FFFFFF'
              },
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              }
            }
          },
          plugins: {
            legend: {
              display: true,
              position: 'top',
              labels: {
                color: '#FFFFFF',
                font: {
                  weight: 'bold',
                  size: window.innerWidth <= 480 ? 10 : 12
                },
                boxWidth: window.innerWidth <= 480 ? 12 : 20,
                boxHeight: window.innerWidth <= 480 ? 12 : 20,
                padding: window.innerWidth <= 480 ? 8 : 15
              }
            },
            tooltip: {
              mode: 'index',
              intersect: false,
              backgroundColor: 'rgba(0, 0, 0, 0.8)',
              titleColor: '#FFFFFF',
              bodyColor: '#FFFFFF',
              borderColor: 'rgba(255, 255, 255, 0.2)',
              borderWidth: 1
            }
          }
        }
      });
    } catch (error) {
      console.error('渲染圖表時發生錯誤:', error);
    }
  });
};

// 監聽數據變化
watch(() => props.data, (newData) => {
  if (newData) {
    renderChart();
  }
}, { deep: true });

// 監聽容器大小變化
const setupResizeObserver = () => {
  if (chartContainer.value && window.ResizeObserver) {
    resizeObserver = new ResizeObserver(() => {
      if (chartInstance) {
        renderChart();
      }
    });
    resizeObserver.observe(chartContainer.value);
  }
};

onMounted(() => {
  // 设置窗口大小改变监听
  setupResizeObserver();
  
  // 仅在数据不存在时才触发加载
  setTimeout(() => {
    if (!props.data && !props.loading && !props.error) {
      emit('load');
    }
  }, 200);
  
  // 如果已有數據但未渲染則渲染圖表
  setTimeout(() => {
    if (props.data && !chartInstance) {
      renderChart();
    }
  }, 500);
});

onUnmounted(() => {
  // 清理圖表和監聽器
  if (chartInstance) {
    chartInstance.destroy();
    chartInstance = null;
  }
  
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }
});
</script>

<style scoped>
.chart-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  min-height: 380px;
}

.chart-wrapper {
  width: 100%;
  height: 100%;
  min-height: 380px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 手機版圖表優化 */
@media (max-width: 480px) {
  .chart-container {
    min-height: 260px !important;
    height: 260px !important;
    padding: 5px !important;
  }

  .chart-wrapper {
    min-height: 220px !important;
    height: 220px !important;
    overflow: hidden !important;
  }
}

.error-state, .empty-state, .loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  min-height: 250px;
}
</style> 