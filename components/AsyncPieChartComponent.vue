<template>
  <div class="card-container pie-chart-card">
    <div class="chart-container">
      <div v-if="loading && !chartData" class="loading-container">
        <el-skeleton-item variant="circle" style="width: 100px; height: 100px" />
      </div>
      <div v-else-if="!chartData" class="empty-state">
        <i class="el-icon-pie-chart" />
        <p>目前尚無牌組數據</p>
      </div>
      <div v-else class="chart-wrapper" ref="chartContainer">
        <canvas ref="chartRef" style="width: 100%; height: 100%;" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed, nextTick, onUnmounted } from 'vue';
import Chart from 'chart.js/auto';

const props = defineProps({
  loading: Boolean,
  data: Object
});

const emit = defineEmits(['load']);
const chartRef = ref(null);
const chartContainer = ref(null);
let chartInstance = null;
let resizeObserver = null;

const chartData = computed(() => props.data);

// 常規色彩備選(當沒有圖片時使用)
const pieColors = [
  '#FF3B30', // 亮紅色
  '#5856D6', // 深紫色
  '#34C759', // 鮮綠色
  '#007AFF', // 亮藍色
  '#FF9500', // 橙色
  '#AF52DE'  // 紫色
];

// 為圓餅圖自定義繪製插件
const createPiecePlugin = (imageMap) => {
  return {
    id: 'pieceCustomization',
    beforeDraw: (chart) => {
      const ctx = chart.ctx;
      const meta = chart.getDatasetMeta(0);
      
      // 如果沒有圖片或元素，不執行繪製
      if (!meta || !meta.data || !imageMap) return;
      
      // 遍歷餅圖的每塊扇形
      meta.data.forEach((piece, index) => {
        const imageKey = chart.data.labels[index];
        const imageInfo = imageMap[imageKey];
        
        // 如果有可用的圖片信息
        if (imageInfo && imageInfo.image) {
          ctx.save();
          
          // 獲取扇形的中心點和半徑
          const center = {
            x: piece.x,
            y: piece.y
          };
          const radius = piece.outerRadius;
          
          // 獲取扇形起始和結束角度
          const startAngle = piece.startAngle;
          const endAngle = piece.endAngle;
          const midAngle = startAngle + (endAngle - startAngle) / 2;
          
          // 創建裁剪路徑 - 只在扇形內繪製
          ctx.beginPath();
          ctx.moveTo(center.x, center.y);
          ctx.arc(center.x, center.y, radius, startAngle, endAngle);
          ctx.closePath();
          ctx.clip();
          
          // 計算圖片繪製參數
          const imageSize = radius * 1.2; // 稍大一些以確保覆蓋整個扇區
          const imgX = center.x - imageSize / 2;
          const imgY = center.y - imageSize / 2;
          
          // 繪製圖片
          ctx.drawImage(imageInfo.image, imgX, imgY, imageSize, imageSize);
          
          // 加入半透明遮罩以確保文字可見
          ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
          ctx.fill();
          
          // 恢復畫布狀態
          ctx.restore();
        }
      });
    }
  };
};

// 載入圖片並保存引用
const loadImages = async (deckData) => {
  const imageMap = {};
  
  // 處理可能的數據格式
  const decks = deckData?.originalData || [];
  
  if (!Array.isArray(decks) || decks.length === 0) return imageMap;
  
  const loadImage = (url, deckName) => {
    return new Promise((resolve) => {
      if (!url) {
        resolve(null);
        return;
      }
      
      // 處理CORS問題 - 使用代理伺服器
      let imgSrc = url;
      if (url.includes('storage.googleapis.com')) {
        // 從URL中提取文件名
        const imageUrlParts = url.split('/');
        const filename = imageUrlParts[imageUrlParts.length - 1];
        
        // 使用本地代理路徑
        imgSrc = `/api/proxy/image/${filename}`;
      }
      
      const img = new Image();
      img.crossOrigin = 'Anonymous';
      
      img.onload = () => {
        imageMap[deckName] = { image: img, loaded: true };
        resolve(true);
      };
      
      img.onerror = () => {
        // console.warn(`無法載入圖片: ${url}`);
        imageMap[deckName] = { loaded: false };
        resolve(false);
      };
      
      img.src = imgSrc;
    });
  };
  
  // 優先載入前5個牌組的圖片
  const promises = [];
  const top5 = decks.slice(0, 5);
  
  for (const deck of top5) {
    const imageUrl = deck.photo || deck.image || deck.thumb || deck.pic || deck.picture;
    if (imageUrl) {
      promises.push(loadImage(imageUrl, deck.title));
    }
  }
  
  // 為"其他"類別加入默認圖片
  promises.push(loadImage('/images/deck-defaults/other-deck.jpg', '其他'));
  
  // 等待所有圖片載入完成
  await Promise.all(promises);
  
  return imageMap;
};

// 渲染圓餅圖
const renderChart = async () => {
  if (!props.data || !chartRef.value) return;
  
  // 确保DOM已更新，再渲染图表
  nextTick(async () => {
    try {
      // 清除之前的圖表實例
      if (chartInstance) {
        chartInstance.destroy();
        chartInstance = null;
      }

      const canvas = chartRef.value;
      const ctx = canvas.getContext('2d');
      
      // 確保canvas尺寸正確
      const container = chartContainer.value;
      if (container) {
        canvas.width = container.clientWidth;
        canvas.height = container.clientHeight;
      }

      // 載入牌組圖片
      const imageMap = await loadImages(props.data);
      
      const { labels, datasets } = props.data;
      
      // 註冊自定義插件
      const piecePlugin = createPiecePlugin(imageMap);
      Chart.register(piecePlugin);
      
      // 設置每個扇區的背景顏色（作為圖片載入失敗的備用）
      const backgroundColors = labels.map((label, i) => {
        // 如果沒有圖片或圖片載入失敗，使用預設顏色
        return pieColors[i % pieColors.length];
      });

      chartInstance = new Chart(ctx, {
        type: 'pie',
        data: {
          labels,
          datasets: [{
            data: datasets[0].data,
            backgroundColor: backgroundColors,
            borderWidth: 2,
            borderColor: 'rgba(255, 255, 255, 0.7)'
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          layout: {
            padding: window.innerWidth <= 480 ? 30 : 40
          },
          plugins: {
            legend: {
              position: 'top',
              labels: {
                color: '#FFFFFF',
                font: {
                  weight: 'bold',
                  size: window.innerWidth <= 480 ? 10 : 13
                },
                padding: window.innerWidth <= 480 ? 6 : 10,
                boxWidth: window.innerWidth <= 480 ? 12 : 20,
                boxHeight: window.innerWidth <= 480 ? 8 : 12
              }
            },
            tooltip: {
              callbacks: {
                label: (context) => {
                  const label = context.label || '';
                  const value = context.raw || 0;
                  const total = context.dataset.data.reduce((sum, val) => sum + val, 0);
                  const percentage = ((value / total) * 100).toFixed(1);
                  return `${label}: ${value} (${percentage}%)`;
                }
              },
              backgroundColor: 'rgba(0, 0, 0, 0.8)',
              bodyColor: '#FFFFFF',
              borderColor: 'rgba(255, 255, 255, 0.2)',
              borderWidth: 1
            }
          }
        }
      });
    } catch (error) {
      // console.error('渲染餅圖時發生錯誤:', error);
    }
  });
};

// 監聽數據變化
watch(() => props.data, (newData) => {
  if (newData) {
    renderChart();
  }
}, { deep: true });

// 監聽容器大小變化
const setupResizeObserver = () => {
  if (chartContainer.value && window.ResizeObserver) {
    resizeObserver = new ResizeObserver(() => {
      if (chartInstance) {
        renderChart();
      }
    });
    resizeObserver.observe(chartContainer.value);
  }
};

onMounted(() => {
  // 设置窗口大小改变监听
  setupResizeObserver();
  
  // 确保DOM已挂载后再处理数据
  nextTick(() => {
    // 仅在数据不存在时才触发加载
    if (!props.data && !props.loading) {
      emit('load');
    }
    
    // 如果已有數據則立即渲染圖表
    if (props.data && !chartInstance) {
      renderChart();
    }
  });
});

onUnmounted(() => {
  // 清理圖表和監聽器
  if (chartInstance) {
    chartInstance.destroy();
    chartInstance = null;
  }
  
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }
});
</script>

<style scoped>
.chart-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  min-height: 380px;
}

.chart-wrapper {
  width: 100%;
  height: 100%;
  min-height: 380px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 手機版圖表 */
@media (max-width: 480px) {
  .chart-container {
    min-height: 250px !important;
    height: 250px !important;
    padding: 5px !important;
  }

  .chart-wrapper {
    min-height: 250px !important;
    height: 250px !important;
    overflow: hidden !important;
  }
}

/* 平板版調整 */
@media (max-width: 768px) and (min-width: 481px) {
  .chart-container {
    min-height: 280px;
    height: 280px;
  }

  .chart-wrapper {
    min-height: 260px;
    height: 260px;
  }
}

.error-state, .empty-state, .loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  min-height: 250px;
}
</style> 