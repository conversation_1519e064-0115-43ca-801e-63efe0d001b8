<template>
    <div class="bg-gray-800 p-4 rounded-lg hover:bg-gray-700 transition">
      <div class="flex items-center mb-3">
        <div class="w-10 h-10 bg-gray-600 rounded-full mr-3"></div>
        <div>
          <div class="font-bold">{{ post.user }}</div>
          <div class="text-gray-400 text-sm">{{ post.time }}</div>
        </div>
      </div>
      <p class="mb-3">{{ post.content }}</p>
      <div class="flex space-x-4 text-sm">
        <button class="flex items-center text-gray-400 hover:text-red-500">
          <span class="mr-1">❤</span> {{ post.likes }}
        </button>
        <button class="flex items-center text-gray-400 hover:text-blue-500">
          <span class="mr-1">💬</span> {{ post.comments }}
        </button>
        <button class="flex items-center text-gray-400 hover:text-green-500">
          <span class="mr-1">↪</span> {{ $t('social.share') }}
        </button>
      </div>
    </div>
  </template>
  
  <script setup>
  defineProps({
    post: Object
  });
  </script>