<template>
  <div class="sidebar-container hidden md:block">
    <ClientOnly>

      <!-- 侧边栏主体 -->
      <nav class="sidebar-nav">

        <!-- Logo 区域 -->
        <div class="p-4 flex items-center justify-center md:justify-start">
          <div class="w-10 h-10 bg-gradient-to-br from-[#1e90ff] to-[#00bfff] rounded-full flex items-center justify-center mr-2">
            <span class="text-lg font-bold text-white">YG</span>
          </div>
          <span class="text-xl font-bold bg-gradient-to-r from-white to-[#1e90ff] bg-clip-text text-transparent">
            {{ $t('sidebar.title') }}
          </span>
        </div>

        <!-- 菜单列表 -->
        <div class="flex-1 overflow-y-auto">
          <ul class="py-2">
            <li v-for="(item, key) in menuItems" :key="key"
              :class="[
                'p-4 flex items-center cursor-pointer transition-all duration-300',
                currentView === key ? 'bg-[rgba(30,144,255,0.2)]' : 'hover:bg-[rgba(30,144,255,0.1)]'
              ]"
              @click="handleMenuItemClick(key)"
            >
              <component :is="item.icon" :size="20" class="mr-3"
                :class="currentView === key ? 'text-[#1e90ff]' : 'text-gray-400'" />
              <span :class="currentView === key ? 'text-white font-medium' : 'text-gray-400'">
                {{ $t(`sidebar.${key}`) }}
              </span>
            </li>
          </ul>
        </div>

        <!-- PWA 安装按钮 -->
        <div class="p-4 border-t border-[rgba(30,144,255,0.15)]">
          <PwaInstallButton />
        </div>

        <!-- 语言切换 -->
        <div class="p-4 border-t border-[rgba(30,144,255,0.15)]">
          <div class="text-sm text-gray-400 mb-2">{{ $t('sidebar.language') }}</div>
          <div class="relative">
            <button
              class="flex items-center w-full text-left px-2 py-1 rounded hover:bg-[rgba(30,144,255,0.1)]"
              @click="showLocaleMenu = !showLocaleMenu"
            >
              <component :is="Globe" :size="18" class="mr-2 text-gray-400" />
              <span class="text-gray-400">{{ localeNames[locale] }}</span>
              <component :is="ChevronDown" :size="16" class="ml-auto text-gray-400" />
            </button>

            <div v-if="showLocaleMenu"
              class="absolute left-0 right-0 mt-1 bg-[#0c2442] rounded shadow-lg border border-[rgba(30,144,255,0.2)]"
              @mouseleave="showLocaleMenu = false"
            >
              <div v-for="(name, code) in localeNames" :key="code"
                class="px-4 py-2 cursor-pointer hover:bg-[rgba(30,144,255,0.15)]"
                @click="changeLocale(code)"
              >
                {{ name }}
              </div>
            </div>
          </div>
        </div>

        <!-- 用户信息/登录区域 -->
        <div class="p-4 border-t border-[rgba(30,144,255,0.15)]">
          <div v-if="isAuthenticated" class="flex flex-col">
            <div class="flex items-center">
              <div class="w-8 h-8 bg-[rgba(30,144,255,0.15)] rounded-full mr-2 overflow-hidden">
                <img v-if="userInfo?.headimg" :src="userInfo.headimg" alt="用户头像" class="w-full h-full object-cover" />
                <div v-else class="w-full h-full flex items-center justify-center">
                  <component :is="User" :size="20" class="text-[#1e90ff]" />
                </div>
              </div>
              <div>
                <div class="text-sm text-white">{{ userInfo?.nickname || '用户' }}</div>
                <div class="text-xs text-gray-400">已登录</div>
              </div>
            </div>
            
            <button 
              class="mt-3 flex items-center w-full px-3 py-2 rounded hover:bg-[rgba(255,0,0,0.15)]"
              @click="handleLogout"
            >
              <component :is="LogOut" :size="18" class="mr-2 text-red-400" />
              <span class="text-sm text-red-400">{{ $t('auth.logout') }}</span>
            </button>
          </div>
          
          <button v-else
            class="flex items-center w-full px-3 py-2 rounded bg-[rgba(30,144,255,0.15)] hover:bg-[rgba(30,144,255,0.25)]"
            @click="showLoginDialog"
          >
            <component :is="LogIn" :size="18" class="mr-2 text-[#1e90ff]" />
            <span class="text-sm text-[#1e90ff]">{{ $t('auth.login') }}</span>
          </button>
        </div>

        <!-- 页脚信息 -->
        <div class="p-4 text-center border-t border-[rgba(30,144,255,0.15)]">
          <div class="text-xs text-gray-500">由 iCard 愛卡提供</div>
        </div>
      </nav>
    </ClientOnly>
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import { Home, BookOpen, Layers, Users, Activity, Globe, ChevronDown, Award, LogIn, LogOut, User, Menu, X, Heart } from 'lucide-vue-next';
import { useAuthStore } from '~/stores/auth';
import PwaInstallButton from './PwaInstallButton.vue';
import { useRoute } from 'vue-router';

const authStore = useAuthStore();
const route = useRoute();

// 菜单项配置
const menuItems = {
  home: { icon: Home },
  series: { icon: BookOpen },
  deck: { icon: Layers },
  social: { icon: Users },
  experience: { icon: BookOpen },
  Goodsgroup: { icon: Activity },
  tierlist: { icon: Award }
  // sponsor: { icon: Heart } // 暫時隱藏贊助頁面
};

const props = defineProps({
  currentView: {
    type: String,
    required: true,
    default: 'home'
  },
  locale: {
    type: String,
    required: true,
    default: 'zh-tw'
  }
});

const emit = defineEmits(['update:view', 'change-locale', 'login']);

const showLocaleMenu = ref(false);
const isMobileMenuOpen = ref(false);

const isAuthenticated = computed(() => authStore.isAuthenticated);
const userInfo = computed(() => authStore.user);

const localeNames = {
  'en': 'English',
  'zh-tw': '繁體中文',
  'zh-cn': '简体中文'
};

const handleMenuItemClick = (view) => {
  emit('update:view', view);
  closeSidebar();
};

const closeSidebar = () => {
  isMobileMenuOpen.value = false;
};

const toggleSidebar = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value;
};

const changeLocale = (code) => {
  emit('change-locale', code);
  showLocaleMenu.value = false;
};

const showLoginDialog = () => {
  emit('login');
};

const handleLogout = () => {
  authStore.logout();
  window.location.reload();
};

watch(route, () => {
  if (window.innerWidth < 768) {
    isMobileMenuOpen.value = false;
  }
});
</script>

<style scoped>
/* 手機版選單按鈕 */
.mobile-menu-btn {
  position: fixed;
  top: 1rem;
  left: 1rem;
  z-index: 9999;
  padding: 0.75rem;
  border-radius: 0.5rem;
  background-color: #061224;
  border: 1px solid rgba(30, 144, 255, 0.3);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.mobile-menu-btn:hover {
  background-color: #0c2442;
  border-color: rgba(30, 144, 255, 0.5);
}

@media (min-width: 768px) {
  .mobile-menu-btn {
    display: none;
  }
}

/* 手機版遮罩層 */
.mobile-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 35;
}

@media (min-width: 768px) {
  .mobile-overlay {
    display: none;
  }
}

/* 側邊欄主體 */
.sidebar-nav {
  position: fixed;
  top: 0;
  width: 16rem; /* 256px */
  background-color: #061224;
  display: flex;
  flex-direction: column;
  border-right: 1px solid rgba(30, 144, 255, 0.15);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  height: 100vh;
  transition: transform 0.3s ease;
  z-index: 40;
}

.sidebar-closed {
  transform: translateX(-100%);
}

.sidebar-open {
  transform: translateX(0);
}

@media (min-width: 768px) {
  .sidebar-nav {
    position: sticky;
    transform: translateX(0) !important;
  }
}

.sidebar-container {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 16rem; /* 256px */
  flex-shrink: 0;
  z-index: 40;
}

nav {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 16rem; /* 256px */
}

.flex-1 {
  flex: 1 1 auto;
  overflow-y: auto;
}

@media (max-width: 768px) {
  .sidebar-container {
    width: 16rem;
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
  }
  
  .sidebar-container.active {
    transform: translateX(0);
  }
}
</style>