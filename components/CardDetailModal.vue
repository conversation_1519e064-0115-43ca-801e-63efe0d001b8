<template>
  <div v-if="isOpen" class="card-modal-overlay" @click.self="closeModal">
    <div class="card-modal-container">
      <!-- 左側卡片3D效果區域 -->
      <div class="card-image-container" ref="cardContainer">
        <div class="card-3d-wrapper" ref="card3dWrapper">
          <img :src="card.goods_thumb" :alt="card.goods_title" class="card-image" @error="handleImageError" />
          <div class="card-shine"></div>
        </div>
      </div>
      
      <!-- 右側卡片資訊 -->
      <div class="card-info">
        <h2 class="card-name">{{ card.goods_title }}</h2>
        <div class="card-meta">
          <div class="meta-item">
            <span class="meta-label">卡號：</span>
            <span class="meta-value">{{ card.goods_sn }}</span>
          </div>
          <div class="meta-item" v-if="card.rare">
            <span class="meta-label">稀有度：</span>
            <span class="meta-value">{{ card.rare }}</span>
          </div>
          <div class="meta-item" v-if="card.info && card.info.type">
            <span class="meta-label">類型：</span>
            <span class="meta-value">{{ card.info.type }}</span>
          </div>
          <div class="meta-item" v-if="card.info && card.info.level">
            <span class="meta-label">等級：</span>
            <span class="meta-value">{{ card.info.level }}</span>
          </div>
          <div class="meta-item" v-if="card.info && card.info.attribute">
            <span class="meta-label">屬性：</span>
            <span class="meta-value">{{ card.info.attribute }}</span>
          </div>
          <div class="meta-item" v-if="card.info && card.info.race">
            <span class="meta-label">種族：</span>
            <span class="meta-value">{{ card.info.race }}</span>
          </div>
          <div class="meta-item" v-if="card.info && card.info.atk">
            <span class="meta-label">攻擊力：</span>
            <span class="meta-value">{{ card.info.atk }}</span>
          </div>
          <div class="meta-item" v-if="card.info && card.info.def">
            <span class="meta-label">防禦力：</span>
            <span class="meta-value">{{ card.info.def }}</span>
          </div>
        </div>
        <div class="card-effect" v-if="card.info && card.info.effect">
          <h3 class="effect-title">效果</h3>
          <p class="effect-text">{{ card.info.effect }}</p>
        </div>
      </div>
      
      <button class="close-button" @click="closeModal">×</button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import VanillaTilt from 'vanilla-tilt';

const props = defineProps({
  card: {
    type: Object,
    required: true
  },
  isOpen: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close']);

const cardContainer = ref(null);
const card3dWrapper = ref(null);

const closeModal = () => {
  emit('close');
};

const handleImageError = (event) => {
  // 避免無限循環：如果已經是佔位圖片還載入失敗，則停止
  if (event.target.src.includes('placeholder.jpg')) {
    // console.warn('佔位圖片載入失敗');
    return;
  }
  event.target.src = '/images/placeholder.jpg';
};

let tiltInstance = null;

const initTilt = () => {
  if (card3dWrapper.value) {
    // 銷毀可能已存在的tilt實例
    if (tiltInstance) {
      tiltInstance.destroy();
      tiltInstance = null;
    }
    
    // 初始化vanilla-tilt
    tiltInstance = VanillaTilt.init(card3dWrapper.value, {
      max: 15,                // 最大傾斜角度
      speed: 400,             // 動畫速度
      glare: true,            // 開啟反光效果
      "max-glare": 0.5,       // 最大反光強度
      gyroscope: true,        // 手機陀螺儀支持
      scale: 1.05,            // 懸停縮放
      perspective: 1000,      // 3D視角
      transition: true,       // 啟用過渡效果
      "full-page-listening": false, // 僅響應卡片區域的鼠標移動
      "mouse-event-element": cardContainer.value // 將鼠標事件偵聽綁定到容器元素
    });
  }
};

// 監聽isOpen變化，當彈窗打開時初始化tilt效果
watch(() => props.isOpen, (newValue) => {
  if (newValue) {
    // 延遲一幀以確保DOM已更新
    setTimeout(initTilt, 0);
  } else if (tiltInstance) {
    tiltInstance.destroy();
    tiltInstance = null;
  }
});

// 組件掛載時初始化tilt效果
onMounted(() => {
  if (props.isOpen) {
    initTilt();
  }
});

// 組件卸載前清理tilt實例
onBeforeUnmount(() => {
  if (tiltInstance) {
    tiltInstance.destroy();
    tiltInstance = null;
  }
});
</script>

<style scoped>
.card-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.card-modal-container {
  display: flex;
  background: linear-gradient(145deg, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.95));
  border-radius: 16px;
  overflow: hidden;
  width: 90%;
  max-width: 1000px;
  height: 80vh;
  max-height: 600px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
  position: relative;
  border: 1px solid rgba(30, 144, 255, 0.2);
}

.card-image-container {
  width: 50%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  perspective: 1000px;
  padding: 30px;
  background: rgba(0, 0, 0, 0.3);
}

.card-3d-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  max-height: 550px;
  transform-style: preserve-3d;
  transition: transform 0.1s ease;
  transform: rotateX(0) rotateY(0);
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 8px;
  transform-style: preserve-3d;
  box-shadow: 0 0 30px rgba(30, 144, 255, 0.2);
}

.card-shine {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 8px;
  pointer-events: none;
}

.card-info {
  width: 50%;
  padding: 40px;
  color: white;
  overflow-y: auto;
}

.card-name {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 20px;
  background: linear-gradient(90deg, #1e90ff, #00bfff);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  text-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.card-meta {
  margin-bottom: 30px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.meta-item {
  margin-bottom: 8px;
}

.meta-label {
  color: rgba(30, 144, 255, 0.8);
  font-size: 14px;
  font-weight: 600;
  margin-right: 5px;
}

.meta-value {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
}

.effect-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 10px;
  color: rgba(30, 144, 255, 0.8);
}

.effect-text {
  font-size: 14px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
  white-space: pre-line;
}

.close-button {
  position: absolute;
  top: 15px;
  right: 15px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: rotate(90deg);
}

/* 自定義滾動條 */
.card-info::-webkit-scrollbar {
  width: 5px;
}

.card-info::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

.card-info::-webkit-scrollbar-thumb {
  background: rgba(30, 144, 255, 0.5);
  border-radius: 5px;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .card-modal-container {
    flex-direction: column;
    height: 90vh;
    max-height: none;
  }
  
  .card-image-container,
  .card-info {
    width: 100%;
  }
  
  .card-image-container {
    height: 50%;
    padding: 20px;
  }
  
  .card-info {
    height: 50%;
    padding: 20px;
  }
  
  .card-meta {
    grid-template-columns: 1fr;
  }
}
</style> 