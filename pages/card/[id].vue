<template>
  <div class="card-details-page">
    <!-- 頂部導航 -->
    <header class="header">
      <div class="container">
        <div class="header-content">
          <button class="back-button" @click="goBack">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            返回
          </button>
          <h1 class="page-title">{{ cardData.goods_title || "卡片詳情" }}</h1>
        </div>
      </div>
    </header>

    <!-- 主內容 -->
    <main class="main-content">
      <div class="container">
        <!-- 卡片基本資訊 -->
        <section class="card-info-section">
          <div class="card-info-card">
            <div class="card-info-content">
              <div class="card-image-container">
                <img
                  :src="cardData.goods_thumb || ''"
                  :alt="cardData.goods_title"
                  class="card-main-image"
                />
                <div class="ban-type-indicator" v-if="cardData.ban_type">
                  {{ getBanTypeText(cardData.ban_type) }}
                </div>
              </div>
              <div class="card-details">
                <h2 class="card-title">{{ cardData.goods_title }}</h2>
                <div class="card-effect-box">
                  <div class="effect-content">
                    {{ cardData.effect || "無效果" }}
                  </div>
                </div>
                <!-- 桌面版的狀態和價格信息 -->
                <div class="card-ban-status desktop-only">
                  <span class="ban-label">限制: </span>
                  <span
                    class="ban-value"
                    :class="`ban-status-${cardData.ban_type}`"
                  >
                    {{ getBanStatusText(cardData.ban_type) }}
                  </span>
                </div>
                <div
                  class="card-price-info desktop-only"
                  v-if="lowestPriceCard"
                >
                  <span class="price-label">參考最低價: </span>
                  <span class="lowest-price-value"
                    >{{ lowestPriceCard.price }} 元</span
                  >
                  <span class="lowest-price-details"
                    >({{ lowestPriceCard.goods_sn }} |
                    {{ lowestPriceCard.rare }})</span
                  >
                </div>
                <div class="card-price-info desktop-only" v-else>
                  <span class="price-label">參考最低價: </span>
                  <span class="price-not-available">暫無出售</span>
                </div>
              </div>
              <!-- 手機版的狀態和價格信息區域 -->
              <div class="card-mobile-status mobile-only">
                <div class="card-ban-status">
                  <span class="ban-label">限制: </span>
                  <span
                    class="ban-value"
                    :class="`ban-status-${cardData.ban_type}`"
                  >
                    {{ getBanStatusText(cardData.ban_type) }}
                  </span>
                </div>
                <div class="card-price-info" v-if="lowestPriceCard">
                  <span class="price-label">參考最低價: </span>
                  <span class="lowest-price-value"
                    >{{ lowestPriceCard.price }} 元</span
                  >
                  <span class="lowest-price-details"
                    >({{ lowestPriceCard.goods_sn }} |
                    {{ lowestPriceCard.rare }})</span
                  >
                </div>
                <div class="card-price-info" v-else>
                  <span class="price-label">參考最低價: </span>
                  <span class="price-not-available">暫無出售</span>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- 收錄詳情 -->
        <section
          class="card-sets-section"
          v-if="cardPrices.length > 0"
        >
          <div class="section-header">
            <h3>收錄詳情</h3>
          </div>
          <div class="card-sets-container">
            <div class="card-sets-list">
              <div class="set-item-header">
                <div class="set-column card-set-name">收錄系列</div>
                <div class="set-column card-sn">卡號</div>
                <div class="set-column card-rare">稀有度</div>
                <div class="set-column card-price">價格</div>
              </div>
              <div
                v-for="(price, index) in cardPrices"
                :key="index"
                class="set-item"
                @click="goToSeriesDetail(price.series_id)"
                @mousemove="
                  updateHoverPosition($event, price, `price_${index}`)
                "
                @mouseleave="hideHover(`price_${index}`)"
              >
                <div class="set-column card-set-name" :title="price.series">
                  {{ truncateSeriesName(price.series) }}
                </div>
                <div class="set-column card-sn">{{ price.goods_sn }}</div>
                <div class="set-column card-rare">
                  <span class="rare-badge">{{ price.rare }}</span>
                </div>
                <div class="set-column card-price">
                  <span class="price-value" v-if="price.price > 0"
                    >{{ price.price }} 元</span
                  >
                  <span class="price-value price-not-available" v-else
                    >暫無出售</span
                  >
                  <el-button
                    type="warning"
                    size="small"
                    round
                    v-if="price.price > 0"
                    @click.stop="goToIWantCardPurchase(price.goods_id)"
                  >
                    購買
                  </el-button>
                </div>
                <div
                  class="card-hover-details"
                  :style="
                    cardHoverStyles[`price_${index}`] || { display: 'none' }
                  "
                >
                  <div class="card-hover-title">
                    {{ price.goods_title || "未知卡名" }}
                  </div>
                  <div class="card-hover-effect" v-if="cardData.effect">
                    {{ cardData.effect }}
                  </div>
                  <div class="card-hover-effect" v-else>無效果描述</div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- 包含此卡的牌組 -->
        <section class="related-decks-section">
          <div class="section-header">
            <h3>包含此卡的牌組</h3>
          </div>
          <div class="related-decks-container">
            <div class="related-decks-list" v-if="relatedDecks.length > 0">
              <div
                v-for="(deck, index) in relatedDecks"
                :key="index"
                class="related-deck-item"
                @click="goToCardGroupDetails(deck.group_id, deck.user_id || 0)"
              >
                <div class="deck-image">
                  <img :src="deck.image" :alt="deck.title" />
                </div>
                <div class="deck-info">
                  <div class="deck-title">
                    {{ deck.title }}
                    <span
                      v-if="deck.is_identify === 1"
                      class="deck-tag normal-deck"
                      >一般牌組</span
                    >
                    <span
                      v-if="deck.is_identify === 2"
                      class="deck-tag top-deck"
                      >上位牌組</span
                    >
                  </div>
                  <div class="deck-desc" v-if="deck.desc">{{ deck.desc }}</div>
                </div>
                <div class="deck-env">{{ deck.env_title }}</div>
              </div>
            </div>

            <div v-if="relatedDecks.length === 0" class="empty-decks-message">
              暫無包含此卡的牌組
            </div>

            <div class="pagination-container" v-if="totalDeckPages > 1">
              <el-pagination
                layout="prev, pager, next"
                :total="totalDeckPages * 20"
                :current-page="currentDeckPage"
                @current-change="handleDeckPageChange"
                :page-size="20"
                background
                class="custom-pagination"
              ></el-pagination>
            </div>
          </div>
        </section>
      </div>
    </main>

    <!-- 購買確認對話框 -->
    <el-dialog
      title="確認購買"
      v-model="purchaseDialogVisible"
      width="360px"
      custom-class="purchase-dialog"
    >
      <div class="purchase-content" v-if="selectedPrice">
        <div class="purchase-card-info">
          <img
            :src="cardData.goods_thumb"
            :alt="cardData.goods_title"
            class="purchase-card-image"
          />
          <div class="purchase-card-details">
            <div class="purchase-card-title">{{ cardData.goods_title }}</div>
            <div class="purchase-card-set">{{ selectedPrice.series }}</div>
            <div class="purchase-card-rare">{{ selectedPrice.rare }}</div>
          </div>
        </div>
        <div class="purchase-price-info">
          <div class="price-label">價格</div>
          <div class="price-value">{{ selectedPrice.price }} 元</div>
        </div>
        <div class="purchase-quantity">
          <div class="quantity-label">數量</div>
          <el-input-number
            v-model="purchaseQuantity"
            :min="1"
            :max="10"
            size="small"
          ></el-input-number>
        </div>
        <div class="purchase-total">
          <div class="total-label">總計</div>
          <div class="total-value">
            {{ (selectedPrice.price * purchaseQuantity).toFixed(2) }} 元
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="purchaseDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmPurchase">確認購買</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch } from "vue";
import { useRoute, useRouter, useHead } from "#app";
import { ElMessage } from "element-plus";
import { useCardApi } from "~/composables/useCardApi";

const route = useRoute();
const router = useRouter();
const goodsId = parseInt(route.params.id) || 0;

// 狀態
const purchaseDialogVisible = ref(false);
const selectedPrice = ref(null);
const purchaseQuantity = ref(1);
const currentDeckPage = ref(1);
const cardHoverStyles = ref({});

// SSR 取得卡片主資料
const { data: cardDataFromServer } = await useFetch(
  "/api/Goods/getInfoDetail",
  {
    method: "POST",
    body: { goods_id: goodsId },
    default: () => ({ code: 200, data: {} }),
  }
);
const cardData = computed(() => cardDataFromServer.value?.data || {});

// SSR 取得卡片價格
const { data: cardPricesFromServer } = await useFetch(
  "/api/Trade/getGoodsInfoByGoodsId",
  {
    method: "POST",
    body: { goods_id: goodsId },
    default: () => ({ code: 200, data: [] }),
  }
);
const cardPrices = computed(() =>
  (cardPricesFromServer.value?.data || []).map((item) => ({
    goods_id: item.goods_id,
    goods_title: item.goods_title,
    goods_sn: item.goods_sn || "未知編號",
    goods_thumb: item.goods_thumb,
    rare: item.rare || "未知稀有度",
    price: item.price || 0,
    series: item.series || "未知系列",
    series_id: item.series_id || null,
  }))
);
const lowestPriceCard = computed(() => {
  const validPrices = cardPrices.value.filter((card) => card.price > 0);
  if (validPrices.length > 0) {
    return validPrices.sort((a, b) => a.price - b.price)[0];
  }
  return null;
});

// SSR 取得相關牌組（初始載入第一頁）
const initialDeckParams = {
  goods_id: goodsId,
  page: 1,
  page_nums: 20,
};
const { data: relatedDecksFromServer } = await useFetch(
  "/api/Goodsgroup/getGroupsByGoodsId",
  {
    method: "POST",
    body: initialDeckParams,
    default: () => ({ code: 200, data: { list: [], total_page: 1 } }),
  }
);

// 客戶端分頁載入（只在用戶互動時）
const deckPageParams = computed(() => ({
  goods_id: goodsId,
  page: currentDeckPage.value,
  page_nums: 20,
}));
const { data: paginatedDecksData, refresh: refreshRelatedDecks } = await useFetch(
  "/api/Goodsgroup/getGroupsByGoodsId",
  {
    method: "POST",
    body: deckPageParams,
    default: () => ({ code: 200, data: { list: [], total_page: 1 } }),
    immediate: false, // 不在初始載入時執行
  }
);
const relatedDecks = computed(() => {
  // 如果是第一頁，使用 SSR 載入的資料
  if (currentDeckPage.value === 1) {
    return relatedDecksFromServer.value?.data?.list || [];
  }
  // 其他頁面使用客戶端載入的資料
  return paginatedDecksData.value?.data?.list || [];
});

const totalDeckPages = computed(() => {
  // 如果是第一頁，使用 SSR 載入的資料
  if (currentDeckPage.value === 1) {
    return relatedDecksFromServer.value?.data?.total_page || 1;
  }
  // 其他頁面使用客戶端載入的資料
  return paginatedDecksData.value?.data?.total_page || 1;
});

// SEO
const seoTitle = computed(() => cardData.value?.goods_title ? `${cardData.value.goods_title} - 遊戲王資訊站` : "遊戲王卡片詳情 - 遊戲王資訊站");
const seoDescription = computed(() => {
  if (cardData.value?.goods_title && cardData.value?.effect) {
    const effect = cardData.value.effect.length > 120 ? cardData.value.effect.slice(0, 120) + "..." : cardData.value.effect;
    return `${cardData.value.goods_title} - ${effect}。查看卡片效果、價格、收錄系列及相關牌組。`;
  }
  return "遊戲王卡片詳情頁面，提供卡片效果、價格、收錄系列及相關牌組資訊。";
});
const seoKeywords = computed(() => {
  const keywords = ["遊戲王", "卡片", "效果", "價格", "收錄系列"];
  if (cardData.value?.goods_title) keywords.unshift(cardData.value.goods_title);
  if (cardData.value?.effect) {
    const effectKeywords = cardData.value.effect.replace(/[^\u4e00-\u9fa5]/g, " ").split(" ").filter((word) => word.length > 1).slice(0, 5);
    keywords.push(...effectKeywords);
  }
  return keywords.join(", ");
});
const ogImage = computed(() => cardData.value?.goods_thumb || "/images/og-image.jpg");
const config = useRuntimeConfig();
const canonicalUrl = computed(() => `${config.public.siteUrl}/card/${goodsId}`);
useHead({
  title: seoTitle,
  meta: [
    { name: "description", content: seoDescription },
    { name: "keywords", content: seoKeywords },
    { name: "robots", content: "index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" },
    { name: "googlebot", content: "index, follow" },
    { name: "bingbot", content: "index, follow" },
    { property: "og:title", content: seoTitle },
    { property: "og:description", content: seoDescription },
    { property: "og:type", content: "website" },
    { property: "og:url", content: canonicalUrl },
    { property: "og:image", content: ogImage },
    { property: "og:image:width", content: "1200" },
    { property: "og:image:height", content: "630" },
    { property: "og:image:alt", content: cardData.value?.goods_title || "遊戲王卡片" },
    { property: "og:site_name", content: "遊戲王資訊站" },
    { property: "og:locale", content: "zh_TW" },
    { name: "twitter:card", content: "summary_large_image" },
    { name: "twitter:title", content: seoTitle },
    { name: "twitter:description", content: seoDescription },
    { name: "twitter:image", content: ogImage },
    { name: "author", content: "遊戲王資訊站" },
    { name: "application-name", content: "遊戲王資訊站" },
    { name: "format-detection", content: "telephone=no" },
  ],
  link: [
    { rel: "canonical", href: canonicalUrl },
    { rel: "preload", as: "image", href: ogImage },
  ],
});

// 分頁互動時 refetch 相關牌組（只在非第一頁時）
watch(currentDeckPage, () => {
  if (currentDeckPage.value > 1) {
    refreshRelatedDecks();
  }
});

// 互動邏輯
const getBanTypeText = (banType) => {
  switch (parseInt(banType)) {
    case 1: return "禁止卡";
    case 2: return "限制卡(限1張)";
    case 3: return "準限制卡(限2張)";
    default: return "";
  }
};
const getBanStatusText = (banType) => {
  switch (parseInt(banType)) {
    case 1: return "禁止卡";
    case 2: return "限制卡(限1張)";
    case 3: return "準限制卡(限2張)";
    default: return "無限制";
  }
};
const goToCardDetail = (goodsId) => { router.push(`/card/${goodsId}`); };
const goToSeriesDetail = (seriesId) => { if (seriesId) router.push(`/series/${seriesId}`); };
const goToIWantCardPurchase = (goodsId) => { const iWantCardUrl = `https://web.iwantcard.tw/carddetail/${goodsId}`; window.open(iWantCardUrl, "_blank"); };
const confirmPurchase = () => {
  if (selectedPrice.value) {
    const iWantCardUrl = `https://web.iwantcard.tw/carddetail/${selectedPrice.value.goods_id}`;
    window.open(iWantCardUrl, "_blank");
  }
  purchaseDialogVisible.value = false;
};
const goToCardGroupDetails = (groupId, userId) => { router.push(`/Goodsgroup/${groupId}`); };
const goBack = () => { router.back(); };
const handleDeckPageChange = (page) => { currentDeckPage.value = page; };
const updateHoverPosition = (event, card, key) => { cardHoverStyles.value[key] = { display: "block" }; };
const hideHover = (key) => { cardHoverStyles.value[key] = { display: "none" }; };
const truncateSeriesName = (seriesName) => { if (!seriesName) return ""; return seriesName.length > 5 ? seriesName.substring(0, 5) + "..." : seriesName; };
</script>

<style scoped>
.card-details-page {
  min-height: 100vh;
  background-color: #091020;
  background-image: radial-gradient(
      circle at 20% 35%,
      rgba(255, 143, 0, 0.1) 0%,
      transparent 40%
    ),
    radial-gradient(
      circle at 80% 10%,
      rgba(255, 61, 0, 0.08) 0%,
      transparent 40%
    );
  color: #fff;
  font-family: "Noto Sans TC", sans-serif;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

.header {
  background: linear-gradient(
    180deg,
    rgba(24, 28, 40, 0.9) 0%,
    rgba(9, 16, 32, 0.8) 100%
  );
  border-bottom: 1px solid rgba(255, 143, 0, 0.2);
  padding: 10px 0;
  position: sticky;
  top: 0;
  z-index: 10;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 143, 0, 0.1);
  border: 1px solid rgba(255, 143, 0, 0.2);
  border-radius: 20px;
  padding: 8px 16px;
  color: #fff;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

.back-button:hover {
  background: rgba(255, 143, 0, 0.2);
  transform: translateY(-2px);
}

.page-title {
  font-size: 24px;
  font-weight: 800;
  letter-spacing: 1px;
  margin: 0;
  text-shadow: 0 0 10px rgba(255, 143, 0, 0.5);
  background: linear-gradient(to right, #fff, #ff8f00);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  flex: 1;
  text-align: center;
}

.main-content {
  padding-top: 15px;
}



/* 卡片基本資訊區 */
.card-info-section {
  margin-bottom: 20px;
}

.card-info-card {
  background: rgba(14, 22, 40, 0.7);
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 143, 0, 0.15);
  padding: 25px;
}

.card-info-content {
  display: flex;
  gap: 30px;
}

.card-image-container {
  flex-shrink: 0;
  width: 280px;
  height: 390px;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(255, 143, 0, 0.3);
  background: rgba(0, 0, 0, 0.3);
}

.card-main-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.ban-type-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(255, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.card-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card-title {
  font-size: 24px;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 15px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(255, 143, 0, 0.3);
}

.card-effect-box {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  flex: 1;
  border: 1px solid rgba(255, 143, 0, 0.15);
}

.effect-content {
  font-size: 14px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
  white-space: pre-wrap;
}

.card-ban-status,
.card-price-info {
  display: flex;
  align-items: center;
  margin-top: 15px;
}

.ban-label,
.price-label {
  font-weight: bold;
  color: rgba(255, 255, 255, 0.7);
  margin-right: 8px;
}

.ban-value {
  padding: 4px 10px;
  border-radius: 4px;
  font-weight: bold;
  font-size: 14px;
}

.ban-status-0 {
  background: rgba(76, 175, 80, 0.7);
  color: white;
}

.ban-status-1 {
  background: rgba(244, 67, 54, 0.7);
  color: white;
}

.ban-status-2,
.ban-status-3 {
  background: rgba(255, 152, 0, 0.7);
  color: white;
}

/* 收錄詳情區域 */
.card-sets-section {
  background: rgba(14, 22, 40, 0.7);
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 143, 0, 0.15);
  padding: 20px;
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h3 {
  font-size: 18px;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  padding-left: 10px;
  border-left: 4px solid #ff8f00;
}

.card-sets-container {
  padding: 10px;
  max-height: 400px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #ff8f00 rgba(0, 0, 0, 0.2);
  overscroll-behavior: contain;
  scroll-behavior: smooth;
}

.card-sets-container::-webkit-scrollbar {
  width: 6px;
}

.card-sets-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
}

.card-sets-container::-webkit-scrollbar-thumb {
  background-color: #ff8f00;
  border-radius: 10px;
}

.card-sets-list {
  display: flex;
  flex-direction: column;
  padding-bottom: 20px;
  min-height: min-content;
}

.set-item-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  background: rgba(0, 0, 0, 0.2);
  padding: 12px 15px;
  border-radius: 8px;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 10px;
  flex-shrink: 0;
}

.set-item {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 12px 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  align-items: center;
  position: relative;
  overflow: visible;
  margin-bottom: 8px;
  flex-shrink: 0;
}

.set-item::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 143, 0, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.set-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  background: rgba(255, 143, 0, 0.1);
}

.set-item:hover::after {
  opacity: 1;
}

.set-column {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 1;
}

.card-sn {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
}

.card-rare {
  display: flex;
  justify-content: center;
}

.rare-badge {
  display: inline-block;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 0.5px;
  background: rgba(255, 143, 0, 0.2);
  color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 143, 0, 0.3);
}

.card-set-name {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.card-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price-value {
  font-size: 16px;
  font-weight: bold;
  color: #ff8f00;
  margin-right: 10px;
}

.price-not-available {
  color: rgba(255, 255, 255, 0.4);
  font-style: italic;
}

.lowest-price-value {
  font-size: 18px;
  font-weight: bold;
  color: #ff8f00;
  background: rgba(255, 143, 0, 0.1);
  padding: 3px 8px;
  border-radius: 4px;
  border: 1px solid rgba(255, 143, 0, 0.3);
}

.lowest-price-details {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
  margin-left: 8px;
  background: rgba(0, 0, 0, 0.2);
  padding: 2px 6px;
  border-radius: 4px;
}

/* 包含此卡的牌組區域 */
.related-decks-section {
  background: rgba(14, 22, 40, 0.7);
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 143, 0, 0.15);
  padding: 20px;
  margin-bottom: 20px;
}

.related-decks-container {
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.related-decks-list {
  display: flex;
  flex-direction: column;
  max-height: 450px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #ff8f00 rgba(0, 0, 0, 0.2);
}

.related-decks-list::-webkit-scrollbar {
  width: 6px;
}

.related-decks-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
}

.related-decks-list::-webkit-scrollbar-thumb {
  background-color: #ff8f00;
  border-radius: 10px;
}

.related-deck-item {
  display: grid;
  grid-template-columns: 50px 1fr auto;
  gap: 12px;
  padding: 12px 15px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  align-items: center;
  border-bottom: 1px solid rgba(255, 143, 0, 0.1);
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  margin-bottom: 8px;
}

.related-deck-item:hover {
  background-color: rgba(255, 143, 0, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.deck-image {
  width: 50px;
  height: 50px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid rgba(255, 143, 0, 0.3);
}

.deck-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.deck-info {
  min-width: 0;
  overflow: hidden;
}

.deck-title {
  font-size: 15px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
  gap: 6px;
}

.deck-tag {
  display: inline-block;
  padding: 1px 6px;
  border-radius: 3px;
  font-size: 12px;
  font-weight: normal;
  white-space: nowrap;
  flex-shrink: 0;
}

.normal-deck {
  background-color: rgba(0, 188, 212, 0.2);
  color: #00bcd4;
  border: 1px solid rgba(0, 188, 212, 0.3);
}

.top-deck {
  background-color: rgba(255, 143, 0, 0.2);
  color: #ff8f00;
  border: 1px solid rgba(255, 143, 0, 0.3);
}

.deck-desc {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.6);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 3px;
}

.deck-env {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.5);
  text-align: right;
}

.pagination-container {
  margin-top: 25px;
  display: flex;
  justify-content: center;
  padding: 10px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  border: 1px solid rgba(255, 143, 0, 0.1);
}

/* 控制桌面版和手機版元素顯示 */
.mobile-only {
  display: none;
}

.desktop-only {
  display: block;
}

/* 響應式調整 */
@media (max-width: 768px) {
  .mobile-only {
    display: block;
  }

  .desktop-only {
    display: none;
  }
  .container {
    padding: 0 10px;
  }

  .header {
    padding: 8px 0;
  }

  .page-title {
    font-size: 18px;
  }

  .back-button {
    padding: 6px 12px;
    font-size: 13px;
    gap: 6px;
  }

  .main-content {
    padding-top: 10px;
  }

  .card-info-section {
    margin-bottom: 15px;
  }

  .card-info-card {
    padding: 15px;
  }

  .card-info-content {
    display: grid;
    grid-template-columns: 120px 1fr;
    grid-template-rows: auto auto auto;
    gap: 12px;
    grid-template-areas:
      "image title"
      "image effect"
      "status status";
  }

  .card-image-container {
    grid-area: image;
    width: 120px;
    height: 170px;
    margin-bottom: 0;
  }

  .card-details {
    grid-area: title / effect;
    min-width: 0;
    display: flex;
    flex-direction: column;
  }

  .card-title {
    font-size: 16px;
    margin: 0 0 8px 0;
    padding-bottom: 6px;
    line-height: 1.3;
  }

  .card-effect-box {
    padding: 10px;
    margin-bottom: 0;
    min-height: 80px;
    max-height: 120px;
    overflow-y: auto;
    flex: 1;
  }

  .effect-content {
    font-size: 12px;
    line-height: 1.4;
  }

  .card-mobile-status {
    grid-area: status;
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 8px;
  }

  .card-ban-status,
  .card-price-info {
    margin-top: 0;
    font-size: 13px;
  }

  .ban-label,
  .price-label {
    font-size: 12px;
  }

  .ban-value {
    font-size: 11px;
    padding: 2px 6px;
  }

  .lowest-price-value {
    font-size: 14px;
    padding: 2px 6px;
  }

  .lowest-price-details {
    font-size: 11px;
    margin-left: 6px;
  }

  .set-item-header,
  .set-item {
    grid-template-columns: 1.2fr 0.8fr 0.8fr 1.2fr;
    padding: 8px 10px;
    font-size: 12px;
  }

  .card-set-name {
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
  }

  .card-set-name::after {
    content: attr(title);
    display: none;
  }

  .section-header h3 {
    font-size: 16px;
  }

  .card-sets-section,
  .related-decks-section {
    padding: 12px;
    margin-bottom: 15px;
  }

  .card-sets-container {
    max-height: 300px;
  }

  .card-price {
    flex-direction: column;
    gap: 6px;
  }

  .price-value {
    font-size: 14px;
  }

  .rare-badge {
    font-size: 10px;
    padding: 4px 6px;
  }
}

@media (max-width: 480px) {
  .related-deck-item {
    grid-template-columns: 40px 1fr auto;
    gap: 10px;
  }

  .deck-image {
    width: 40px;
    height: 40px;
  }

  .deck-title {
    font-size: 14px;
  }

  .deck-desc {
    font-size: 12px;
  }

  .deck-env {
    font-size: 12px;
  }

  .set-item-header,
  .set-item {
    grid-template-columns: 1fr 1fr 1fr;
  }

  .set-column.card-sn {
    display: none;
  }
}

.empty-decks-message {
  text-align: center;
  padding: 40px 0;
  color: rgba(255, 255, 255, 0.5);
  font-size: 16px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  border: 1px dashed rgba(255, 143, 0, 0.3);
}

.card-hover-details {
  width: 300px;
  background-color: rgba(10, 20, 40, 0.95);
  color: white;
  border: 1px solid rgba(255, 143, 0, 0.4);
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  z-index: 9999;
  pointer-events: none;
  position: absolute;
  top: 0;
  left: 100%;
  margin-left: 10px;
}

/* 自定義分頁樣式 */
.custom-pagination {
  /* 全局分頁變量 */
  --el-pagination-bg-color: transparent;
  --el-pagination-text-color: #fff;
  --el-pagination-hover-color: #ff8f00;
  --el-pagination-button-color: rgba(255, 255, 255, 0.7);
  --el-pagination-button-bg-color: rgba(0, 0, 0, 0.3);
  --el-pagination-button-disabled-color: rgba(255, 255, 255, 0.3);
  --el-pagination-button-disabled-bg-color: rgba(0, 0, 0, 0.2);
  --el-pagination-button-active-color: #fff;
  --el-pagination-button-active-bg-color: #ff8f00;
}

.custom-pagination :deep(.el-pager li) {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 143, 0, 0.2);
  color: rgba(255, 255, 255, 0.7);
  border-radius: 4px;
  margin: 0 3px;
  transition: all 0.3s ease;
}

.custom-pagination :deep(.el-pager li.is-active) {
  background: rgba(255, 143, 0, 0.3);
  border-color: rgba(255, 143, 0, 0.6);
  color: #fff;
  font-weight: bold;
}

.custom-pagination :deep(.el-pager li:hover) {
  background: rgba(255, 143, 0, 0.2);
  border-color: rgba(255, 143, 0, 0.4);
  color: #ff8f00;
}

.custom-pagination :deep(.btn-prev),
.custom-pagination :deep(.btn-next) {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 143, 0, 0.2);
  color: rgba(255, 255, 255, 0.7);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.custom-pagination :deep(.btn-prev:hover),
.custom-pagination :deep(.btn-next:hover) {
  background: rgba(255, 143, 0, 0.2);
  border-color: rgba(255, 143, 0, 0.4);
  color: #ff8f00;
}

.custom-pagination :deep(.btn-prev.is-disabled),
.custom-pagination :deep(.btn-next.is-disabled) {
  background: rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.3);
}

/* 購買對話框樣式 */
.purchase-dialog {
  background: rgba(14, 22, 40, 0.95);
  border: 1px solid rgba(255, 143, 0, 0.3);
  border-radius: 12px;
}

.purchase-content {
  color: #fff;
}

.purchase-card-info {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  border: 1px solid rgba(255, 143, 0, 0.2);
}

.purchase-card-image {
  width: 80px;
  height: 112px;
  border-radius: 6px;
  object-fit: contain;
  border: 1px solid rgba(255, 143, 0, 0.3);
}

.purchase-card-details {
  flex: 1;
}

.purchase-card-title {
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 5px;
}

.purchase-card-set {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 3px;
}

.purchase-card-rare {
  font-size: 12px;
  color: #ff8f00;
  background: rgba(255, 143, 0, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
}

.purchase-price-info,
.purchase-quantity,
.purchase-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 6px;
}

.purchase-total {
  background: rgba(255, 143, 0, 0.1);
  border: 1px solid rgba(255, 143, 0, 0.3);
  font-weight: bold;
}

.price-label,
.quantity-label,
.total-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.price-value,
.total-value {
  font-size: 16px;
  font-weight: bold;
  color: #ff8f00;
}

.total-value {
  font-size: 18px;
}
</style>
