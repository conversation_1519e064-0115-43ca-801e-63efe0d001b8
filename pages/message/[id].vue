<template>
  <div class="bg-gradient-to-b from-gray-900 to-gray-800 min-h-full">
    <!-- 主要內容區 -->
    <div
      class="container mx-auto px-3 md:px-4 max-w-4xl py-3 md:py-6 pb-40 md:pb-20"
    >
      <!-- 載入狀態 -->
      <div v-if="isLoading" class="flex items-center justify-center py-20">
        <div
          class="animate-spin h-12 w-12 border-t-2 border-b-2 border-blue-500 rounded-full"
        ></div>
      </div>

      <!-- 錯誤狀態 -->
      <div v-else-if="error" class="text-center py-20">
        <div
          class="bg-gray-800 rounded-lg p-8 max-w-md mx-auto border border-[rgba(30,144,255,0.15)]"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-16 w-16 text-gray-500 mx-auto mb-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
          <h3 class="text-xl font-bold text-white mb-2">載入失敗</h3>
          <p class="text-red-400 mb-6">{{ error }}</p>
          <div class="flex gap-3 justify-center">
            <button
              @click="goBack"
              class="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              返回上頁
            </button>
            <button
              v-if="messageId > 0"
              @click="fetchPostDetail"
              class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              重試
            </button>
          </div>
        </div>
      </div>

      <!-- 動態內容 -->
      <div v-else-if="postData" class="space-y-2 md:space-y-4">
        <!-- 動態卡片 -->
        <div
          class="bg-gray-800 rounded-lg shadow-lg border border-[rgba(30,144,255,0.15)]"
        >
          <!-- 標題和更多操作按鈕 -->
          <div class="p-3 md:p-6 border-b border-[rgba(30,144,255,0.15)]">
            <div class="flex justify-between items-start mb-1 md:mb-4">
              <!-- 標題和返回按鈕區域 -->
              <div class="flex items-center flex-1 mr-4">
                <!-- 返回按鈕 -->
                <button
                  @click="goBack"
                  class="mr-3 p-2 rounded-full hover:bg-[rgba(30,144,255,0.1)] transition-colors duration-200 group"
                  title="返回上頁"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 text-gray-400 group-hover:text-blue-400 transition-colors duration-200"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M10 19l-7-7m0 0l7-7m-7 7h18"
                    />
                  </svg>
                </button>

                <!-- 標題 -->
                <h1 v-if="postData.title" class="text-2xl font-bold text-white">
                  {{ postData.title }}
                </h1>
              </div>

              <!-- 更多操作按鈕 -->
              <div class="flex items-center gap-2">
                <div
                  v-if="postData.is_pay === 1 && !isPaid"
                  class="flex items-center gap-1 bg-[rgba(30,144,255,0.1)] px-3 py-1 rounded-full"
                >
                  <span class="text-sm font-medium text-blue-400"
                    >${{ postData.price }}</span
                  >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4 text-blue-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 15v2m0 0v2m0-2h2m-2 0h-2m9-8a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>

                <button
                  @click="showActionsMenu = !showActionsMenu"
                  class="p-2 rounded-full hover:bg-[rgba(30,144,255,0.1)] transition-colors relative"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 text-gray-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
                    />
                  </svg>

                  <!-- 操作選單 -->
                  <div
                    v-if="showActionsMenu"
                    class="absolute right-0 top-full mt-2 bg-gray-800 rounded-lg shadow-lg border border-[rgba(30,144,255,0.3)] py-2 min-w-[120px] z-10"
                  >
                    <button
                      v-if="canEditOrDelete"
                      @click="handleEdit"
                      class="w-full px-4 py-2 text-left text-white hover:bg-[rgba(30,144,255,0.1)] transition-colors"
                    >
                      編輯
                    </button>
                    <button
                      v-if="canEditOrDelete"
                      @click="handleDelete"
                      class="w-full px-4 py-2 text-left text-red-400 hover:bg-[rgba(255,0,0,0.1)] transition-colors"
                    >
                      刪除
                    </button>
                    <button
                      @click="handleReport"
                      class="w-full px-4 py-2 text-left text-white hover:bg-[rgba(30,144,255,0.1)] transition-colors"
                    >
                      檢舉
                    </button>
                  </div>
                </button>
              </div>
            </div>

            <!-- 用戶信息 -->
            <div
              class="flex items-center gap-3 px-3 md:px-6 py-1.5 md:py-4 border-b border-[rgba(30,144,255,0.15)]"
            >
              <img
                :src="
                  postData.user_info?.headimg || '/images/default-avatar.png'
                "
                :alt="getUserDisplayName"
                class="w-10 h-10 rounded-full border border-[rgba(30,144,255,0.3)] flex-shrink-0"
                @click="navigateToUserProfile(postData.user_id)"
              />
              <div class="min-w-0 flex-1">
                <h2 class="text-base font-medium text-white truncate">
                  {{ getUserDisplayName }}
                </h2>
                <p class="text-sm text-gray-400">
                  {{ formatDateTime(postData.create_time) }}
                </p>
              </div>
            </div>
          </div>

          <!-- 內容區域 -->
          <div class="p-3 md:p-6">
            <!-- 付費內容遮罩 -->
            <div
              v-if="postData.is_pay === 1 && !isPaid"
              class="relative overflow-hidden rounded-lg"
            >
              <div class="blur-sm pointer-events-none p-4 bg-gray-700/50">
                <div v-html="formatContent" class="content-wrapper"></div>
              </div>
              <div
                class="absolute inset-0 bg-gradient-to-b from-transparent to-gray-800/90 flex flex-col items-center justify-end pb-8"
              >
                <p class="text-gray-300 text-center mb-4 font-medium">
                  此為付費內容，支付 ${{ postData.price }} 後可查看完整內容
                </p>
                <button
                  @click="handlePurchase"
                  class="px-6 py-2 bg-gradient-to-r from-blue-500 to-cyan-400 text-white rounded-full shadow-md hover:shadow-lg transition-all duration-300"
                >
                  購買解鎖
                </button>
              </div>
            </div>
            <div
              v-else
              v-html="formatContent"
              class="content-wrapper"
              @click="handleImageClick"
            ></div>
          </div>
        </div>

        <!-- 評論區 -->
        <div
          class="bg-gray-800 rounded-lg shadow-lg border border-[rgba(30,144,255,0.15)] mb-8 md:mb-6"
        >
          <div
            class="p-3 md:p-4 border-b border-[rgba(30,144,255,0.15)] flex justify-between items-center"
          >
            <h3 class="font-semibold text-white">
              評論 ({{ postData.comment_count || 0 }})
            </h3>
          </div>

          <!-- 評論列表 -->
          <div class="divide-y divide-[rgba(30,144,255,0.1)]">
            <div
              v-for="comment in comments"
              :key="comment.comment_id"
              class="p-3 md:p-4 flex gap-3"
            >
              <img
                :src="
                  comment.user_info?.headimg || '/images/default-avatar.png'
                "
                :alt="comment.user_info?.nickname || '匿名用戶'"
                class="w-8 h-8 rounded-full border border-[rgba(30,144,255,0.3)] mt-1"
                @click="navigateToUserProfile(comment.user_id)"
              />
              <div class="flex-1">
                <div class="flex justify-between items-start">
                  <div>
                    <h4 class="font-semibold text-white text-sm">
                      {{ comment.user_info?.nickname || "匿名用戶" }}
                    </h4>
                    <p
                      class="mt-1 text-sm text-gray-300"
                      v-html="formatCommentContent(comment.comment)"
                    ></p>
                  </div>
                  <button
                    v-if="canManageComment(comment)"
                    @click="handleDeleteComment(comment.comment_id)"
                    class="p-1 rounded-full hover:bg-[rgba(30,144,255,0.1)] transition-colors"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-4 w-4 text-gray-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                      />
                    </svg>
                  </button>
                </div>
                <div class="mt-1 text-xs text-gray-500">
                  {{ formatDateTime(comment.create_time) }}
                </div>
              </div>
            </div>

            <!-- 空狀態 -->
            <div
              v-if="comments.length === 0"
              class="p-4 md:p-6 text-center text-gray-500 text-sm min-h-[120px] flex items-center justify-center"
            >
              暫無評論，成為第一個留言的人吧！
            </div>
          </div>
        </div>

        <!-- 手機版額外滾動空間 -->
        <div class="md:hidden h-20"></div>
      </div>
    </div>

    <!-- 底部操作欄 -->
    <div
      class="fixed left-0 right-0 bg-gray-800/95 backdrop-blur-sm border-t border-[rgba(30,144,255,0.3)] py-3 px-4 z-40 mobile-bottom-comment"
    >
      <div class="container mx-auto max-w-4xl flex items-center gap-3">
        <div class="flex-1">
          <textarea
            v-model="commentText"
            placeholder="發表評論..."
            rows="1"
            class="w-full px-4 py-2 bg-gray-700 text-white rounded-full border border-[rgba(30,144,255,0.3)] focus:border-blue-400 focus:outline-none resize-none"
            @keyup.enter.prevent="handleSendComment"
          ></textarea>
        </div>

        <!-- 操作按鈕 -->
        <div class="flex items-center gap-2">
          <button
            @click="handleLike"
            :class="[
              'flex items-center gap-1 px-3 py-2 rounded-full transition-colors duration-300',
              isLiked
                ? 'bg-blue-500 text-white hover:bg-blue-600'
                : 'text-blue-400 border border-blue-400 hover:bg-blue-500/10',
            ]"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              :class="['h-4 w-4', isLiked ? 'fill-current' : '']"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
              />
            </svg>
            <span class="text-sm">{{ postData?.collect_count || 0 }}</span>
          </button>

          <button
            @click="handleShare"
            class="flex items-center gap-1 px-3 py-2 rounded-full text-green-400 border border-green-400 hover:bg-green-500/10 transition-colors duration-300"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"
              />
            </svg>
          </button>

          <button
            @click="handleSendComment"
            class="px-4 py-2 bg-gradient-to-r from-blue-500 to-cyan-400 text-white rounded-full hover:shadow-lg transition-all duration-300"
          >
            發送
          </button>
        </div>
      </div>
    </div>

    <!-- 分享對話框 -->
    <div
      v-if="shareDialogVisible"
      class="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
      @click="shareDialogVisible = false"
    >
      <div class="bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4" @click.stop>
        <h3 class="text-lg font-semibold text-white mb-4">分享動態</h3>
        <div class="space-y-3">
          <button
            @click="handleCopyLink"
            class="w-full py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            複製連結
          </button>
          <button
            @click="handleShareToLine"
            class="w-full py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
          >
            分享到 LINE
          </button>
          <button
            @click="handleNativeShare"
            class="w-full py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
          >
            系統分享
          </button>
        </div>
      </div>
    </div>

    <!-- 圖片預覽對話框 -->
    <div
      v-if="imagePreviewVisible"
      class="fixed inset-0 bg-black/90 flex items-center justify-center z-50"
      @click="imagePreviewVisible = false"
    >
      <div class="relative max-w-[90vw] max-h-[90vh]">
        <img
          :src="previewImageSrc"
          class="max-w-full max-h-full object-contain"
        />
        <button
          @click="imagePreviewVisible = false"
          class="absolute top-4 right-4 bg-black/50 hover:bg-black/70 text-white rounded-full p-2 transition-colors"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import { useRoute, useRouter, useHead } from "#app";

// 路由和初始化
const route = useRoute();
const router = useRouter();

// 獲取動態ID
const messageId = computed(() => parseInt(route.params.id) || 0);

// 使用 SSR 數據獲取動態詳情
const { data: postDetailData } = await useFetch("/api/message/info", {
  method: "POST",
  body: {
    message_id: messageId.value,
  },
  default: () => ({ code: 500, data: null }),
  // 啟用 SSR 以提高 SEO 和首屏載入速度
});

// 使用 SSR 數據獲取評論列表
const { data: commentsData } = await useFetch("/api/comment/listbymessage", {
  method: "POST",
  body: {
    message_id: messageId.value,
    page: 1,
    page_nums: 50,
  },
  default: () => ({ code: 500, data: { list: [] } }),
  // 啟用 SSR 以提高 SEO 和首屏載入速度
});

// 狀態管理
const isLoading = ref(false);
const error = ref(null);
const commentText = ref("");
const showActionsMenu = ref(false);
const shareDialogVisible = ref(false);
const imagePreviewVisible = ref(false);
const previewImageSrc = ref("");

// 使用 computed 處理動態數據
const postData = computed(() => {
  const data = postDetailData.value;
  if (data?.code === 200 && data?.data) {
    return data.data;
  }
  return null;
});

// 使用 computed 處理評論數據
const comments = computed(() => {
  const data = commentsData.value;
  if (data?.code === 200 && data?.data?.list) {
    return data.data.list;
  }
  return [];
});

// 手機版檢測
const isMobile = ref(false);

// 檢測是否為手機版
const checkMobile = () => {
  isMobile.value = window.innerWidth < 768;
};

// 計算屬性
const isPaid = computed(() => postData.value?.pay_status === 1);
const isAnonymous = computed(() => postData.value?.is_anonymous === 1);
const isLiked = computed(() => postData.value?.is_collect === 1);

// 安全的 localStorage 訪問函數
const getLocalStorageItem = (key) => {
  if (typeof window !== "undefined" && window.localStorage) {
    return localStorage.getItem(key);
  }
  return null;
};

// 獲取當前用戶ID
const getCurrentUserId = () => {
  // 從localStorage獲取用戶ID
  return getLocalStorageItem("user_id");
};

// 權限檢查
const canEditOrDelete = computed(() => {
  const currentUserId = getCurrentUserId();
  return (
    String(postData.value?.user_id) === String(currentUserId) ||
    getLocalStorageItem("user_type") === "99"
  );
});

// 用戶顯示名稱
const getUserDisplayName = computed(() => {
  if (isAnonymous.value) {
    return "匿名用戶";
  } else {
    return postData.value?.user_info?.nickname || "未知用戶";
  }
});

// 格式化內容
const formatContent = computed(() => {
  if (!postData.value?.content) return "";

  try {
    const parsedContent =
      typeof postData.value.content === "string"
        ? JSON.parse(postData.value.content)
        : postData.value.content;

    return processContent(parsedContent);
  } catch (error) {
    console.error("內容解析錯誤：", error);
    return processContent(postData.value.content);
  }
});

// API調用函數
const fetchPostDetail = async () => {
  try {
    isLoading.value = true;
    error.value = null;

    // 檢查 message_id 是否有效
    if (!messageId.value || messageId.value === 0) {
      error.value = "無效的動態ID";
      return;
    }

    const response = await $fetch("/api/message/info", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        ...(getLocalStorageItem("token")
          ? { token: getLocalStorageItem("token") }
          : {}),
      },
      body: {
        message_id: messageId.value,
      },
    });

    if (response.code === 200) {
      postData.value = response.data;

      // 動態更新 SEO 信息（僅在客戶端）
      if (process.client) {
        useHead({
          title: postData.value?.title || "動態詳情",
          meta: [
            {
              name: "description",
              content: postData.value?.content?.slice(0, 155) || "動態詳情頁面",
            },
          ],
        });
      }
    } else {
      error.value = response.msg || "獲取動態詳情失敗";
    }
  } catch (err) {
    console.error("獲取動態詳情失敗:", err);
    error.value = "獲取動態詳情失敗";
  } finally {
    isLoading.value = false;
  }
};

const fetchComments = async () => {
  try {
    const response = await $fetch("/api/comment/listbymessage", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        ...(getLocalStorageItem("token")
          ? { token: getLocalStorageItem("token") }
          : {}),
      },
      body: {
        message_id: messageId.value,
        page: 1,
      },
    });

    if (response.code === 200) {
      comments.value = response.data?.list || [];
    }
  } catch (error) {
    console.error("獲取評論失敗:", error);
  }
};

// 內容處理函數
const processContent = (content) => {
  if (!Array.isArray(content)) {
    return content;
  }

  return content
    .map((item) => {
      if (item.insert) {
        // 處理圖片
        if (item.insert?.imageStamp || item.insert?.image) {
          return `<div class="image-container my-4"><img src="${
            item.insert.imageStamp || item.insert.image
          }" alt="上傳圖片" class="max-w-full h-auto rounded-lg cursor-pointer" data-preview="true" /></div>`;
        }
        // 處理YouTube影片
        else if (item.insert?.video?.includes("youtu")) {
          const videoId = getYouTubeVideoId(item.insert.video);
          return `<div class="youtube-container my-4"><iframe src="https://www.youtube.com/embed/${videoId}" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen class="w-full h-64 rounded-lg"></iframe></div>`;
        }
        // 處理文字內容
        else if (typeof item.insert === "string") {
          let formattedText = item.insert;

          // 根據attributes加入格式
          if (item.attributes) {
            if (item.attributes.size) {
              const sizeClass = getSizeClass(item.attributes.size);
              formattedText = `<span class="${sizeClass}">${formattedText}</span>`;
            }

            if (item.attributes.bold) {
              formattedText = `<strong>${formattedText}</strong>`;
            }
            if (item.attributes.italic) {
              formattedText = `<em>${formattedText}</em>`;
            }
            if (item.attributes.underline) {
              formattedText = `<u>${formattedText}</u>`;
            }
          }

          // 處理換行符
          const lines = formattedText.split("\n");
          return lines
            .map((line) => {
              if (item.attributes?.header && line.trim()) {
                const headerLevel = item.attributes.header;
                return `<h${headerLevel} class="text-white font-bold my-2">${line}</h${headerLevel}>`;
              }
              return line.trim()
                ? `<p class="text-gray-300 my-2">${line}</p>`
                : `<p class="my-2"></p>`;
            })
            .join("");
        }
      }
      return "";
    })
    .join("");
};

const getSizeClass = (size) => {
  const normalizedSize = String(size).toLowerCase();
  switch (normalizedSize) {
    case "small":
    case "0.75em":
    case "12px":
      return "text-sm";
    case "large":
    case "1.5em":
    case "18px":
      return "text-lg";
    case "huge":
    case "2.0em":
    case "2.5em":
    case "24px":
      return "text-xl";
    default:
      return "text-base";
  }
};

const getYouTubeVideoId = (url) => {
  const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
  const match = url.match(regExp);
  return match && match[2].length === 11 ? match[2] : null;
};

// 日期格式化
const formatDateTime = (dateTimeString) => {
  if (!dateTimeString) return "";

  let date;

  // 檢查是否為字符串格式的日期時間
  if (typeof dateTimeString === "string" && dateTimeString.includes("-")) {
    date = new Date(dateTimeString);
  } else {
    // 處理Unix時間戳（數字）
    date = new Date(dateTimeString * 1000);
  }

  if (isNaN(date.getTime())) {
    return "";
  }

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");

  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

// 評論相關函數
const canManageComment = (comment) => {
  const currentUserId = getCurrentUserId();
  const isAdmin = getLocalStorageItem("user_type") === "99";
  return String(comment.user_id) === String(currentUserId) || isAdmin;
};

const formatCommentContent = (comment) => {
  if (!comment) return "";

  let formattedContent = comment.replace(
    /@(\S+)/g,
    '<span class="text-blue-400">@$1</span>'
  );

  formattedContent = formattedContent.replace(/\n/g, "<br>");
  return formattedContent;
};

// 事件處理函數
const goBack = () => {
  router.back();
};

const handleEdit = () => {
  // 編輯功能，這裡可以跳轉到編輯頁面
  showActionsMenu.value = false;
  // router.push(`/message/edit/${messageId.value}`);
};

const handleDelete = async () => {
  if (!confirm("確定要刪除這條動態嗎？")) return;

  try {
    const response = await $fetch("/api/message/del", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        token: getLocalStorageItem("token"),
      },
      body: {
        message_id: messageId.value,
      },
    });

    if (response.code === 200) {
      alert("刪除成功");
      router.back();
    } else {
      alert(response.msg || "刪除失敗");
    }
  } catch (error) {
    console.error("刪除失敗:", error);
    alert("刪除失敗");
  }
  showActionsMenu.value = false;
};

const handleReport = () => {
  // 檢舉功能
  showActionsMenu.value = false;
  alert("檢舉功能開發中");
};

const handleDeleteComment = async (commentId) => {
  if (!confirm("確定要刪除這條評論嗎？")) return;

  try {
    const response = await $fetch("/api/comment/delete", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        token: getLocalStorageItem("token"),
      },
      body: {
        comment_id: commentId,
      },
    });

    if (response.code === 200) {
      alert("評論已刪除");
      await fetchComments();
    } else {
      alert(response.msg || "刪除評論失敗");
    }
  } catch (error) {
    console.error("刪除評論失敗:", error);
    alert("刪除評論失敗");
  }
};

const handleSendComment = async () => {
  if (!commentText.value.trim()) {
    alert("請輸入評論內容");
    return;
  }

  try {
    const response = await $fetch("/api/comment/add", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        token: getLocalStorageItem("token"),
      },
      body: {
        type: 1,
        message_id: messageId.value,
        comment: commentText.value,
      },
    });

    if (response.code === 200) {
      commentText.value = "";
      await fetchComments();
    } else {
      alert(response.msg || "評論發送失敗");
    }
  } catch (error) {
    console.error("評論發送失敗:", error);
    alert("評論發送失敗");
  }
};

const handleLike = async () => {
  try {
    const status = postData.value.is_collect === 1 ? 2 : 1;

    const response = await $fetch("/api/collect/collect", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        token: getLocalStorageItem("token"),
      },
      body: {
        type: 1,
        target_id: messageId.value,
        status: status,
      },
    });

    if (response.code === 200) {
      postData.value.is_collect = status === 1 ? 1 : 0;
      postData.value.collect_count =
        status === 1
          ? (postData.value.collect_count || 0) + 1
          : Math.max(0, (postData.value.collect_count || 1) - 1);
    }
  } catch (error) {
    console.error("點讚操作失敗:", error);
    alert("操作失敗");
  }
};

const handleShare = () => {
  shareDialogVisible.value = true;
};

const handleCopyLink = async () => {
  try {
    const url = `${window.location.origin}/message/${messageId.value}`;
    await navigator.clipboard.writeText(url);
    alert("已複製連結");
    shareDialogVisible.value = false;
  } catch (error) {
    console.error("複製失敗:", error);
    alert("複製失敗");
  }
};

const handleShareToLine = () => {
  const url = `${window.location.origin}/message/${messageId.value}`;
  const lineUrl = `line://msg/text/${encodeURIComponent(url)}`;
  window.open(lineUrl, "_blank");
  shareDialogVisible.value = false;
};

const handleNativeShare = () => {
  if (navigator.share) {
    const url = `${window.location.origin}/message/${messageId.value}`;
    navigator
      .share({
        title: postData.value?.title || "動態分享",
        text: `查看這個動態: ${postData.value?.title || ""}`,
        url: url,
      })
      .then(() => {
        shareDialogVisible.value = false;
      })
      .catch((error) => {
        console.error("分享失敗:", error);
      });
  } else {
    alert("您的瀏覽器不支持系統分享功能");
  }
};

const handlePurchase = async () => {
  try {
    const orderResponse = await $fetch("/api/message/createorder", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        token: getLocalStorageItem("token"),
      },
      body: {
        message_id: messageId.value,
      },
    });

    if (orderResponse.code === 200 && orderResponse.data?.order_id) {
      const paymentResponse = await $fetch("/api/pay/payment", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          token: getLocalStorageItem("token"),
        },
        body: {
          type: 2,
          order_id: orderResponse.data.order_id,
          pay_type: "",
        },
      });

      if (paymentResponse.code === 200 && paymentResponse.data?.html) {
        const paymentWindow = window.open("", "_blank");
        paymentWindow.document.write(paymentResponse.data.html);

        const checkPaymentStatus = setInterval(async () => {
          if (paymentWindow.closed) {
            clearInterval(checkPaymentStatus);
            await fetchPostDetail();
          }
        }, 1000);
      }
    }
  } catch (error) {
    console.error("支付發起失敗:", error);
    alert("支付發起失敗");
  }
};

const handleImageClick = (event) => {
  if (
    event.target.tagName === "IMG" &&
    event.target.dataset.preview === "true"
  ) {
    previewImageSrc.value = event.target.src;
    imagePreviewVisible.value = true;
  }
};

const navigateToUserProfile = (userId) => {
  // 跳轉到用戶詳情頁
};

// 點擊外部關閉選單
const handleClickOutside = (event) => {
  if (showActionsMenu.value && !event.target.closest(".relative")) {
    showActionsMenu.value = false;
  }
};

// 客戶端初始化
onMounted(() => {
  // SSR 數據已經在服務端獲取，這裡只需要客戶端特定的初始化
  console.log(
    "動態詳情頁面已載入，動態ID:",
    messageId.value,
    "評論數量:",
    comments.value.length
  );

  document.addEventListener("click", handleClickOutside);

  // 初始化手機版檢測
  if (process.client) {
    checkMobile();
    window.addEventListener("resize", checkMobile);

    // 使用新的滾動修復機制
    import("~/utils/fixDoubleScroll.js").then(({ useMobileScrollFix }) => {
      const { fixSpecialPageScroll } = useMobileScrollFix({
        pageType: "message",
        autoFix: true,
      });

      // 確保評論框正確定位
      nextTick(() => {
        fixSpecialPageScroll("message");
      });
    });
  }
});

onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
  if (process.client) {
    window.removeEventListener("resize", checkMobile);
    // 滾動修復將由插件自動處理清理
  }
});

// SEO配置 - 先設置基本值，避免 SSR 錯誤
useHead({
  title: "動態詳情",
  meta: [{ name: "description", content: "動態詳情頁面" }],
});
</script>

<style scoped>
/* 底部評論列位置 */
.mobile-bottom-comment {
  bottom: 0;
  left: 0;
  right: 0;
}

@media (max-width: 767px) {
  .mobile-bottom-comment {
    bottom: 4rem; /* 64px - 底部導航列的高度 */
  }
}

@media (min-width: 768px) {
  .mobile-bottom-comment {
    left: 16rem; /* 側邊欄寬度 */
    right: 0;
  }
}

/* 修復雙重滾動問題 - 通過JavaScript處理 */

/* 內容樣式 */
.content-wrapper {
  line-height: 1.6;
}

.content-wrapper img {
  max-width: 100%;
  max-height: 400px; /* 限制圖片最大高度 */
  height: auto;
  border-radius: 8px;
  margin: 16px 0;
  object-fit: contain; /* 保持圖片比例 */
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.content-wrapper p {
  margin: 8px 0;
  color: #d1d5db;
}

.content-wrapper h1,
.content-wrapper h2,
.content-wrapper h3,
.content-wrapper h4,
.content-wrapper h5,
.content-wrapper h6 {
  color: #ffffff;
  font-weight: bold;
  margin: 16px 0 8px 0;
}

.content-wrapper strong {
  font-weight: bold;
  color: #ffffff;
}

.content-wrapper em {
  font-style: italic;
}

.content-wrapper u {
  text-decoration: underline;
}

.image-container {
  text-align: center;
}

.image-container img {
  max-height: 300px; /* 確保圖片容器中的圖片也有高度限制 */
  object-fit: contain;
}

.youtube-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

.youtube-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* 載入動畫 */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
