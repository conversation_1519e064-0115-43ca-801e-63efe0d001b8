<template>
    <div class="relative group">
      <img 
  :src="card?.image || '/images/placeholder.jpg'" 
  :alt="card?.name || '卡片圖片'" 
  class="w-full rounded-lg cursor-pointer" 
  @click="$emit('view-details', card)"
/>
      <div 
        class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
      >
        <button 
          v-if="inDeck"
          class="bg-red-600 rounded-full w-8 h-8 flex items-center justify-center hover:bg-red-700"
          @click="$emit('remove-card')"
        >
          <component :is="X" :size="16" />
        </button>
        <button 
          v-else
          class="bg-green-600 rounded-full w-8 h-8 flex items-center justify-center hover:bg-green-700"
          @click="$emit('add-card', card)"
        >
          <span class="text-white text-xl">+</span>
        </button>
      </div>
      <div class="flex justify-between items-center mt-1">
        <p class="truncate">{{ card.name }}</p>
        <button 
          class="text-blue-400 hover:text-blue-300"
          @click="$emit('view-details', card)"
        >
          <component :is="Info" :size="16" />
        </button>
      </div>
    </div>
  </template>
  
  <script setup>
  import { Info, X } from 'lucide-vue-next';
  
  defineProps({
    card: Object,
    inDeck: Boolean
  });
  
  defineEmits(['view-details', 'add-card', 'remove-card']);
  </script>
  