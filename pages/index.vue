<template>
  <div class="p-4 min-h-screen">
    <!-- 麵包屑導航 -->
    <div class="container mx-auto px-0 py-2 max-w-7xl">
      <h1
        class="text-2xl font-bold text-white bg-gradient-to-r from-blue-500 to-cyan-400 bg-clip-text text-transparent"
      >
        遊戲王資訊站
      </h1>
    </div>

    <div class="container mx-auto px-0 py-4 max-w-7xl">
      <!-- 頂部輪播新聞區塊 -->
      <div class="mb-8">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-xl font-bold">焦點新聞</h2>
          <a
            href="/message?news=1"
            class="text-blue-500 hover:text-blue-400 text-sm transition-colors duration-200"
          >
            查看全部
          </a>
        </div>
        <div
          class="bg-gray-800 rounded-lg overflow-hidden shadow-lg border border-[rgba(30,144,255,0.15)]"
        >
          <div
            class="relative h-64 md:h-64 bg-gradient-to-r from-[#061224] to-[#0c2442]"
          >
            <!-- 新聞輪播 -->
            <div
              v-for="(news, index) in newsItems"
              :key="news.id"
              class="absolute inset-0 transition-opacity duration-500 cursor-pointer"
              :class="
                index === currentNewsIndex
                  ? 'opacity-100'
                  : 'opacity-0 pointer-events-none'
              "
              @click="viewNewsDetails(news.id)"
            >
              <!-- 移动端布局 -->
              <div class="flex flex-col md:flex-row h-full">
                <!-- 圖片 -->
                <div
                  class="w-full md:w-1/2 h-1/2 md:h-full relative overflow-hidden"
                >
                  <img
                    :src="news.image"
                    :alt="news.title"
                    class="w-full h-full object-contain"
                    @error="handleImageError"
                    @load="handleImageLoad"
                  />
                  <div
                    class="absolute inset-0 bg-gradient-to-b md:bg-gradient-to-r from-transparent to-[#0c2442] opacity-30"
                  ></div>
                </div>

                <!-- 文字 -->
                <div class="w-full md:w-1/2 p-4 md:p-6 flex items-center">
                  <div
                    class="bg-[rgba(0,0,0,0.5)] p-4 md:p-5 rounded-lg backdrop-blur-sm border border-[rgba(30,144,255,0.2)] w-full"
                  >
                    <h3 class="text-lg md:text-xl font-bold mb-2 md:mb-3">
                      {{ news.title }}
                    </h3>
                    <div class="news-content-preview">
                      <p
                        class="text-gray-300 text-sm mb-2 md:mb-3 line-clamp-3 md:line-clamp-5"
                      >
                        {{ news.content }}
                      </p>
                    </div>
                    <div class="flex justify-between items-center">
                      <p class="text-blue-400 text-xs">
                        {{ formatDate(news.create_time) }}
                      </p>
                      <span
                        class="text-gray-400 text-xs hover:text-blue-400 cursor-pointer"
                        >繼續閱讀</span
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 無數據狀態 -->
            <div
              v-if="newsItems.length === 0"
              class="absolute inset-0 flex items-center justify-center"
            >
              <div class="p-4 md:p-6 text-center max-w-2xl">
                <div
                  class="bg-[rgba(0,0,0,0.5)] p-4 rounded-lg backdrop-blur-sm border border-[rgba(30,144,255,0.2)]"
                >
                  <h3 class="text-lg md:text-xl font-bold mb-2">最新動態</h3>
                  <p class="text-gray-300 mb-4">正在加載最新動態...</p>
                </div>
              </div>
            </div>

            <!-- 左右箭頭 -->
            <button
              @click.stop="prevNews"
              class="absolute left-2 md:left-4 top-1/2 -translate-y-1/2 bg-gray-800/80 hover:bg-gray-700/80 p-1 md:p-2 rounded-full transition-colors duration-200 z-10"
              v-if="newsItems.length > 1"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 md:h-6 md:w-6 text-white"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>

            <button
              @click.stop="nextNews"
              class="absolute right-2 md:right-4 top-1/2 -translate-y-1/2 bg-gray-800/80 hover:bg-gray-700/80 p-1 md:p-2 rounded-full transition-colors duration-200 z-10"
              v-if="newsItems.length > 1"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 md:h-6 md:w-6 text-white"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>

            <!-- 導航指示器 -->
            <div
              class="absolute bottom-2 md:bottom-4 left-0 right-0 flex justify-center space-x-1 md:space-x-2 z-10"
            >
              <button
                v-for="(news, index) in newsItems"
                :key="index"
                @click.stop="goToNews(index)"
                class="w-1.5 h-1.5 md:w-2 md:h-2 rounded-full transition-colors duration-200"
                :class="
                  currentNewsIndex === index
                    ? 'bg-blue-500'
                    : 'bg-gray-400 hover:bg-gray-300'
                "
              ></button>
            </div>
          </div>
        </div>
      </div>

      <!-- 近期發售系列區塊 -->
      <div class="mb-6">
        <div class="flex items-center justify-between mb-2">
          <h2 class="text-xl font-bold">近期發售系列</h2>
          <NuxtLink
            to="/series"
            class="text-blue-500 hover:text-blue-400 text-sm transition-colors duration-200"
          >
            查看全部
          </NuxtLink>
        </div>

        <div class="relative">
          <!-- 左箭頭 -->
          <button
            @click="prevSeriesPage"
            class="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-gray-800/80 hover:bg-gray-700/80 p-1 md:p-2 rounded-r-lg transition-colors duration-200"
            :class="{
              'opacity-50 cursor-not-allowed': currentSeriesPage === 0,
            }"
            :disabled="currentSeriesPage === 0"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 md:h-6 md:w-6 text-white"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </button>

          <div
            class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-3 px-8"
          >
            <div
              v-for="series in displayedSeries"
              :key="series.series_id"
              class="card-container"
              @click="viewSeriesDetails(series.series_id)"
            >
              <div class="image-container">
                <img
                  :src="series.goods_thumb"
                  :alt="series.title"
                  class="card-image"
                  @error="handleImageError"
                  @load="handleImageLoad"
                />
                <div class="image-overlay"></div>
                <div class="title-container">
                  <h3 class="card-title" :title="series.title">
                    {{ series.title }}
                  </h3>
                </div>
              </div>

              <div class="card-info">
                <p class="date-info">
                  <span class="date-label">發售日期：</span>
                  <span class="date-value">{{
                    formatDate(series.create_time)
                  }}</span>
                </p>
              </div>
            </div>

            <!-- 空狀態 -->
            <div
              v-if="recentSeries.length === 0"
              class="col-span-full flex flex-col items-center justify-center py-10 bg-gray-800/50 rounded-lg border border-dashed border-gray-700"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-8 w-8 md:h-10 md:w-10 text-gray-500 mb-3"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4"
                />
              </svg>
              <p class="text-gray-500">正在加載系列數據...</p>
            </div>
          </div>

          <!-- 右箭頭 -->
          <button
            @click="nextSeriesPage"
            class="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-gray-800/80 hover:bg-gray-700/80 p-1 md:p-2 rounded-l-lg transition-colors duration-200"
            :class="{
              'opacity-50 cursor-not-allowed':
                currentSeriesPage >=
                Math.ceil(recentSeries.length / seriesPageSize) - 1,
            }"
            :disabled="
              currentSeriesPage >=
              Math.ceil(recentSeries.length / seriesPageSize) - 1
            "
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 md:h-6 md:w-6 text-white"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 5l7 7-7 7"
              />
            </svg>
          </button>

          <!-- 頁面指示器 -->
          <div class="flex justify-center mt-2 md:mt-3 space-x-1">
            <button
              v-for="page in Math.ceil(recentSeries.length / seriesPageSize)"
              :key="page"
              @click="currentSeriesPage = page - 1"
              class="w-1.5 h-1.5 md:w-2 md:h-2 rounded-full transition-colors duration-200"
              :class="
                currentSeriesPage === page - 1
                  ? 'bg-blue-500'
                  : 'bg-gray-400 hover:bg-gray-300'
              "
            ></button>
          </div>
        </div>
      </div>

      <!-- 近期上位牌組區塊 -->
      <div class="mb-8">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-xl font-bold">近期上位牌組</h2>
          <NuxtLink
            to="/Goodsgroup"
            class="text-blue-500 hover:text-blue-400 text-sm transition-colors duration-200"
          >
            查看全部
          </NuxtLink>
        </div>

        <div class="relative">
          <!-- 左箭頭 -->
          <button
            @click="prevDeckPage"
            class="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-gray-800/80 hover:bg-gray-700/80 p-1 md:p-2 rounded-r-lg transition-colors duration-200"
            :class="{ 'opacity-50 cursor-not-allowed': currentPage === 0 }"
            :disabled="currentPage === 0"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 md:h-6 md:w-6 text-white"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </button>

          <!-- 上位牌組網格 -->
          <div
            class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-3 px-8"
          >
            <div
              v-for="(deck, index) in displayedDecks"
              :key="deck.group_id || index"
              @click="viewDeckDetails(deck.group_id)"
              class="deck-container"
            >
              <!-- 牌組圖片 -->
              <div class="deck-image-container">
                <img
                  :src="deck.image"
                  :alt="deck.title"
                  class="deck-image"
                  @error="handleImageError"
                  @load="handleImageLoad"
                />
                <div class="deck-image-overlay"></div>
                <div class="deck-title-container">
                  <h3 class="deck-title" :title="deck.title">
                    {{ deck.title }}
                  </h3>
                </div>
              </div>

              <!-- 牌組資訊 -->
              <div class="deck-info">
                <!-- 說明文字區域 -->
                <div class="deck-desc-container">
                  <p class="deck-desc" :title="deck.desc">
                    {{ deck.desc || "暫無說明" }}
                  </p>
                </div>

                <!-- 比賽日期區域 -->
                <div class="deck-date-container">
                  <span class="deck-date-label">比賽日期</span>
                  <span class="deck-date">{{
                    formatDate(deck.competition_time)
                  }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 右箭頭 -->
          <button
            @click="nextDeckPage"
            class="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-gray-800/80 hover:bg-gray-700/80 p-1 md:p-2 rounded-l-lg transition-colors duration-200"
            :class="{
              'opacity-50 cursor-not-allowed':
                currentPage >= Math.ceil(topDecks.length / pageSize) - 1,
            }"
            :disabled="currentPage >= Math.ceil(topDecks.length / pageSize) - 1"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 md:h-6 md:w-6 text-white"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 5l7 7-7 7"
              />
            </svg>
          </button>

          <!-- 頁面指示器 -->
          <div class="flex justify-center mt-2 md:mt-3 space-x-1">
            <button
              v-for="page in Math.ceil(topDecks.length / pageSize)"
              :key="page"
              @click="currentPage = page - 1"
              class="w-1.5 h-1.5 md:w-2 md:h-2 rounded-full transition-colors duration-200"
              :class="
                currentPage === page - 1
                  ? 'bg-blue-500'
                  : 'bg-gray-400 hover:bg-gray-300'
              "
            ></button>
          </div>
        </div>
      </div>

      <!-- 熱門動態區塊 -->
      <div class="mb-8">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-xl font-bold">熱門動態</h2>
          <NuxtLink
            to="/social"
            class="text-blue-500 hover:text-blue-400 text-sm transition-colors duration-200"
          >
            查看全部
          </NuxtLink>
        </div>
        <div class="space-y-4">
          <div
            v-for="post in socialPosts"
            :key="post.id"
            class="bg-gray-800 rounded-lg p-4 shadow-lg hover:shadow-xl transition-all duration-300 border border-[rgba(30,144,255,0.15)] cursor-pointer"
            @click="viewPostDetails(post.message_id)"
          >
            <div class="flex items-start">
              <div
                class="w-10 h-10 bg-[rgba(30,144,255,0.15)] rounded-full overflow-hidden border border-[rgba(30,144,255,0.3)]"
              >
                <img
                  v-if="post.userAvatar"
                  :src="post.userAvatar"
                  :alt="post.user"
                  class="w-full h-full object-cover"
                />
                <div
                  v-else
                  class="w-full h-full flex items-center justify-center"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-6 w-6 text-[#1e90ff]"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                    />
                  </svg>
                </div>
              </div>
              <div class="ml-3 flex-1">
                <div class="flex justify-between">
                  <h4 class="font-bold text-white">{{ post.user }}</h4>
                  <span class="text-xs text-gray-400">{{ post.time }}</span>
                </div>
                <h5
                  v-if="post.title"
                  class="text-blue-400 font-semibold mt-1 mb-2"
                >
                  {{ post.title }}
                </h5>
                <p class="text-gray-300 mt-1 mb-3">{{ post.content }}</p>
                <div
                  v-if="post.image"
                  class="mt-2 mb-3 rounded-lg overflow-hidden"
                >
                  <img
                    :src="post.image"
                    :alt="post.user"
                    class="w-full max-h-80 object-cover"
                  />
                </div>
                <div
                  class="flex items-center justify-between mt-2 pt-2 border-t border-[rgba(30,144,255,0.15)]"
                >
                  <div class="flex items-center space-x-4">
                    <button
                      class="flex items-center text-gray-400 hover:text-red-400 transition-colors duration-200"
                      @click.stop
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-5 w-5 mr-1"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                        />
                      </svg>
                      <span>{{ post.likes }}</span>
                    </button>
                    <button
                      class="flex items-center text-gray-400 hover:text-blue-400 transition-colors duration-200"
                      @click.stop
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-5 w-5 mr-1"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                        />
                      </svg>
                      <span>{{ post.comments }}</span>
                    </button>
                  </div>
                  <button
                    class="text-gray-400 hover:text-blue-400 transition-colors duration-200"
                    @click.stop
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 空狀態 -->
          <div
            v-if="socialPosts.length === 0"
            class="bg-gray-800 rounded-lg p-8 text-center border border-[rgba(30,144,255,0.15)]"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-12 w-12 text-gray-500 mx-auto mb-3"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
              />
            </svg>
            <p class="text-gray-500">正在載入熱門動態...</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, onUnmounted, nextTick } from "vue";
import { useHead } from "#app";
import { useApi } from "~/composables/useApi";

useHead({
  title: "遊戲王卡牌構建器 - 首頁",
  meta: [
    {
      name: "description",
      content: "遊戲王卡牌構建與分享平台，提供最新系列、上位牌組以及社區動態",
    },
    { name: "keywords", content: "遊戲王,卡牌,牌組,決鬥,社區" },
  ],
});

const api = useApi();

// 使用 Nuxt 3 的 useFetch 進行 SSR 數據獲取
const { data: topDecksData } = await useFetch(
  "/api/Goodsgroup/getlatesttopdecks",
  {
    method: "POST",
    body: { game_id: 1 },
    default: () => ({ code: 500, data: [] }),
    // 啟用 SSR 以提高首屏載入速度和 SEO
  }
);

const { data: seriesData } = await useFetch("/api/series/list", {
  method: "POST",
  body: {
    new: true,
    newnum: true,
    page: 1,
    page_nums: 10,
    type: 1,
    game_id: 1,
  },
  default: () => ({ code: 500, data: { list: [] } }),
  // 啟用 SSR 以提高首屏載入速度和 SEO
});

const { data: newsData } = await useFetch("/api/message/list", {
  method: "POST",
  body: {
    news: 1,
    page: 1,
    page_nums: 5,
    game_id: 1,
  },
  default: () => ({ code: 500, data: { list: [] } }),
  // 啟用 SSR 以提高首屏載入速度和 SEO
});

// 添加熱門動態的API調用
const { data: socialData } = await useFetch("/api/message/list", {
  method: "POST",
  body: {
    page: 1,
    page_nums: 5,
    game_id: 1,
  },
  default: () => ({ code: 500, data: { list: [] } }),
  // 啟用 SSR 以提高首屏載入速度和 SEO
});

// 改進圖片錯誤處理
const handleImageError = (event) => {
  console.log("圖片加載錯誤:", event.target.src);
  event.target.src = "/images/placeholder.jpg";
  event.target.classList.add("loaded");
};

// 添加圖片加載完成處理
const handleImageLoad = (event) => {
  console.log("圖片加載成功:", event.target.src);
  event.target.classList.add("loaded");
};

// 修改圖片元素，添加加載事件監聽
const addImageLoadListener = (img) => {
  img.addEventListener("load", handleImageLoad);
};

// 模擬系列詳情頁跳轉
const viewSeriesDetails = (id) => {
  navigateTo(`/series/${id}`);
};

// 初始化數據 - 使用響應式數據
const topDecks = computed(() => {
  const data = topDecksData.value;
  if (data?.code === 200 && data?.data) {
    return data.data
      .map((deck) => ({
        ...deck,
        title: deck.title || "未知牌組",
        desc: deck.desc || "暫無說明",
        image: deck.image || "/images/placeholder.jpg",
        competition_time:
          deck.competition_time || Math.floor(Date.now() / 1000),
      }))
      .sort((a, b) => b.competition_time - a.competition_time);
  }
  return [];
});

const recentSeries = computed(() => {
  const data = seriesData.value;
  if (data?.code === 200 && data?.data?.list) {
    return data.data.list.sort((a, b) => b.create_time - a.create_time);
  }
  return [];
});

const newsItems = computed(() => {
  const data = newsData.value;
  if (data?.code === 200 && data?.data?.list) {
    return data.data.list.map((item) => {
      let mainImage = "/images/news-placeholder.jpg";
      if (item.images) {
        const imageArray = item.images.split(",");
        if (imageArray.length > 0 && imageArray[0].trim() !== "") {
          mainImage = imageArray[0].trim();
        }
      }
      return {
        ...item,
        id: item.message_id,
        title: item.title || "未知新聞",
        content: item.content || "暫無內容",
        image: mainImage,
        imageList: item.images
          ? item.images.split(",").filter((url) => url.trim() !== "")
          : [],
        create_time: item.create_time || Math.floor(Date.now() / 1000),
      };
    });
  }
  return [];
});

const currentPage = ref(0);
const pageSize = ref(1);

// 計算當前應該顯示的牌組
const displayedDecks = computed(() => {
  const start = currentPage.value * pageSize.value;
  const end = start + pageSize.value;
  return topDecks.value.slice(start, end);
});

// 上一頁
const prevDeckPage = () => {
  if (currentPage.value > 0) {
    currentPage.value--;
  }
};

// 下一頁
const nextDeckPage = () => {
  if (
    currentPage.value <
    Math.ceil(topDecks.value.length / pageSize.value) - 1
  ) {
    currentPage.value++;
  }
};

// 客戶端數據刷新函數（可選）
const refreshTopDecks = async () => {
  try {
    const result = await $fetch("/api/Goodsgroup/getlatesttopdecks", {
      method: "POST",
      body: { game_id: 1 },
    });

    if (result.code === 200 && result.data) {
      topDecks.value = result.data
        .map((deck) => ({
          ...deck,
          title: deck.title || "未知牌組",
          desc: deck.desc || "暫無說明",
          image: deck.image || "/images/placeholder.jpg",
          competition_time:
            deck.competition_time || Math.floor(Date.now() / 1000),
        }))
        .sort((a, b) => b.competition_time - a.competition_time);
    }
  } catch (error) {
    console.error("刷新上位牌組失敗:", error);
  }
};

// 查看牌組詳情
const viewDeckDetails = (groupId) => {
  navigateTo(`/Goodsgroup/${groupId}`);
};

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return "未知日期";

  let date;

  // 檢查是否為字符串格式的日期時間
  if (typeof timestamp === "string" && timestamp.includes("-")) {
    date = new Date(timestamp);
  } else {
    // 處理Unix時間戳（數字）
    date = new Date(timestamp * 1000);
  }

  // 檢查是否為有效日期
  if (isNaN(date.getTime())) {
    return "未知日期";
  }

  try {
    return date.toLocaleDateString("zh-TW", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  } catch (error) {
    console.error("日期格式化錯誤:", error);
    // 手動格式化，避免Invalid Date
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${year}年${month}月${day}日`;
  }
};

// 系列分頁相關
const currentSeriesPage = ref(0);
const seriesPageSize = ref(1); // 移动端只显示1个

// 計算當前應該顯示的系列
const displayedSeries = computed(() => {
  const start = currentSeriesPage.value * seriesPageSize.value;
  const end = start + seriesPageSize.value;
  return recentSeries.value.slice(start, end);
});

// 上一頁 - 系列
const prevSeriesPage = () => {
  if (currentSeriesPage.value > 0) {
    currentSeriesPage.value--;
  }
};

// 下一頁 - 系列
const nextSeriesPage = () => {
  if (
    currentSeriesPage.value <
    Math.ceil(recentSeries.value.length / seriesPageSize.value) - 1
  ) {
    currentSeriesPage.value++;
  }
};

// 新聞輪播相關
const currentNewsIndex = ref(0);

// 前往新聞詳情頁
const viewNewsDetails = (newsId) => {
  navigateTo(`/message/${newsId}`);
};

// 切換到前一條新聞
const prevNews = () => {
  if (currentNewsIndex.value > 0) {
    currentNewsIndex.value--;
  } else {
    currentNewsIndex.value = newsItems.value.length - 1;
  }
};

// 切換到後一條新聞
const nextNews = () => {
  if (currentNewsIndex.value < newsItems.value.length - 1) {
    currentNewsIndex.value++;
  } else {
    currentNewsIndex.value = 0;
  }
};

// 切換到指定新聞
const goToNews = (index) => {
  currentNewsIndex.value = index;
};

// 社交動態數據 - 使用computed從API獲取
const socialPosts = computed(() => {
  const data = socialData.value;
  if (data?.code === 200 && data?.data?.list) {
    return data.data.list.map((post) => ({
      ...post,
      id: post.message_id,
      user: post.user_info?.nickname || "匿名用戶",
      userAvatar: post.user_info?.headimg || null,
      content: formatPostContent(post.content),
      image: extractImageFromContent(post.content),
      timeAgo: formatTimeAgo(post.create_time),
    }));
  }
  return [];
});

// 格式化動態內容
const formatPostContent = (content) => {
  if (!content) return "暫無內容";

  try {
    // 如果是JSON格式的內容，解析並提取文字
    if (typeof content === "string" && content.startsWith("[")) {
      const parsedContent = JSON.parse(content);
      return extractTextFromContent(parsedContent);
    }

    // 如果是普通字符串，直接返回（限制長度）
    const text = typeof content === "string" ? content : String(content);
    return text.length > 150 ? text.substring(0, 150) + "..." : text;
  } catch (error) {
    console.error("解析動態內容失敗:", error);
    return "內容解析錯誤";
  }
};

// 從內容中提取文字
const extractTextFromContent = (contentArray) => {
  if (!Array.isArray(contentArray)) return "暫無內容";

  let textContent = "";
  contentArray.forEach((item) => {
    if (item.insert && typeof item.insert === "string") {
      textContent += item.insert;
    }
  });

  return textContent.length > 150
    ? textContent.substring(0, 150) + "..."
    : textContent || "暫無內容";
};

// 從內容中提取圖片
const extractImageFromContent = (content) => {
  if (!content) return null;

  try {
    if (typeof content === "string" && content.startsWith("[")) {
      const parsedContent = JSON.parse(content);

      for (const item of parsedContent) {
        if (item.insert && (item.insert.imageStamp || item.insert.image)) {
          return item.insert.imageStamp || item.insert.image;
        }
      }
    }
  } catch (error) {
    console.error("提取圖片失敗:", error);
  }

  return null;
};

// 格式化時間為相對時間
const formatTimeAgo = (timestamp) => {
  if (!timestamp) return "未知時間";

  try {
    let date;

    // 檢查是否為字符串格式的日期時間
    if (typeof timestamp === "string" && timestamp.includes("-")) {
      date = new Date(timestamp);
    } else {
      // 處理Unix時間戳（數字）
      date = new Date(timestamp * 1000);
    }

    if (isNaN(date.getTime())) {
      return "未知時間";
    }

    const now = new Date();
    const diffInMinutes = Math.floor((now - date) / (1000 * 60));

    if (diffInMinutes < 1) return "剛剛";
    if (diffInMinutes < 60) return `${diffInMinutes}分鐘前`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}小時前`;

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}天前`;

    return date.toLocaleDateString("zh-TW");
  } catch (error) {
    console.error("時間格式化錯誤:", error);
    return "時間錯誤";
  }
};

// 點擊動態跳轉到詳情頁
const viewPostDetails = (messageId) => {
  if (messageId) {
    navigateTo(`/message/${messageId}`);
  }
};

// 啟動輪播新聞自動切換
let newsInterval;
const startNewsAutoplay = () => {
  if (newsItems.value.length > 1) {
    newsInterval = setInterval(() => {
      nextNews();
    }, 5000); // 每5秒切換一次
  }
};

// 停止輪播新聞自動切換
const stopNewsAutoplay = () => {
  clearInterval(newsInterval);
};

// 監聽窗口大小變化
const updatePageSize = () => {
  // 確保在客戶端執行
  if (process.client) {
    if (window.innerWidth < 768) {
      pageSize.value = 1;
      seriesPageSize.value = 1;
    } else {
      pageSize.value = 5;
      seriesPageSize.value = 4;
    }
  }
};

// 監聽窗口大小變化
onMounted(() => {
  // 只在客戶端執行必要的初始化
  // 使用 nextTick 確保 DOM 已經渲染完成
  nextTick(() => {
    const images = document.querySelectorAll("img");
    images.forEach(addImageLoadListener);
  });

  // 啟動新聞自動輪播
  startNewsAutoplay();

  updatePageSize();
  window.addEventListener("resize", updatePageSize);
});

onUnmounted(() => {
  stopNewsAutoplay();
  window.removeEventListener("resize", updatePageSize);
});
</script>

<style scoped>
/* 圖片加載動畫 */
img {
  transition: opacity 0.3s ease-in-out;
  opacity: 1;
}

img[loading="lazy"] {
  opacity: 0;
}

img.loaded {
  opacity: 1;
}

/* 近期發售系列區塊樣式 */
.card-container {
  height: 190px;
  background-color: rgb(31, 41, 55);
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid rgba(30, 144, 255, 0.15);
  display: flex;
  flex-direction: column;
}

.card-container:hover {
  transform: translateY(-0.25rem);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.image-container {
  position: relative;
  height: 160px;
  overflow: hidden;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.card-container:hover .card-image {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  inset: 0;
  background-image: linear-gradient(to top, rgb(17, 24, 39), transparent);
  opacity: 0.7;
}

.title-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(17, 24, 39, 0.7);
  padding: 0.25rem 0.5rem;
  backdrop-filter: blur(4px);
}

.card-title {
  font-weight: bold;
  color: white;
  font-size: 0.875rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-info {
  padding: 0.5rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.date-info {
  font-size: 0.75rem;
  color: rgb(156, 163, 175);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.date-label {
  color: rgb(96, 165, 250);
}

.date-value {
  color: #1e90ff;
  font-weight: 500;
}

/* 近期上位牌組區塊樣式 */
.deck-container {
  height: 220px;
  background-color: rgb(31, 41, 55);
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid rgba(30, 144, 255, 0.15);
  display: flex;
  flex-direction: column;
  position: relative;
}

.deck-container:hover {
  transform: translateY(-0.25rem);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.deck-image-container {
  position: relative;
  height: 160px;
  overflow: hidden;
}

.deck-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.deck-container:hover .deck-image {
  transform: scale(1.05);
}

.deck-image-overlay {
  position: absolute;
  inset: 0;
  background-image: linear-gradient(to top, rgb(17, 24, 39), transparent);
  opacity: 0.7;
}

.deck-title-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(17, 24, 39, 0.7);
  padding: 0.25rem 0.5rem;
  backdrop-filter: blur(4px);
}

.deck-title {
  font-weight: bold;
  color: white;
  font-size: 0.875rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.deck-info {
  padding: 0.5rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.deck-desc-container {
  flex-grow: 1;
  overflow: auto;
}

.deck-desc {
  color: rgb(209, 213, 219);
  font-size: 0.75rem;
  line-height: 1.25rem;
  margin-bottom: 0.5rem;
}

.deck-date-container {
  margin-top: auto;
  padding-top: 0.25rem;
  border-top: 1px solid rgba(30, 144, 255, 0.1);
}

.deck-date-label {
  display: block;
  font-size: 0.75rem;
  color: rgb(96, 165, 250);
}

.deck-date {
  color: #1e90ff;
  font-weight: 500;
}

.deck-hover-overlay {
  position: absolute;
  inset: 0;
  background-color: rgba(59, 130, 246, 0);
  transition: background-color 0.3s ease;
}

.deck-container:hover .deck-hover-overlay {
  background-color: rgba(59, 130, 246, 0.05);
}

/* 自定義滾動條樣式 */
.deck-desc-container {
  scrollbar-width: thin;
  scrollbar-color: rgba(30, 144, 255, 0.3) transparent;
}

.deck-desc-container::-webkit-scrollbar {
  width: 4px;
}

.deck-desc-container::-webkit-scrollbar-track {
  background: transparent;
}

.deck-desc-container::-webkit-scrollbar-thumb {
  background-color: rgba(30, 144, 255, 0.3);
  border-radius: 2px;
}

.news-content-preview {
  min-height: 100px;
  max-height: 120px;
  overflow: hidden;
  position: relative;
}

/* 確保line-clamp在所有瀏覽器都有效 */
.line-clamp-5 {
  display: -webkit-box;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
