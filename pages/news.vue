<template>
  <div class="mobile-safe-page">
    <!-- 手機版頭部距離 -->
    <div class="mobile-header-spacer md:hidden"></div>

    <!-- 頁面標題 -->
    <div class="page-container">
      <div class="flex items-center mb-6">
        <!-- 返回按鈕 -->
        <button
          @click="goBack"
          class="mr-3 p-2 rounded-full hover:bg-[rgba(30,144,255,0.1)] transition-colors duration-200 group"
          title="返回首頁"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 text-gray-400 group-hover:text-blue-400 transition-colors duration-200"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M10 19l-7-7m0 0l7-7m-7 7h18"
            />
          </svg>
        </button>

        <h1 class="page-title">焦點新聞</h1>
      </div>
    </div>

    <div class="page-container">
      <!-- 載入中提示 -->
      <div v-if="loading && !newsList.length" class="loading-container">
        <div
          class="animate-spin h-12 w-12 border-t-2 border-b-2 border-blue-500 rounded-full"
        ></div>
        <p class="mt-4 text-gray-400">載入中...</p>
      </div>

      <!-- 錯誤提示 -->
      <div v-else-if="error" class="empty-state">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-12 w-12 text-gray-500 mx-auto mb-3"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z"
          />
        </svg>
        <p class="text-red-400 mb-4">{{ error }}</p>
        <button
          @click="fetchNewsList"
          class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          重試
        </button>
      </div>

      <!-- 新聞列表 -->
      <div v-else class="news-list">
        <div
          v-for="news in newsList"
          :key="news.id"
          class="news-item"
          @click="viewNewsDetails(news.id)"
        >
          <!-- 桌面版佈局 -->
          <div class="hidden md:flex items-start gap-4 h-full">
            <!-- 左側圖片 -->
            <div class="flex-shrink-0">
              <div class="w-48 h-32 rounded-lg overflow-hidden bg-gray-700">
                <img
                  :src="news.image"
                  :alt="news.title"
                  class="w-full h-full object-cover hover:scale-105 transition-transform duration-200"
                  @error="handleImageError"
                />
              </div>
            </div>

            <!-- 右側內容 -->
            <div class="flex-1 min-w-0">
              <h3
                class="text-lg font-bold text-white mb-2 line-clamp-2 hover:text-blue-400 transition-colors"
              >
                {{ news.title }}
              </h3>
              <p
                class="text-gray-300 text-sm mb-3 line-clamp-3 leading-relaxed"
              >
                {{ news.content }}
              </p>
              <div class="flex justify-between items-center">
                <span class="text-blue-400 text-xs">{{
                  formatDate(news.create_time)
                }}</span>
                <span
                  class="text-gray-400 text-xs hover:text-blue-400 cursor-pointer"
                  >繼續閱讀</span
                >
              </div>
            </div>
          </div>

          <!-- 手機版佈局 -->
          <div class="md:hidden">
            <div class="flex gap-3">
              <!-- 左側內容 -->
              <div class="flex-1 min-w-0">
                <h3
                  class="text-base font-bold text-white mb-2 line-clamp-2 hover:text-blue-400 transition-colors"
                >
                  {{ news.title }}
                </h3>
                <p
                  class="text-gray-300 text-sm mb-2 line-clamp-3 leading-relaxed"
                >
                  {{ news.content }}
                </p>
                <span class="text-blue-400 text-xs">{{
                  formatDate(news.create_time)
                }}</span>
              </div>

              <!-- 右側圖片 -->
              <div class="flex-shrink-0">
                <div class="w-20 h-20 rounded-lg overflow-hidden bg-gray-700">
                  <img
                    :src="news.image"
                    :alt="news.title"
                    class="w-full h-full object-cover hover:scale-105 transition-transform duration-200"
                    @error="handleImageError"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 載入更多按鈕 -->
        <div v-if="canLoadMore" class="text-center mt-6">
          <button
            @click="loadMore"
            class="px-6 py-3 bg-gradient-to-r from-blue-500 to-cyan-400 text-white rounded-lg hover:shadow-lg transition-all duration-300 disabled:opacity-50"
            :disabled="loadingMore"
          >
            {{ loadingMore ? "載入中..." : "載入更多" }}
          </button>
        </div>

        <!-- 空狀態 -->
        <div v-if="!loading && newsList.length === 0" class="empty-state">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-12 w-12 text-gray-500 mx-auto mb-3"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3v6m0 0l-3-3m3 3l3-3"
            />
          </svg>
          <p class="text-gray-500">目前尚無新聞</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useRouter } from "#app";

// SEO 配置
useHead({
  title: "焦點新聞 - 遊戲王資訊站",
  meta: [
    { name: "description", content: "遊戲王最新焦點新聞，掌握第一手資訊" },
    { name: "keywords", content: "遊戲王,新聞,焦點,資訊" },
  ],
});

const router = useRouter();

// 使用 SSR 數據獲取新聞列表
const { data: newsData } = await useFetch("/api/message/list", {
  method: "POST",
  body: {
    news: 1,
    page: 1,
    page_nums: 10,
    game_id: 1,
  },
  default: () => ({ code: 500, data: { list: [], total_page: 1 } }),
  // 啟用 SSR 以提高首屏載入速度和 SEO
});

// 狀態管理
const loading = ref(false);
const loadingMore = ref(false);
const error = ref(null);
const currentPage = ref(1);

// 使用 computed 處理新聞數據
const newsList = computed(() => {
  const data = newsData.value;
  if (data?.code === 200 && data?.data?.list) {
    return data.data.list.map((item) => {
      // 處理圖片字段，如果是逗號分隔的多個URL，取第一個URL
      let mainImage = "/images/news-placeholder.jpg";
      if (item.images) {
        const imageArray = item.images.split(",");
        if (imageArray.length > 0 && imageArray[0].trim() !== "") {
          mainImage = imageArray[0].trim();
        }
      }

      return {
        ...item,
        id: item.message_id,
        title: item.title || "未知新聞",
        content: item.content || "暫無內容",
        image: mainImage,
        create_time: item.create_time || Math.floor(Date.now() / 1000),
      };
    });
  }
  return [];
});

const totalPages = computed(() => {
  const data = newsData.value;
  if (data?.code === 200 && data?.data?.total_page) {
    return data.data.total_page;
  }
  return 1;
});

// 計算屬性
const canLoadMore = computed(() => currentPage.value < totalPages.value);

// 圖片錯誤處理
const handleImageError = (event) => {
  if (event.target.src.includes("placeholder.jpg")) {
    // console.warn('佔位圖片載入失敗');
    return;
  }
  event.target.src = "/images/placeholder.jpg";
};

// 獲取新聞列表
const fetchNewsList = async (isLoadMore = false) => {
  try {
    if (isLoadMore) {
      loadingMore.value = true;
    } else {
      loading.value = true;
      currentPage.value = 1;
      newsList.value = [];
    }

    error.value = null;

    const response = await fetch("/api/message/list", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        news: 1,
        page: currentPage.value,
        page_nums: 10,
        game_id: 1,
      }),
    });

    const result = await response.json();

    if (result.code === 200 && result.data && result.data.list) {
      const processedNews = result.data.list.map((item) => {
        // 處理圖片字段，如果是逗號分隔的多個URL，取第一個URL
        let mainImage = "/images/news-placeholder.jpg";
        if (item.images) {
          const imageArray = item.images.split(",");
          if (imageArray.length > 0 && imageArray[0].trim() !== "") {
            mainImage = imageArray[0].trim();
          }
        }

        return {
          ...item,
          id: item.message_id,
          title: item.title || "未知新聞",
          content: item.content || "暫無內容",
          image: mainImage,
          create_time: item.create_time || Math.floor(Date.now() / 1000),
        };
      });

      if (isLoadMore) {
        newsList.value = [...newsList.value, ...processedNews];
      } else {
        newsList.value = processedNews;
      }

      totalPages.value = result.data.total_page || 1;
    } else {
      if (!isLoadMore) {
        error.value = result.msg || "獲取新聞失敗";
      }
    }
  } catch (err) {
    // console.error('獲取新聞時發生錯誤:', err);
    if (!isLoadMore) {
      error.value = "網路錯誤，請稍後再試";
    }
  } finally {
    loading.value = false;
    loadingMore.value = false;
  }
};

// 載入更多
const loadMore = async () => {
  if (loadingMore.value || currentPage.value >= totalPages.value) return;

  currentPage.value++;
  await fetchNewsList(true);
};

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return "未知日期";

  let date;

  if (typeof timestamp === "string" && timestamp.includes("-")) {
    date = new Date(timestamp);
  } else {
    date = new Date(timestamp * 1000);
  }

  if (isNaN(date.getTime())) {
    return "未知日期";
  }

  try {
    return date.toLocaleDateString("zh-TW", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  } catch (error) {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${year}年${month}月${day}日`;
  }
};

// 跳轉到新聞詳情
const viewNewsDetails = (newsId) => {
  navigateTo(`/message/${newsId}`);
};

// 返回首頁
const goBack = () => {
  router.push("/");
};

// 客戶端初始化
onMounted(() => {
  // SSR 數據已經在服務端獲取，這裡只需要客戶端特定的初始化
  console.log("新聞頁面已載入，新聞數量:", newsList.value.length);
});
</script>

<style scoped>
/* 頁面整體佈局 */
.mobile-safe-page {
  min-height: 100vh;
  width: 100%;
  overflow-x: hidden;
  padding: 1rem;
  background: linear-gradient(to bottom, #0f1726, #0a0e18);
  color: #eee;
}

@media (max-width: 767px) {
  .mobile-safe-page {
    padding: 0.75rem;
  }
}

.mobile-header-spacer {
  height: 1rem;
}

.page-container {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 0;
  overflow-x: hidden;
}

@media (max-width: 767px) {
  .page-container {
    padding: 0 0.5rem;
  }
}

@media (min-width: 768px) {
  .page-container {
    max-width: 1200px;
    padding: 0 1rem;
  }
}

.page-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
  background: linear-gradient(to right, #3b82f6, #06b6d4);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0;
}

@media (min-width: 768px) {
  .page-title {
    font-size: 2rem;
  }
}

/* 新聞列表 */
.news-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.news-item {
  background: linear-gradient(
    145deg,
    rgba(20, 30, 48, 0.7),
    rgba(12, 20, 35, 0.7)
  );
  border-radius: 1rem;
  padding: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(30, 144, 255, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
}

.news-item:hover {
  border-color: rgba(30, 144, 255, 0.4);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.25);
  transform: translateY(-2px);
}

@media (max-width: 767px) {
  .news-item {
    padding: 0.75rem;
  }
}

/* 載入和錯誤狀態 */
.loading-container,
.empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  width: 100%;
  color: rgba(255, 255, 255, 0.3);
  text-align: center;
}

/* 文字截斷樣式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 載入動畫 */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
