<!-- pages/community.vue -->
<template>
  <div class="card-group-page container mx-auto py-4 px-4">
    <ClientOnly>
      <!-- 頁面頭部 -->
      <div class="page-header mb-4">
        <div class="header-content container mx-auto px-4">
          <h1 class="page-title">遊戲王社區</h1>
        </div>
      </div>

      <!-- 快速發文區域 - 始終顯示在頂部 -->
      <div class="quick-post-card mb-4">
        <div class="flex items-start p-3">
          <img
            :src="authStore.user?.headimg || '/images/default-avatar.png'"
            :alt="authStore.user?.nickname || '匿名用戶'"
            class="w-10 h-10 rounded-full mr-3 object-cover border border-[rgba(30,144,255,0.3)]"
            @error="handleAvatarError"
          />
          <div class="flex-grow">
            <textarea
              v-model="newPost.content"
              placeholder="分享你的遊戲王心得..."
              class="w-full p-2 bg-gray-700 rounded-lg h-16 focus:outline-none focus:ring-2 focus:ring-blue-500"
              @focus="expandPostForm"
            ></textarea>

            <!-- 展開後顯示的完整發文表單 -->
            <div v-if="showFullPostForm" class="mt-2">
              <input
                v-model="newPost.title"
                type="text"
                placeholder="標題（選填）"
                class="w-full p-2 bg-gray-700 rounded-lg mb-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />

              <div
                class="flex flex-wrap mt-2"
                v-if="imagePreviewUrls.length > 0"
              >
                <div
                  v-for="(img, index) in imagePreviewUrls"
                  :key="index"
                  class="relative mr-2 mb-2"
                >
                  <img
                    :src="img"
                    alt="預覽圖片"
                    class="h-16 w-16 object-contain rounded"
                  />
                  <button
                    @click="removeImage(index)"
                    class="absolute top-0 right-0 bg-red-500 rounded-full h-5 w-5 flex items-center justify-center text-xs"
                  >
                    ×
                  </button>
                </div>
              </div>

              <div class="flex justify-between mt-2">
                <div class="flex gap-2">
                  <label
                    class="bg-gray-700 px-3 py-1 rounded hover:bg-gray-600 cursor-pointer"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5 inline-block"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                      />
                    </svg>
                    <input
                      type="file"
                      @change="onFileSelected"
                      accept="image/*"
                      multiple
                      class="hidden"
                    />
                  </label>
                </div>
                <div>
                  <button
                    @click="cancelPost"
                    class="px-4 py-1 rounded bg-gray-700 hover:bg-gray-600 mr-2"
                  >
                    取消
                  </button>
                  <button
                    @click="submitPost"
                    class="my-deck-button px-4 py-1 rounded hover:bg-blue-700 disabled:opacity-50"
                    :disabled="!canSubmit"
                  >
                    發佈
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 搜尋区域 -->
      <div class="search-bar mb-4">
        <div class="relative w-full md:w-1/2 lg:w-1/3">
          <input
            type="text"
            v-model="searchQuery"
            placeholder="搜尋帖子..."
            class="pl-10 pr-4 py-2 w-full bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <span class="absolute left-3 top-2.5 text-gray-400">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </span>
        </div>
      </div>

      <!-- 載入中提示 -->
      <div v-if="loading && !filteredPosts.length" class="loading-container">
        <div
          class="animate-spin h-12 w-12 border-t-2 border-b-2 border-blue-500 rounded-full"
        ></div>
        <p class="mt-4 text-gray-400">載入中...</p>
      </div>

      <!-- 錯誤提示 -->
      <div v-else-if="error" class="empty-state">
        <i class="el-icon-warning-outline"></i>
        <p>{{ error }}</p>
        <button
          @click="fetchPosts"
          class="mt-4 bg-blue-600 px-4 py-2 rounded hover:bg-blue-700"
        >
          重試
        </button>
      </div>

      <!-- 帖子列表 - 線性布局，类似 X/Threads -->
      <div v-else class="posts-list">
        <div
          v-for="post in filteredPosts"
          :key="post.message_id"
          class="post-item"
        >
          <div class="p-4">
            <div class="flex">
              <!-- 用户头像 -->
              <div class="flex-shrink-0 mr-3">
                <img
                  :src="post.user_info?.headimg || '/images/default-avatar.png'"
                  :alt="post.user_info?.nickname || '匿名用戶'"
                  class="w-10 h-10 rounded-full object-cover border border-[rgba(30,144,255,0.3)]"
                  @error="handleAvatarError"
                />
              </div>

              <!-- 帖子内容 -->
              <div class="flex-grow">
                <!-- 用户信息 -->
                <div class="flex items-center mb-1">
                  <div class="font-semibold">
                    {{ post.user_info?.nickname || "匿名用戶" }}
                  </div>
                  <div class="text-gray-400 text-xs ml-2">
                    · {{ formatDate(post.create_time) }}
                  </div>
                </div>

                <!-- 桌面版布局 -->
                <div
                  class="hidden md:block cursor-pointer"
                  @click="navigateToPostDetail(post.message_id)"
                >
                  <div class="flex gap-4 min-h-[120px]">
                    <!-- 左側文字區域 -->
                    <div class="flex-1 min-w-0">
                      <!-- 帖子标题 -->
                      <h3
                        v-if="post.title"
                        class="text-base font-bold mb-2 line-clamp-2 hover:text-blue-400 transition-colors"
                      >
                        {{ post.title }}
                      </h3>

                      <!-- 帖子内容 -->
                      <div
                        class="text-sm text-gray-300 line-clamp-4 leading-relaxed"
                      >
                        {{ post.content }}
                      </div>
                    </div>

                    <!-- 右側圖片縮圖 -->
                    <div
                      v-if="post.images && post.images.length > 0"
                      class="flex-shrink-0"
                    >
                      <div
                        class="w-24 h-24 rounded-lg overflow-hidden bg-slate-700"
                        @click.stop="
                          openImageViewer(getProcessedImages(post.images)[0])
                        "
                      >
                        <img
                          :src="getProcessedImages(post.images)[0]"
                          :alt="`附圖 1`"
                          class="w-full h-full object-cover hover:scale-105 transition-transform duration-200"
                        />
                        <!-- 如果有多張圖片，顯示數量標記 -->
                        <div
                          v-if="post.images.length > 1"
                          class="absolute top-1 right-1 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded-full"
                        >
                          +{{ post.images.length - 1 }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 手機版布局（巴哈姆特風格）-->
                <div
                  class="md:hidden cursor-pointer"
                  @click="navigateToPostDetail(post.message_id)"
                >
                  <div class="flex gap-3 min-h-[100px]">
                    <!-- 左側文字區域 -->
                    <div class="flex-1 min-w-0">
                      <!-- 帖子標題 -->
                      <h3
                        v-if="post.title"
                        class="text-sm font-bold mb-2 line-clamp-2 hover:text-blue-400 transition-colors"
                      >
                        {{ post.title }}
                      </h3>

                      <!-- 帖子內容預覽 -->
                      <div
                        class="text-sm text-gray-300 line-clamp-3 leading-relaxed"
                      >
                        {{ post.content }}
                      </div>
                    </div>

                    <!-- 右側圖片縮圖 -->
                    <div
                      v-if="post.images && post.images.length > 0"
                      class="flex-shrink-0"
                    >
                      <div
                        class="w-20 h-20 rounded-lg overflow-hidden bg-slate-700"
                        @click.stop="
                          openImageViewer(getProcessedImages(post.images)[0])
                        "
                      >
                        <img
                          :src="getProcessedImages(post.images)[0]"
                          :alt="`附圖 1`"
                          class="w-full h-full object-cover hover:scale-105 transition-transform duration-200"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 帖子交互 -->
                <div class="flex text-gray-400 text-sm mt-2 pt-2">
                  <div
                    class="mr-6 flex items-center cursor-pointer hover:text-blue-400"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5 mr-1"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                      />
                    </svg>
                    {{ post.hot }}
                  </div>
                  <div
                    class="mr-6 flex items-center cursor-pointer hover:text-blue-400"
                    @click="navigateToComments(post.message_id)"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5 mr-1"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                      />
                    </svg>
                    {{ post.comment_count }}
                  </div>
                  <div
                    class="flex items-center cursor-pointer hover:text-blue-400"
                    @click="sharePost(post)"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5 mr-1"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"
                      />
                    </svg>
                    分享
                  </div>
                  <!-- 如果是帖子作者，显示编辑和删除按钮 -->
                  <template
                    v-if="post.user_info?.user_id === authStore.user?.user_id"
                  >
                    <div
                      class="flex items-center cursor-pointer hover:text-blue-400 ml-6"
                      @click="editPost(post)"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-5 w-5 mr-1"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                        />
                      </svg>
                      編輯
                    </div>
                    <div
                      class="flex items-center cursor-pointer hover:text-red-400 ml-6"
                      @click="deletePost(post.message_id)"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-5 w-5 mr-1"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                        />
                      </svg>
                      刪除
                    </div>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 載入更多按鈕 -->
      <div v-if="canLoadMore" class="text-center mt-4">
        <button
          @click="loadMore"
          class="my-deck-button px-6 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
          :disabled="loadingMore"
        >
          {{ loadingMore ? "載入中..." : "載入更多" }}
        </button>
      </div>

      <!-- 圖片查看器 -->
      <div
        v-if="showImageViewer"
        class="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50"
        @click="showImageViewer = false"
      >
        <div class="relative max-w-full max-h-full p-4">
          <img
            :src="selectedImage"
            class="max-w-full max-h-full rounded-lg shadow-xl"
            alt="查看大圖"
          />

          <!-- 圖片計數器 -->
          <div class="absolute bottom-8 left-0 right-0 text-center text-white">
            {{ currentImageIndex + 1 }} / {{ currentGalleryImages.length }}
          </div>

          <!-- 上一張/下一張按鈕 -->
          <button
            v-if="currentGalleryImages.length > 1"
            class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 p-2 rounded-full text-white"
            @click.stop="prevImage"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </button>
          <button
            v-if="currentGalleryImages.length > 1"
            class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 p-2 rounded-full text-white"
            @click.stop="nextImage"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 5l7 7-7 7"
              />
            </svg>
          </button>

          <!-- 關閉按鈕 -->
          <button
            class="absolute top-4 right-4 bg-black bg-opacity-50 p-2 rounded-full text-white"
            @click.stop="showImageViewer = false"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
      </div>
    </ClientOnly>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import { useApi } from "~/composables/useApi";
import { useAuthStore } from "~/stores/auth";
import { ElMessage } from "element-plus";

const api = useApi();
const authStore = useAuthStore();

// 使用 SSR 數據獲取社交動態
const { data: postsData } = await useFetch("/api/message/list", {
  method: "POST",
  body: {
    page: 1,
    page_nums: 20,
    game_id: 1,
  },
  default: () => ({ code: 500, data: { list: [], total_page: 1 } }),
  server: false,
});

// 获取当前用户信息
const currentUser = computed(() => authStore.user);

// 使用 computed 處理帖子數據
const posts = computed(() => {
  const data = postsData.value;
  if (data?.code === 200 && data?.data?.list) {
    return data.data.list;
  }
  return [];
});

const totalPages = computed(() => {
  const data = postsData.value;
  if (data?.code === 200 && data?.data?.total_page) {
    return data.data.total_page;
  }
  return 1;
});

// 帖子數據狀態
const page = ref(1);
const loading = ref(false);
const loadingMore = ref(false);
const error = ref(null);

// 搜尋
const searchQuery = ref("");
const showFullPostForm = ref(false);

// 發帖相關狀態
const newPost = ref({ title: "", content: "" });
const imageFiles = ref([]);
const imagePreviewUrls = ref([]);

// 圖片查看器狀態
const showImageViewer = ref(false);
const selectedImage = ref("");
const currentImageIndex = ref(0);
const currentGalleryImages = ref([]);

// 展開發文表單
function expandPostForm() {
  showFullPostForm.value = true;
}

// 取消發文
function cancelPost() {
  showFullPostForm.value = false;
  newPost.value = { title: "", content: "" };
  imageFiles.value = [];
  imagePreviewUrls.value = [];
}

// 过滤帖子
const filteredPosts = computed(() => {
  if (!posts.value.length) return [];

  let result = [...posts.value];

  // 应用搜尋过滤
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(
      (post) =>
        (post.title && post.title.toLowerCase().includes(query)) ||
        (post.content && post.content.toLowerCase().includes(query))
    );
  }

  return result;
});

// 計算屬性
const canLoadMore = computed(() => page.value < totalPages.value);
const canSubmit = computed(() => newPost.value.content.trim().length > 0);

// 載入帖子數據
async function fetchPosts() {
  try {
    loading.value = true;
    error.value = null;

    const response = await api.getMessageList({ page: page.value });

    if (response.code === 200) {
      // 确保每个帖子的用户信息都有正确的头像URL
      posts.value = response.data.list.map((post) => ({
        ...post,
        user_info: {
          ...post.user_info,
          headimg: post.user_info?.headimg || "/images/default-avatar.png",
        },
      }));
      totalPages.value = response.data.total_page;
    } else {
      error.value = response.msg || "獲取帖子失敗";
    }
  } catch (err) {
    console.error("Error fetching posts:", err);
    error.value = "獲取帖子時出錯，請稍後再試";
  } finally {
    loading.value = false;
  }
}

// 載入更多帖子
async function loadMore() {
  if (loadingMore.value || page.value >= totalPages.value) return;

  try {
    loadingMore.value = true;
    page.value++;

    const response = await api.getMessageList({ page: page.value });

    if (response.code === 200) {
      posts.value = [...posts.value, ...response.data.list];
    } else {
      error.value = response.msg || "獲取更多帖子失敗";
      page.value--; // 還原頁碼
    }
  } catch (err) {
    console.error("Error loading more posts:", err);
    error.value = "獲取更多帖子時出錯，請稍後再試";
    page.value--; // 還原頁碼
  } finally {
    loadingMore.value = false;
  }
}

// 格式化日期
function formatDate(dateString) {
  if (!dateString) return "";

  let date;
  let now = new Date();

  // 檢查是否為字符串格式的日期時間
  if (typeof dateString === "string" && dateString.includes("-")) {
    date = new Date(dateString);
  } else {
    // 處理Unix時間戳（數字）
    date = new Date(dateString * 1000);
  }

  if (isNaN(date.getTime())) {
    return "";
  }

  const diffTime = Math.abs(now - date);
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    return "今天";
  } else if (diffDays === 1) {
    return "昨天";
  } else if (diffDays < 7) {
    return `${diffDays}天前`;
  } else {
    return (
      dateString.split(" ")[0] ||
      `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
        2,
        "0"
      )}-${String(date.getDate()).padStart(2, "0")}`
    );
  }
}

// 處理圖片顯示相關函數
function getProcessedImages(imagesStr) {
  if (!imagesStr) return [];
  return typeof imagesStr === "string" ? imagesStr.split(",") : imagesStr;
}

// 打開圖片查看器
function openImageViewer(imgUrl) {
  // 找到當前帖子
  const currentPost = posts.value.find((post) => {
    const images = getProcessedImages(post.images);
    return images.includes(imgUrl);
  });

  if (currentPost) {
    const images = getProcessedImages(currentPost.images);
    currentGalleryImages.value = images;
    currentImageIndex.value = images.indexOf(imgUrl);
  } else {
    currentGalleryImages.value = [imgUrl];
    currentImageIndex.value = 0;
  }

  selectedImage.value = imgUrl;
  showImageViewer.value = true;
}

// 圖片瀏覽器導航
function nextImage() {
  if (currentGalleryImages.value.length <= 1) return;
  currentImageIndex.value =
    (currentImageIndex.value + 1) % currentGalleryImages.value.length;
  selectedImage.value = currentGalleryImages.value[currentImageIndex.value];
}

function prevImage() {
  if (currentGalleryImages.value.length <= 1) return;
  currentImageIndex.value =
    (currentImageIndex.value - 1 + currentGalleryImages.value.length) %
    currentGalleryImages.value.length;
  selectedImage.value = currentGalleryImages.value[currentImageIndex.value];
}

// 文件選擇處理
function onFileSelected(event) {
  const selectedFiles = Array.from(event.target.files);

  // 檢查文件類型和大小
  const validFiles = selectedFiles.filter((file) => {
    const isImage = file.type.startsWith("image/");
    const isValidSize = file.size <= 5 * 1024 * 1024; // 5MB限制
    return isImage && isValidSize;
  });

  if (validFiles.length !== selectedFiles.length) {
    ElMessage.warning("部分文件不是圖片或超過5MB大小限制");
  }

  // 更新文件列表
  imageFiles.value = [...imageFiles.value, ...validFiles];

  // 生成預覽URL
  validFiles.forEach((file) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      imagePreviewUrls.value.push(e.target.result);
    };
    reader.readAsDataURL(file);
  });

  // 重置輸入以允許重複選擇相同文件
  event.target.value = null;
}

// 移除選擇的圖片
function removeImage(index) {
  imageFiles.value.splice(index, 1);
  imagePreviewUrls.value.splice(index, 1);
}

// 發布帖子
async function submitPost() {
  if (!canSubmit.value) return;

  try {
    const formData = {
      game_id: "1",
      category_id: "",
      is_shop: "0",
      is_anonymous: "0",
      title: newPost.value.title,
      content: newPost.value.content,
      price: "0",
      images: "",
      video_url: "",
    };

    // 如果有图片，处理图片上传
    if (imageFiles.value.length > 0) {
      try {
        const uploadedImages = await Promise.all(
          imageFiles.value.map(async (file) => {
            const response = await api.uploadImage(file);
            if (response.code === 200 && response.data?.url) {
              return response.data.url;
            } else {
              throw new Error(response.msg || "圖片上傳失敗");
            }
          })
        );
        formData.images = uploadedImages.join(",");
      } catch (error) {
        console.error("圖片上傳失敗:", error);
        ElMessage.error("圖片上傳失敗，請重試");
        return;
      }
    }

    const response = await api.messageAdd(formData);

    if (response.code === 200) {
      ElMessage.success("發布成功");
      // 重置表单
      newPost.value = { title: "", content: "" };
      imageFiles.value = [];
      imagePreviewUrls.value = [];
      showFullPostForm.value = false;
      // 刷新帖子列表
      await fetchPosts();
    } else {
      ElMessage.error(response.msg || "發布失敗");
    }
  } catch (error) {
    console.error("發布失敗:", error);
    ElMessage.error("發布失敗，請稍後重試");
  }
}

// 删除动态
async function deletePost(postId) {
  try {
    const response = await api.messageDelete(postId);
    if (response.code === 200) {
      ElMessage.success("刪除成功");
      // 从列表中移除该动态
      posts.value = posts.value.filter((post) => post.message_id !== postId);
    } else {
      ElMessage.error(response.msg || "刪除失敗");
    }
  } catch (error) {
    console.error("刪除失敗:", error);
    ElMessage.error("刪除失敗，請稍後重試");
  }
}

// 编辑动态
async function editPost(post) {
  try {
    const formData = {
      message_id: post.message_id,
      user_id: authStore.user?.user_id,
      game_id: post.game_id || "1",
      category_id: post.category_id || "",
      is_shop: post.is_shop || "0",
      is_anonymous: post.is_anonymous || "0",
      title: post.title,
      content: post.content,
      price: post.price || "0",
      images: post.images || "",
      video_url: post.video_url || "",
    };

    const response = await api.editMessage(formData);
    if (response.code === 200) {
      ElMessage.success("編輯成功");
      // 更新列表中的动态
      const index = posts.value.findIndex(
        (p) => p.message_id === post.message_id
      );
      if (index !== -1) {
        posts.value[index] = { ...posts.value[index], ...formData };
      }
    } else {
      ElMessage.error(response.msg || "編輯失敗");
    }
  } catch (error) {
    console.error("編輯失敗:", error);
    ElMessage.error("編輯失敗，請稍後重試");
  }
}

// 跳轉到評論頁面
function navigateToComments(postId) {
  // 跳轉到動態詳情頁
  navigateTo(`/message/${postId}`);
}

// 跳轉到帖子詳情頁面
function navigateToPostDetail(postId) {
  // 跳轉到動態詳情頁
  navigateTo(`/message/${postId}`);
}

// 分享帖子
function sharePost(post) {
  if (navigator.share) {
    navigator
      .share({
        title: post.title,
        text: post.content.substring(0, 100),
        url: post.fx_url,
      })
      .catch((err) => {
        console.error("分享失敗:", err);
      });
  } else {
    navigator.clipboard
      .writeText(post.fx_url)
      .then(() => alert("鏈接已複製到剪貼板"))
      .catch((err) => console.error("複製失敗:", err));
  }
}

// 處理头像加载错误
const handleAvatarError = (e) => {
  // 避免無限循環：如果已經是默認頭像還載入失敗，則停止
  if (e.target.src.includes("default-avatar.png")) {
    console.warn("默認頭像載入失敗");
    return;
  }
  e.target.src = "/images/default-avatar.png";
};

// 客戶端初始化
onMounted(async () => {
  // SSR 數據已經在服務端獲取，這裡只需要客戶端特定的初始化
  console.log("社交頁面已載入，帖子數量:", posts.value.length);

  if (authStore.isAuthenticated && !authStore.user?.headimg) {
    await updateUserInfo();
  }
  setupUserInfoListener();
});

// 更新用户信息的函数
const updateUserInfo = async () => {
  if (authStore.isAuthenticated) {
    try {
      const response = await api.getUserInfo();
      if (response.code === 200 && response.data) {
        authStore.updateUserInfo(response.data);
      }
    } catch (err) {
      console.error("获取用户信息失败:", err);
    }
  }
};

// 监听用户信息更新事件
const setupUserInfoListener = () => {
  if (process.client) {
    window.addEventListener("user-info-updated", () => {
      // 只在用户信息确实发生变化时更新
      const currentUserInfo = JSON.stringify(authStore.user);
      const newUserInfo = JSON.stringify(currentUser.value);
      if (currentUserInfo !== newUserInfo) {
        currentUser.value = authStore.user;
      }
    });
  }
};

// SEO設置
useHead({
  title: "遊戲王社區 - iWantCard",
  meta: [
    { name: "description", content: "遊戲王卡片交流社區" },
    { property: "og:title", content: "遊戲王社區 - iWantCard" },
    { property: "og:description", content: "遊戲王卡片交流社區" },
    { property: "og:type", content: "website" },
  ],
});
</script>

<style scoped>
/* 頁面整體佈局 */
.card-group-page {
  min-height: 90vh;
  background: linear-gradient(to bottom, #0f1726, #0a0e18);
  color: #eee;
  overflow-x: hidden;
  padding-bottom: 60px;
}

/* 頁面頭部 - 简化标题 */
.page-header {
  background: linear-gradient(to right, #061224, #0c2442);
  padding: 12px 0;
  color: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
  position: relative;
  overflow: hidden;
  border-bottom: 1px solid rgba(30, 144, 255, 0.3);
  width: 100%;
  margin-bottom: 12px;
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  margin: 0;
  background: linear-gradient(to right, #fff, #1e90ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 0 20px rgba(30, 144, 255, 0.5);
  letter-spacing: 1px;
}

/* 快速發文區域 */
.quick-post-card {
  background: linear-gradient(
    145deg,
    rgba(20, 30, 48, 0.7),
    rgba(12, 20, 35, 0.7)
  );
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(30, 144, 255, 0.2);
}

/* 搜尋区域 */
.search-bar {
  background: rgba(14, 22, 40, 0.5);
  border-radius: 8px;
  padding: 10px;
  border: 1px solid rgba(30, 144, 255, 0.1);
}

/* 帖子列表 - 線性布局，类似 X/Threads */
.posts-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.post-item {
  background: linear-gradient(
    145deg,
    rgba(20, 30, 48, 0.7),
    rgba(12, 20, 35, 0.7)
  );
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(30, 144, 255, 0.2);
  transition: all 0.3s ease;
  min-height: 120px; /* 固定最小高度 */
}

.post-item:hover {
  border-color: rgba(30, 144, 255, 0.4);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.25);
}

/* 統一固定高度佈局樣式 */
.post-item .p-4 {
  padding: 16px;
}

/* 文字截斷樣式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-4 {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 圖片容器樣式 */
.threads-image-container {
  position: relative;
  height: 100%;
  cursor: pointer;
  overflow: hidden;
}

.threads-image-container img {
  transition: transform 0.3s ease;
}

.threads-image-container:hover img {
  transform: scale(1.05);
}

/* 其他必要样式保留 */
.loading-container,
.empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  width: 100%;
  color: rgba(255, 255, 255, 0.3);
}

.my-deck-button {
  background: linear-gradient(135deg, #1e90ff, #00bfff);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.my-deck-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(30, 144, 255, 0.4);
}

/* 手機版文字截斷樣式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 手機版貼文固定高度容器 */
@media (max-width: 768px) {
  .post-item {
    min-height: 120px;
  }

  .post-item .p-4 {
    padding: 12px 16px;
  }
}
</style>
