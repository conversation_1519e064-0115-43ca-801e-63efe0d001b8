<template>
  <div class="p-4 min-h-screen">
    <!-- 麵包屑導航 -->
    <div class="container mx-auto px-0 py-2 max-w-7xl">
      <nav class="flex items-center text-sm">
        <NuxtLink
          to="/"
          class="text-gray-300 hover:text-blue-500 transition-colors"
          >首頁</NuxtLink
        >
        <span class="mx-2 text-gray-500">/</span>
        <NuxtLink
          to="/series"
          class="text-gray-300 hover:text-blue-500 transition-colors"
          >卡片系列</NuxtLink
        >
        <span class="mx-2 text-gray-500">/</span>
        <span class="text-blue-500">{{
          seriesInfo?.title || "載入中..."
        }}</span>
      </nav>
    </div>

    <!-- 主要內容區域 -->
    <div class="container mx-auto px-0 py-4 max-w-7xl">
      <!-- 載入中狀態 -->
      <!-- (移除 loading 判斷) -->

      <!-- 系列資訊卡片 -->
      <div class="series-detail-card mb-6">
        <div class="flex flex-col md:flex-row gap-6">
          <!-- 左側圖片 -->
          <div class="w-full md:w-1/5">
            <div class="series-image-container">
              <img
                :src="seriesInfo?.image || seriesInfo?.goods_thumb"
                :alt="seriesInfo?.title"
                class="w-full h-full object-contain"
                @error="handleImageError"
              />
            </div>
          </div>

          <!-- 右側訊息 -->
          <div class="flex-1">
            <h1 class="text-2xl font-bold text-white mb-2">
              {{ seriesInfo?.title }}
            </h1>
            <p class="text-gray-300 mb-2" v-if="seriesInfo?.goods_alias">
              {{ seriesInfo?.goods_alias }}
            </p>
            <div class="space-y-4">
              <p class="text-gray-300" v-if="seriesInfo?.goods_sn">
                {{ seriesInfo?.goods_sn }}
              </p>
              <div v-if="seriesInfo?.game" class="series-badge">
                {{ seriesInfo?.game }}
              </div>
              <p class="text-gray-300 mt-4" v-if="seriesInfo?.description">
                {{ seriesInfo?.description }}
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- 搜尋與篩選區域 -->
      <div class="filter-card mb-4">
        <div class="flex flex-col gap-3">
          <!-- 標題列 -->
          <div class="flex items-center gap-2">
            <h2 class="text-lg font-bold text-white">收錄內容</h2>
            <span class="text-gray-400">({{ total }})</span>
          </div>

          <!-- 搜尋與篩選列 -->
          <div class="flex gap-3">
            <!-- 搜尋框 -->
            <div class="search-box flex-1">
              <input
                type="text"
                v-model="searchKeyword"
                placeholder="搜尋卡片名稱或卡號"
                class="w-full bg-gray-700 text-white border border-gray-600 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                @keyup.enter="handleSearch"
              />
            </div>

            <!-- 稀有度篩選按鈕 -->
            <button
              class="filter-button flex-shrink-0"
              @click="showRareFilter = true"
            >
              <span>稀有度篩選</span>
              <span v-if="selectedRares.length" class="filter-badge">
                {{ selectedRares.length }}
              </span>
            </button>
          </div>
        </div>

        <!-- 已選擇的稀有度標籤 -->
        <div v-if="selectedRares.length" class="flex flex-wrap gap-2 mt-4">
          <span v-for="rare in selectedRares" :key="rare.id" class="rare-tag">
            {{ rare.name }}
            <button
              @click="removeRare(rare.id)"
              class="ml-1 hover:text-blue-300"
            >
              ×
            </button>
          </span>
        </div>
      </div>

      <!-- 卡片網格 -->
      <div
        class="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-2 md:gap-3"
      >
        <div
          v-for="card in cardList"
          :key="card.goods_id"
          class="card-item"
          @click="goToCardDetail(card.goods_id)"
          style="cursor: pointer"
        >
          <div class="card-image" @click.stop="goToCardDetail(card.goods_id)">
            <img
              :src="card.goods_thumb"
              :alt="card.goods_title"
              class="w-full h-full object-contain"
              @error="handleImageError"
            />
            <span v-if="card.rare" class="rare-badge">
              {{ card.rare }}
            </span>
          </div>

          <div class="card-info" @click.stop="goToCardDetail(card.goods_id)">
            <h3 class="card-title">
              {{ card.goods_title }}
            </h3>
            <p class="card-sn">{{ card.goods_sn }}</p>
            <p class="card-price">
              <span v-if="card.sell_min_price" class="price-text">
                ${{ Number(card.sell_min_price).toLocaleString() }}元起
              </span>
              <span v-else class="no-price-text"> 目前無參考價格 </span>
            </p>
          </div>
        </div>
      </div>

      <!-- 沒有數據的狀態 -->
      <div v-if="cardList.length === 0" class="empty-state">
        <p>該系列下暫無卡片或沒有符合條件的卡片</p>
      </div>

      <!-- 分頁 -->
      <div v-if="totalPages > 1" class="pagination-container">
        <button
          v-for="p in totalPages"
          :key="p"
          :class="[
            'pagination-button',
            currentPage === p ? 'pagination-button-active' : '',
          ]"
          @click="changePage(p)"
        >
          {{ p }}
        </button>
      </div>
    </div>

    <!-- 稀有度篩選彈窗 -->
    <div
      v-if="showRareFilter"
      class="filter-modal"
      @click.self="cancelRareFilter"
    >
      <div class="modal-content">
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-lg font-bold text-white">稀有度篩選</h3>
          <button
            @click="cancelRareFilter"
            class="text-gray-400 hover:text-white"
          >
            <span class="text-2xl">&times;</span>
          </button>
        </div>

        <div class="flex flex-wrap gap-3 mb-6">
          <button
            v-for="rare in rareList"
            :key="rare.id"
            :class="[
              'rare-filter-button',
              isRareSelected(rare.id) ? 'rare-filter-button-active' : '',
            ]"
            @click="toggleRare(rare)"
          >
            {{ rare.name }}
            <span v-if="rare.number" class="ml-1 text-xs opacity-75"
              >({{ rare.number }})</span
            >
          </button>
        </div>

        <div class="flex gap-3">
          <button class="secondary-button w-1/2" @click="cancelRareFilter">
            取消
          </button>
          <button class="primary-button w-1/2" @click="applyRareFilter">
            確認
          </button>
        </div>
      </div>
    </div>

    <!-- 重新設計的卡片詳情模態彈窗 -->
    <div
      v-if="isCardModalOpen"
      class="card-modal-overlay"
      @click.self="closeCardModal"
    >
      <div class="card-modal-layout">
        <!-- 關閉按鈕 -->
        <button @click="closeCardModal" class="card-close-btn">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>

        <!-- 左側：獨立卡片圖片 -->
        <div class="card-image-standalone">
          <div class="card-image-frame">
            <img
              id="card-tilt"
              :src="selectedCard?.goods_thumb"
              :alt="selectedCard?.goods_title"
              class="card-modal-image"
            />
            <!-- 卡片光澤效果 -->
            <div class="card-shine-overlay"></div>
          </div>
        </div>

        <!-- 右側：獨立資訊框 -->
        <div class="card-info-container">
          <!-- 卡片標題與價格 -->
          <div class="card-header">
            <div class="card-title-row">
              <div class="card-title-left">
                <h2 class="card-modal-title">
                  {{ selectedCard?.goods_title }}
                </h2>
                <div class="card-number">{{ selectedCard?.goods_sn }}</div>
              </div>
              <div v-if="selectedCard?.sell_min_price" class="card-price-right">
                <div class="price-label">參考價格</div>
                <div class="price-value">
                  ${{ Number(selectedCard.sell_min_price).toLocaleString() }}
                </div>
              </div>
            </div>
          </div>

          <!-- 稀有度 -->
          <div v-if="selectedCard?.rare" class="card-rarity">
            <div class="rarity-label">稀有度</div>
            <div class="rarity-badge">{{ selectedCard.rare }}</div>
          </div>

          <!-- 卡片效果 -->
          <div
            v-if="selectedCard?.effect || selectedCard?.desc"
            class="card-effect"
          >
            <div class="effect-label">卡片效果</div>
            <div class="effect-text">
              {{ selectedCard?.effect || selectedCard?.desc || "無效果描述" }}
            </div>
          </div>

          <!-- 按鈕區域 -->
          <div class="card-action">
            <!-- 購買按鈕 -->
            <button
              v-if="selectedCard?.sell_min_price"
              @click="goToIWantCardPurchase(selectedCard?.goods_id)"
              class="purchase-action-btn"
            >
              <span>立即購買</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 ml-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"
                />
              </svg>
            </button>

            <!-- 查看完整詳情按鈕 -->
            <NuxtLink
              :to="`/card/${selectedCard?.info_id || selectedCard?.goods_id}`"
              class="detail-action-btn"
              @click="closeCardModal"
            >
              <span>查看完整卡片詳情</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 ml-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M13 7l5 5m0 0l-5 5m5-5H6"
                />
              </svg>
            </NuxtLink>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from "vue";
import { useRoute, useRouter, useHead } from "#app";

const route = useRoute();
const router = useRouter();

// 狀態
const showRareFilter = ref(false);
const selectedRares = ref([]);
const tempSelectedRares = ref([]);
const currentPage = ref(1);
const searchKeyword = ref("");
const pageSize = 50;

// 取得系列ID
const seriesId = computed(() => parseInt(route.params.id));

// SSR 取得系列資訊
const { data: seriesInfoData } = await useFetch("/api/series/getInfo", {
  method: "POST",
  body: { series_id: seriesId.value },
  default: () => ({ code: 500, data: null }),
});
const seriesInfo = computed(() => {
  const data = seriesInfoData.value;
  if (data?.code === 200 && data?.data) return data.data;
  return { id: seriesId.value, title: `系列 #${seriesId.value}`, description: "暫無描述", image: "/images/placeholder.jpg" };
});

// SSR 取得稀有度列表
const { data: rareListData, refresh: refreshRareList } = await useFetch("/api/Rare/getSeriesRareList", {
  method: "POST",
  body: { series_id: seriesId.value, type: 1 },
  default: () => ({ code: 500, data: [] }),
});
const rareList = computed(() => {
  const data = rareListData.value;
  if (data?.code === 200 && data?.data) return data.data;
  return [];
});

// SSR 取得卡片列表
const cardListParams = computed(() => ({
  series_id: seriesId.value,
  page: currentPage.value,
  page_nums: pageSize,
  rare_id: selectedRares.value.map(r => r.id).join(",") || 0,
  key_word: searchKeyword.value,
}));

// 初始 SSR 載入（不包含篩選條件）
const initialCardListParams = computed(() => ({
  series_id: seriesId.value,
  page: 1,
  page_nums: pageSize,
  rare_id: 0,
  key_word: "",
}));

// SSR 初始載入
const { data: cardListData } = await useFetch("/api/Goods/getList", {
  method: "POST",
  body: initialCardListParams,
  default: () => ({ code: 500, data: { list: [], total: 0, total_page: 1 } }),
});

// 客戶端互動載入（只在有篩選條件時使用）
const { data: filteredCardListData, refresh: refreshCardList } = await useFetch("/api/Goods/getList", {
  method: "POST",
  body: cardListParams,
  default: () => ({ code: 500, data: { list: [], total: 0, total_page: 1 } }),
  // 只在客戶端且有必要時才執行
  immediate: false,
});

// 判斷是否使用篩選後的資料
const hasFilters = computed(() => 
  searchKeyword.value.trim() !== "" || selectedRares.value.length > 0 || currentPage.value > 1
);

const cardList = computed(() => {
  // 如果沒有篩選條件，使用 SSR 載入的資料
  if (!hasFilters.value) {
    const data = cardListData.value;
    if (data?.code === 200 && data?.data?.list) return data.data.list;
    return [];
  }
  
  // 如果有篩選條件，使用客戶端載入的資料
  const data = filteredCardListData.value;
  if (data?.code === 200 && data?.data?.list) return data.data.list;
  return [];
});

const total = computed(() => {
  if (!hasFilters.value) {
    return cardListData.value?.data?.total || 0;
  }
  return filteredCardListData.value?.data?.total || 0;
});

const totalPages = computed(() => {
  if (!hasFilters.value) {
    return cardListData.value?.data?.total_page || 1;
  }
  return filteredCardListData.value?.data?.total_page || 1;
});

// SEO
useHead({
  title: computed(() => `${seriesInfo.value.title} - 遊戲王卡片系列`),
  meta: [
    { name: "description", content: computed(() => seriesInfo.value.description || "遊戲王卡片系列詳情，包含收錄卡片和稀有度列表。") },
    { property: "og:title", content: computed(() => `${seriesInfo.value.title} - 遊戲王卡片系列`) },
    { property: "og:description", content: computed(() => seriesInfo.value.description || "遊戲王卡片系列詳情，包含收錄卡片和稀有度列表。") },
    { property: "og:image", content: computed(() => seriesInfo.value.image || seriesInfo.value.goods_thumb || "/images/placeholder.jpg") },
    { property: "og:type", content: "website" },
  ],
});

// 篩選、搜尋、分頁互動
const applyRareFilter = () => {
  selectedRares.value = [...tempSelectedRares.value];
  showRareFilter.value = false;
  currentPage.value = 1;
};
const cancelRareFilter = () => {
  tempSelectedRares.value = [...selectedRares.value];
  showRareFilter.value = false;
};
const toggleRare = (rare) => {
  const idx = tempSelectedRares.value.findIndex(r => r.id === rare.id);
  if (idx === -1) tempSelectedRares.value.push(rare);
  else tempSelectedRares.value.splice(idx, 1);
};
const removeRare = (rareId) => {
  tempSelectedRares.value = tempSelectedRares.value.filter(r => r.id !== rareId);
  selectedRares.value = selectedRares.value.filter(r => r.id !== rareId);
  currentPage.value = 1;
};
const isRareSelected = (rareId) => tempSelectedRares.value.some(r => r.id === rareId);
const handleSearch = () => { currentPage.value = 1; };
const changePage = (p) => { currentPage.value = p; };

// 監控條件變化自動 refetch（只在有篩選條件時）
watch([seriesId, currentPage, searchKeyword, selectedRares], () => {
  // 只有在有篩選條件時才進行客戶端調用
  if (hasFilters.value) {
    refreshCardList();
  }
});

watch(seriesId, () => {
  refreshRareList();
});

// 卡片詳情彈窗互動（client only）
const selectedCard = ref(null);
const isCardModalOpen = ref(false);
const handleImageError = (event) => {
  if (event.target.src.includes("placeholder.jpg")) return;
  event.target.src = "/images/placeholder.jpg";
};
const goToCardDetail = (goodsId) => {
  const card = cardList.value.find((c) => c.goods_id === goodsId);
  if (card) {
    selectedCard.value = card;
    isCardModalOpen.value = true;
    nextTick(() => {
      const cardElement = document.getElementById("card-tilt");
      if (cardElement) {
        initTiltEffect(cardElement);
      }
    });
  }
};
const closeCardModal = () => { isCardModalOpen.value = false; };
const goToIWantCardPurchase = (goodsId) => {
  const iWantCardUrl = `https://web.iwantcard.tw/carddetail/${goodsId}`;
  window.open(iWantCardUrl, "_blank");
};
// 卡片傾斜特效
const initTiltEffect = (element) => {
  try {
    let rect = element.getBoundingClientRect();
    let width = rect.width;
    let height = rect.height;
    let centerX = rect.left + width / 2;
    let centerY = rect.top + height / 2;
    element.removeEventListener("mousemove", element._tiltMoveHandler);
    element.removeEventListener("mouseleave", element._tiltLeaveHandler);
    element._tiltMoveHandler = (e) => {
      const mouseX = e.clientX - centerX;
      const mouseY = e.clientY - centerY;
      const rotateX = (mouseY / (height / 2)) * -10;
      const rotateY = (mouseX / (width / 2)) * 10;
      element.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
    };
    element._tiltLeaveHandler = () => {
      element.style.transform = "perspective(1000px) rotateX(0) rotateY(0)";
    };
    element.addEventListener("mousemove", element._tiltMoveHandler);
    element.addEventListener("mouseleave", element._tiltLeaveHandler);
  } catch (error) {
    console.error("初始化傾斜效果失敗:", error);
  }
};
</script>

<style scoped>
.loading-container {
  background-color: rgba(20, 30, 48, 0.5);
  border-radius: 16px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(30, 144, 255, 0.1);
  backdrop-filter: blur(4px);
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.empty-state {
  background-color: rgba(20, 30, 48, 0.5);
  border-radius: 16px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(30, 144, 255, 0.1);
  backdrop-filter: blur(4px);
  width: 100%;
  color: rgba(255, 255, 255, 0.6);
  font-size: 16px;
  margin-top: 20px;
}

.series-detail-card {
  background: linear-gradient(
    145deg,
    rgba(20, 30, 48, 0.8),
    rgba(30, 40, 60, 0.8)
  );
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(30, 144, 255, 0.15);
  backdrop-filter: blur(4px);
  padding: 20px;
}

.series-image-container {
  height: 300px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(10, 15, 25, 0.7);
  border-radius: 8px;
  padding: 12px;
}

/* 手機版縮小系列圖片高度 */
@media (max-width: 768px) {
  .series-image-container {
    height: 120px;
    width: 120px;
    padding: 6px;
    flex-shrink: 0;
  }

  .series-detail-card {
    margin-bottom: 16px !important;
    padding: 16px;
  }

  .series-detail-card .flex {
    flex-direction: row !important;
    gap: 12px !important;
    align-items: flex-start;
  }

  .series-detail-card > div > div:first-child {
    width: auto !important;
    flex-shrink: 0;
  }

  .series-detail-card h1 {
    font-size: 18px !important;
    margin-bottom: 6px !important;
    line-height: 1.3;
  }

  .series-detail-card p {
    font-size: 13px !important;
    margin-bottom: 4px !important;
  }

  .series-detail-card .space-y-4 {
    gap: 8px;
  }

  .series-detail-card .space-y-4 > * + * {
    margin-top: 8px !important;
  }

  .series-badge {
    font-size: 11px !important;
    padding: 3px 8px !important;
  }
}

.series-badge {
  display: inline-block;
  padding: 4px 12px;
  background: linear-gradient(
    135deg,
    rgba(30, 144, 255, 0.15),
    rgba(30, 144, 255, 0.3)
  );
  color: #1e90ff;
  border: 1px solid rgba(30, 144, 255, 0.4);
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.filter-card {
  background: linear-gradient(
    145deg,
    rgba(20, 30, 48, 0.6),
    rgba(30, 40, 60, 0.6)
  );
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(30, 144, 255, 0.1);
  backdrop-filter: blur(4px);
  padding: 16px;
}

/* 手機版進一步縮小間距 */
@media (max-width: 768px) {
  .filter-card {
    margin-bottom: 12px !important;
    padding: 12px;
  }

  .filter-card h2 {
    font-size: 16px !important;
  }

  .filter-button {
    padding: 6px 10px !important;
    font-size: 13px !important;
  }
}

.filter-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 14px;
  background: rgba(30, 144, 255, 0.1);
  border: 1px solid rgba(30, 144, 255, 0.3);
  color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.filter-button:hover {
  background: rgba(30, 144, 255, 0.2);
  border-color: rgba(30, 144, 255, 0.5);
}

.filter-badge {
  background: #1e90ff;
  color: white;
  border-radius: 9999px;
  padding: 1px 6px;
  font-size: 11px;
  font-weight: 600;
}

.rare-tag {
  display: inline-flex;
  align-items: center;
  padding: 4px 10px;
  background: rgba(30, 144, 255, 0.1);
  border: 1px solid rgba(30, 144, 255, 0.3);
  color: #1e90ff;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.card-item {
  background: linear-gradient(
    145deg,
    rgba(20, 30, 48, 0.6),
    rgba(30, 40, 60, 0.6)
  );
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(30, 144, 255, 0.1);
  cursor: pointer;
  backdrop-filter: blur(4px);
}

.card-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  border-color: rgba(30, 144, 255, 0.3);
}

.card-image {
  aspect-ratio: 1 / 1;
  position: relative;
  background: rgba(10, 15, 25, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6px;
}

.rare-badge {
  position: absolute;
  top: 4px;
  left: 4px;
  padding: 2px 6px;
  background: rgba(30, 144, 255, 0.8);
  color: white;
  font-size: 11px;
  font-weight: 600;
  border-radius: 4px;
  backdrop-filter: blur(4px);
}

.card-info {
  padding: 8px;
}

.card-title {
  font-size: 12px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.3;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  height: 16px;
}

.card-sn {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.5);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 4px;
}

.card-price {
  font-size: 10px;
  margin: 0;
}

.price-text {
  color: #ffd700;
  font-weight: 600;
}

.no-price-text {
  color: rgba(255, 255, 255, 0.4);
  font-style: italic;
}

.pagination-container {
  display: flex;
  justify-content: center;
  gap: 6px;
  margin-top: 24px;
  flex-wrap: wrap;
}

.pagination-button {
  padding: 6px 12px;
  border-radius: 6px;
  background: rgba(30, 40, 60, 0.6);
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.2s ease;
  font-size: 14px;
  min-width: 36px;
  text-align: center;
}

.pagination-button:hover {
  background: rgba(30, 144, 255, 0.2);
}

.pagination-button-active {
  background: linear-gradient(135deg, #1e90ff, #00bfff);
  color: white;
  box-shadow: 0 2px 6px rgba(0, 150, 255, 0.3);
}

.filter-modal {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
}

.modal-content {
  background: linear-gradient(
    145deg,
    rgba(30, 40, 60, 0.95),
    rgba(20, 30, 48, 0.95)
  );
  border-radius: 12px;
  padding: 20px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(30, 144, 255, 0.2);
}

.rare-filter-button {
  padding: 6px 12px;
  border-radius: 20px;
  background: rgba(30, 144, 255, 0.05);
  border: 1px solid rgba(30, 144, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
  font-size: 13px;
  transition: all 0.2s ease;
}

.rare-filter-button:hover {
  background: rgba(30, 144, 255, 0.1);
  border-color: rgba(30, 144, 255, 0.3);
}

.rare-filter-button-active {
  background: linear-gradient(135deg, #1e90ff, #00bfff);
  color: white;
  border-color: transparent;
  box-shadow: 0 2px 6px rgba(0, 150, 255, 0.3);
}

.primary-button {
  padding: 10px;
  background: linear-gradient(135deg, #1e90ff, #00bfff);
  color: white;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(0, 150, 255, 0.3);
  text-align: center;
}

.primary-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 150, 255, 0.4);
}

.secondary-button {
  padding: 10px;
  background: rgba(30, 144, 255, 0.1);
  border: 1px solid rgba(30, 144, 255, 0.3);
  color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.2s ease;
  text-align: center;
}

.secondary-button:hover {
  background: rgba(30, 144, 255, 0.15);
  border-color: rgba(30, 144, 255, 0.4);
}

/* 新的卡片模態彈窗樣式 */
.card-modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
  padding: 20px;
}

.card-modal-layout {
  position: relative;
  display: flex;
  align-items: center;
  gap: 30px;
  max-width: 1000px;
  width: 100%;
}

.card-close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 20;
  background: rgba(0, 0, 0, 0.7);
  border: none;
  border-radius: 50%;
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.card-close-btn:hover {
  background: rgba(255, 50, 50, 0.8);
  transform: scale(1.1);
}

.card-image-standalone {
  flex-shrink: 0;
  z-index: 5;
}

.card-image-frame {
  position: relative;
  width: 280px;
  height: 450px;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 0 0 4px rgba(255, 215, 0, 0.4),
    0 0 0 8px rgba(30, 144, 255, 0.3), 0 25px 50px rgba(0, 0, 0, 0.6),
    0 0 100px rgba(30, 144, 255, 0.2);
  background: linear-gradient(135deg, #1a1a2e, #16213e);
  transform: perspective(1000px) rotateY(-5deg);
  transition: all 0.3s ease;
}

.card-image-frame:hover {
  transform: perspective(1000px) rotateY(0deg) scale(1.02);
  box-shadow: 0 0 0 4px rgba(255, 215, 0, 0.6),
    0 0 0 8px rgba(30, 144, 255, 0.5), 0 30px 60px rgba(0, 0, 0, 0.7),
    0 0 120px rgba(30, 144, 255, 0.3);
}

.card-modal-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
  filter: brightness(1.1) contrast(1.1);
}

.card-shine-overlay {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.8s ease;
  pointer-events: none;
}

.card-image-frame:hover .card-shine-overlay {
  left: 100%;
}

.card-info-container {
  width: 600px;
  background: linear-gradient(
    135deg,
    rgba(15, 25, 40, 0.95),
    rgba(25, 35, 55, 0.95)
  );
  border-radius: 20px;
  padding: 30px;
  display: flex;
  flex-direction: column;
  min-height: 450px;
  max-height: 550px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(30, 144, 255, 0.2);
  backdrop-filter: blur(10px);
  overflow: hidden;
  justify-content: space-between; /* 添加這個屬性 */
}

.card-header {
  margin-bottom: 20px;
  flex-shrink: 0;
}

.card-title-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.card-title-left {
  flex: 1;
  min-width: 0;
}

.card-price-right {
  flex-shrink: 0;
  text-align: right;
}

.card-modal-title {
  font-size: 24px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 8px;
  line-height: 1.3;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.card-number {
  font-size: 14px;
  color: rgba(30, 144, 255, 0.8);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.card-rarity {
  margin-bottom: 15px;
  padding: 15px;
  background: linear-gradient(
    135deg,
    rgba(30, 144, 255, 0.1),
    rgba(255, 215, 0, 0.1)
  );
  border-radius: 12px;
  border: 1px solid rgba(30, 144, 255, 0.3);
  flex-shrink: 0;
}

.rarity-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 5px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.rarity-badge {
  display: inline-block;
  padding: 6px 12px;
  background: linear-gradient(135deg, #1e90ff, #ffd700);
  color: #000;
  font-weight: 700;
  font-size: 14px;
  border-radius: 20px;
  text-shadow: none;
}

.card-effect {
  margin-bottom: 20px; /* 改為固定的 margin-bottom */
  flex-shrink: 0; /* 改為不收縮 */
}

.effect-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 10px;
  font-weight: 600;
}

.effect-text {
  background: rgba(0, 0, 0, 0.3);
  padding: 15px;
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  font-size: 14px;
  border-left: 3px solid rgba(30, 144, 255, 0.5);
  max-height: 120px;
  min-height: 80px;
  overflow-y: auto;
  word-wrap: break-word;
}

.card-price-right .price-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 4px;
}

.card-price-right .price-value {
  font-size: 16px;
  font-weight: 700;
  color: #ffd700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.card-action {
  flex-shrink: 0; /* 保持按鈕在底部 */
}

.detail-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 15px 25px;
  background: linear-gradient(135deg, #1e90ff, #00bfff);
  color: white;
  text-decoration: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(30, 144, 255, 0.3);
  border: 1px solid rgba(30, 144, 255, 0.5);
}

.detail-action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(30, 144, 255, 0.4);
  background: linear-gradient(135deg, #0080ff, #0099ff);
}

.purchase-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 15px 25px;
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: white;
  text-decoration: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
  border: 1px solid rgba(255, 107, 53, 0.5);
  margin-bottom: 12px;
  cursor: pointer;
}

.purchase-action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
  background: linear-gradient(135deg, #ff5722, #ff9800);
}

/* 響應式設計 */
@media (max-width: 768px) {
  .card-modal-overlay {
    padding: 8px;
    align-items: flex-start;
    justify-content: center;
    overflow-y: auto;
  }

  .card-modal-layout {
    flex-direction: column;
    max-width: 96%;
    gap: 8px;
    max-height: none;
    margin: 10px auto;
  }

  .card-image-standalone {
    align-self: center;
    flex-shrink: 0;
  }

  .card-image-frame {
    width: 120px;
    height: 190px;
    transform: none;
  }

  .card-image-frame:hover {
    transform: scale(1.02);
  }

  .card-info-container {
    width: 100%;
    height: auto;
    padding: 15px;
    min-height: auto;
    max-height: none;
    flex-shrink: 0;
  }

  .card-title-row {
    gap: 10px;
  }

  .card-price-right {
    text-align: right;
  }

  .card-modal-title {
    font-size: 16px;
    margin-bottom: 4px;
    line-height: 1.2;
  }

  .card-number {
    font-size: 11px;
  }

  .card-header {
    margin-bottom: 10px;
  }

  .card-price-right .price-label {
    font-size: 10px;
    margin-bottom: 2px;
  }

  .card-price-right .price-value {
    font-size: 14px;
  }

  .card-rarity {
    margin-bottom: 8px;
    padding: 8px 10px;
  }

  .rarity-label {
    font-size: 10px;
    margin-bottom: 3px;
  }

  .rarity-badge {
    font-size: 12px;
    padding: 4px 8px;
  }

  .card-effect {
    margin-bottom: 10px;
  }

  .effect-label {
    font-size: 12px;
    margin-bottom: 6px;
  }

  .effect-text {
    max-height: 80px;
    min-height: 40px;
    font-size: 12px;
    padding: 8px;
    line-height: 1.4;
  }

  .detail-action-btn,
  .purchase-action-btn {
    padding: 10px 16px;
    font-size: 13px;
  }

  .purchase-action-btn {
    margin-bottom: 8px;
  }

  .card-close-btn {
    top: 10px;
    right: 10px;
    width: 36px;
    height: 36px;
  }
}
</style>
