<!-- pages/series/index.vue -->
<template>
  <div class="p-2">
    <h2 class="text-lg font-bold mb-3">系列商品</h2>

    <!-- 載入中狀態（首次載入） -->
    <div v-if="!periodicalsData" class="loading-container py-8">
      <div
        class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"
      ></div>
    </div>

    <!-- 錯誤訊息 -->
    <div
      v-else-if="periodicalsData?.code !== 200"
      class="bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded relative text-sm"
      role="alert"
    >
      <strong class="font-bold">錯誤:</strong>
      <span class="block sm:inline ml-1">{{ periodicalsData?.msg || '載入期數資料失敗' }}</span>
    </div>

    <!-- 無數據狀態 -->
    <div v-else-if="!periodicals.length" class="empty-state py-8">
      <p>沒有找到期數數據</p>
    </div>

    <!-- 期數與系列列表 -->
    <div v-else>
      <div v-for="periodical in periodicals" :key="periodical.id" class="mb-3">
        <!-- 期數標題 -->
        <div class="periodical-header" @click="togglePeriodical(periodical.id)">
          <h3 class="font-bold">{{ periodical.title }}</h3>
          <div
            class="transform transition-transform duration-200"
            :class="{ 'rotate-180': expandedPeriodicals[periodical.id] }"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                clip-rule="evenodd"
              />
            </svg>
          </div>
        </div>

        <!-- 系列列表 -->
        <div v-if="expandedPeriodicals[periodical.id]" class="transition-all">
          <!-- 期數系列載入中 -->
          <div
            v-if="loadingPeriodicals[periodical.id]"
            class="flex justify-center py-4"
          >
            <div
              class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"
            ></div>
          </div>

          <!-- 無系列數據 -->
          <div
            v-else-if="
              !seriesByPeriodical[periodical.id] ||
              !seriesByPeriodical[periodical.id].length
            "
            class="text-center py-4 text-gray-400 text-sm"
          >
            該期數下沒有系列
          </div>

          <!-- 系列网格 -->
          <div v-else class="series-grid">
            <div
              v-for="series in seriesByPeriodical[periodical.id]"
              :key="series.id"
              class="series-card"
              @click="viewSeriesDetails(series.id)"
            >
              <div class="series-image">
                <img
                  :src="series.image"
                  :alt="series.title"
                  class="max-h-full max-w-full object-contain"
                  @error="handleImageError"
                />
              </div>
              <div class="series-info">
                <h4 class="series-title">{{ series.title }}</h4>
                <p class="series-date">{{ series.createTime }}</p>
              </div>
            </div>
          </div>

          <!-- 載入更多 - 只有当确实有更多数据时才显示 -->
          <div
            v-if="
              hasMoreSeries[periodical.id] &&
              seriesByPeriodical[periodical.id]?.length >= pageSize
            "
            class="flex justify-center mt-2"
          >
            <button
              @click="loadMoreSeries(periodical.id)"
              class="load-more-btn"
              :disabled="loadingMoreSeries[periodical.id]"
            >
              {{ loadingMoreSeries[periodical.id] ? "載入中..." : "載入更多" }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed } from "vue";
import { SeriesItem } from "~/models/series";
import { PeriodicalItem } from "~/models/periodical";
import { useHead } from "#app";

useHead({
  title: "遊戲王卡片系列 - 遊戲王牌組構築器",
  meta: [
    {
      name: "description",
      content: "瀏覽遊戲王所有系列卡片，包括最新發行和經典收藏系列。",
    },
    { name: "keywords", content: "遊戲王,系列,卡片,牌組,收藏" },
  ],
});

const pageSize = 20;

// 使用 SSR 數據獲取期數列表
const { data: periodicalsData } = await useFetch("/api/Periodical/getList", {
  method: "POST",
  body: {
    new: true,
    newnum: true,
    page: 1,
    page_nums: 50,
    type: 1,
    game_id: 1,
  },
  default: () => ({ code: 500, data: { list: [] } }),
});

// 期數相關狀態
const expandedPeriodicals = reactive({});
const loadingPeriodicals = reactive({});

// 系列相關狀態
const seriesByPeriodical = reactive({});
const seriesPageByPeriodical = reactive({});
const hasMoreSeries = reactive({});
const loadingMoreSeries = reactive({});

// 使用 computed 處理期數數據
const periodicals = computed(() => {
  const data = periodicalsData.value;
  if (data?.code === 200 && data?.data) {
    // API 返回的資料結構是 data 陣列，不是 data.list
    const periodicalList = Array.isArray(data.data) ? data.data : data.data.list || [];
    return periodicalList.map((item) => new PeriodicalItem(item));
  }
  return [];
});

// 切換期數展開狀態
const togglePeriodical = (periodicalId) => {
  // 如果之前沒有載入過，則載入該期數下的系列
  if (!expandedPeriodicals[periodicalId] && !seriesByPeriodical[periodicalId]) {
    loadSeriesByPeriodical(periodicalId);
  }

  expandedPeriodicals[periodicalId] = !expandedPeriodicals[periodicalId];
};

// 載入特定期數下的系列列表（只在用戶互動時調用）
const loadSeriesByPeriodical = async (periodicalId, page = 1) => {
  if (!periodicalId) return;

  if (page === 1) {
    loadingPeriodicals[periodicalId] = true;
  } else {
    loadingMoreSeries[periodicalId] = true;
  }

  try {
    const response = await $fetch("/api/series/list", {
      method: "POST",
      body: {
        periodical_id: periodicalId,
        page: page,
        page_nums: pageSize,
        type: 1,
        game_id: 1,
      },
    });

    if (response.code === 200) {
      if (
        response.data &&
        response.data.list &&
        Array.isArray(response.data.list)
      ) {
        const seriesItems = response.data.list.map(
          (item) => new SeriesItem(item)
        );

        if (page === 1) {
          seriesByPeriodical[periodicalId] = seriesItems;
        } else {
          seriesByPeriodical[periodicalId] = [
            ...(seriesByPeriodical[periodicalId] || []),
            ...seriesItems,
          ];
        }

        seriesPageByPeriodical[periodicalId] = page;
        hasMoreSeries[periodicalId] = seriesItems.length >= pageSize;
      } else {
        console.error("Invalid series data format:", response);
      }
    } else if (response.code === 401) {
      console.error("需要登入");
    } else {
      console.error("Failed to load series:", response);
    }
  } catch (err) {
    console.error(`載入期數 ${periodicalId} 的系列失敗:`, err);
  } finally {
    if (page === 1) {
      loadingPeriodicals[periodicalId] = false;
    } else {
      loadingMoreSeries[periodicalId] = false;
    }
  }
};

// 載入更多系列
const loadMoreSeries = (periodicalId) => {
  const nextPage = (seriesPageByPeriodical[periodicalId] || 1) + 1;
  loadSeriesByPeriodical(periodicalId, nextPage);
};

// 圖片錯誤處理
const handleImageError = (event) => {
  event.target.src = "/images/placeholder.jpg";
};

// 查看系列詳情
const viewSeriesDetails = (seriesId) => {
  try {
    // 確保seriesId是整數
    const parsedId = parseInt(seriesId);
    // 使用navigateTo統一路由
    navigateTo(`/series/${parsedId}`);
  } catch (err) {
    console.error("跳轉系列詳情頁面時發生錯誤:", err);
  }
};

// 客戶端初始化（只做必要的客戶端設置，不調用 API）
onMounted(() => {
  console.log("系列頁面已載入，期數數量:", periodicals.value.length);
  console.log("期數資料:", periodicalsData.value);
});
</script>

<style scoped>
.loading-container,
.empty-state {
  background-color: rgba(20, 30, 48, 0.5);
  border-radius: 10px;
  text-align: center;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(30, 144, 255, 0.1);
  backdrop-filter: blur(4px);
  width: 100%;
}

.empty-state p {
  color: rgba(255, 255, 255, 0.6);
  font-size: 15px;
}

.periodical-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  margin-bottom: 8px;
  background: linear-gradient(
    90deg,
    rgba(12, 20, 35, 0.95),
    rgba(20, 40, 80, 0.95)
  );
  color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid rgba(30, 144, 255, 0.2);
  font-size: 14px;
}

@media (max-width: 640px) {
  .periodical-header {
    padding: 6px 10px;
    font-size: 13px;
    margin-bottom: 6px;
  }
}

.periodical-header:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
  background: linear-gradient(
    90deg,
    rgba(15, 25, 40, 0.95),
    rgba(25, 45, 85, 0.95)
  );
}

.series-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin-top: 8px;
}

@media (min-width: 480px) {
  .series-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
  }
}

@media (min-width: 768px) {
  .series-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 18px;
  }
}

.series-card {
  background: linear-gradient(
    145deg,
    rgba(20, 30, 48, 0.7),
    rgba(30, 40, 60, 0.7)
  );
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.25s ease;
  border: 1px solid rgba(30, 144, 255, 0.1);
  backdrop-filter: blur(4px);
  cursor: pointer;
}

.series-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  border-color: rgba(30, 144, 255, 0.3);
}

.series-image {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(10, 15, 25, 0.7);
  padding: 4px;
}

@media (min-width: 768px) {
  .series-image {
    height: 130px;
    padding: 8px;
  }
}

.series-info {
  padding: 6px;
}

@media (min-width: 768px) {
  .series-info {
    padding: 10px 8px 8px 8px;
  }
}

.series-title {
  font-weight: 600;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.95);
  line-height: 1.3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 18px;
}

@media (min-width: 768px) {
  .series-title {
    font-size: 16px;
    height: 22px;
  }
}

.series-date {
  font-size: 9px;
  color: rgba(255, 255, 255, 0.5);
  margin-top: 1px;
}

@media (min-width: 768px) {
  .series-date {
    font-size: 12px;
  }
}

.load-more-btn {
  background: linear-gradient(135deg, #1e90ff, #00bfff);
  color: white;
  border: none;
  padding: 6px 16px;
  border-radius: 16px;
  font-size: 15px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(30, 144, 255, 0.08);
  transition: all 0.2s;
  margin-bottom: 8px;
}

.load-more-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>
