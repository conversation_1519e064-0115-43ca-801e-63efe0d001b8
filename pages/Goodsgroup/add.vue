<template>
  <div
    class="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white"
  >
    <!-- Header -->
    <header
      class="bg-slate-800/50 backdrop-blur-sm border-b border-slate-700/50 sticky top-0 z-40"
    >
      <div class="max-w-7xl mx-auto px-3 md:px-6 py-3 md:py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-4">
            <button
              @click="goBack"
              class="p-2 hover:bg-slate-700/50 rounded-lg transition-colors duration-200"
            >
              <ArrowLeft class="w-5 h-5" />
            </button>
            <h1 class="text-lg md:text-2xl font-bold">
              {{ isEdit ? "編輯牌組" : "新建牌組" }}
            </h1>
          </div>
          <div class="flex items-center gap-3">
            <button
              @click="saveDeck"
              :disabled="isSaving"
              class="flex items-center gap-1 md:gap-2 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 px-2 md:px-4 py-1.5 md:py-2 rounded-lg transition-colors duration-200 text-sm md:text-base"
            >
              <Save class="w-3 h-3 md:w-4 md:h-4" />
              <span class="hidden sm:inline">{{
                isSaving ? "儲存中..." : "儲存牌組"
              }}</span>
              <span class="sm:hidden">{{ isSaving ? "儲存..." : "儲存" }}</span>
            </button>
            <button
              class="hidden md:flex items-center gap-2 bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg transition-colors duration-200"
            >
              <Share2 class="w-4 h-4" />
              分享
            </button>
          </div>
        </div>
      </div>
    </header>

    <div class="max-w-7xl mx-auto px-3 md:px-6 py-4 md:py-8">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-3 md:gap-8">
        <!-- 左側：牌組資訊、統計、搜尋卡片 -->
        <div class="lg:col-span-1 space-y-3 md:space-y-6">
          <!-- 牌組基本資訊 -->
          <div
            class="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 overflow-hidden"
          >
            <div class="bg-gradient-to-r from-blue-600 to-purple-600 p-4">
              <h2 class="text-lg font-semibold text-white">牌組資訊</h2>
            </div>
            <div class="p-3 md:p-6 space-y-3 md:space-y-4">
              <div class="flex gap-3 md:gap-4">
                <!-- 封面圖片 -->
                <div
                  class="w-16 h-24 md:w-24 md:h-36 bg-slate-700 rounded-lg flex items-center justify-center cursor-pointer hover:bg-slate-600 transition-colors duration-200 group relative overflow-hidden"
                  @click="selectCoverFromDeck"
                >
                  <img
                    v-if="deckData.image"
                    :src="deckData.image"
                    alt="牌組封面"
                    class="w-full h-full object-cover"
                  />
                  <div v-else class="text-center">
                    <Camera
                      class="w-4 h-4 md:w-6 md:h-6 mx-auto mb-1 text-gray-400"
                    />
                    <span class="text-xs text-gray-400 hidden md:block"
                      >選擇封面</span
                    >
                  </div>
                  <div
                    class="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center"
                  >
                    <Camera class="w-5 h-5 text-white" />
                  </div>
                </div>

                <!-- 基本資訊 -->
                <div class="flex-1 space-y-2 md:space-y-3">
                  <div>
                    <label class="block text-sm font-medium text-gray-300 mb-1"
                      >牌組名稱</label
                    >
                    <input
                      v-model="deckData.title"
                      type="text"
                      class="w-full bg-slate-700 border border-slate-600 rounded-lg px-2 md:px-3 py-1.5 md:py-2 text-sm md:text-base text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="輸入牌組名稱"
                    />
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-300 mb-1"
                      >環境</label
                    >
                    <select
                      v-model="deckData.env_id"
                      class="w-full bg-slate-700 border border-slate-600 rounded-lg px-2 md:px-3 py-1.5 md:py-2 text-sm md:text-base text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">選擇環境</option>
                      <option
                        v-for="env in envList"
                        :key="env.env_id"
                        :value="env.env_id"
                      >
                        {{ env.title }}
                      </option>
                    </select>
                  </div>
                </div>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-300 mb-1"
                  >牌組說明</label
                >
                <textarea
                  v-model="deckData.desc"
                  class="w-full bg-slate-700 border border-slate-600 rounded-lg px-2 md:px-3 py-1.5 md:py-2 text-sm md:text-base text-white focus:outline-none focus:ring-2 focus:ring-blue-500 h-16 md:h-20 resize-none"
                  placeholder="請簡單描述這套牌組"
                />
              </div>
            </div>
          </div>

          <!-- 牌組統計 -->
          <div
            class="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 overflow-hidden"
          >
            <div class="bg-gradient-to-r from-green-600 to-blue-600 p-4">
              <h2 class="text-lg font-semibold text-white">牌組統計</h2>
            </div>
            <div class="p-3 md:p-6 space-y-3 md:space-y-4">
              <div
                v-for="(stat, index) in deckStats"
                :key="stat.id"
                class="space-y-1 md:space-y-2"
              >
                <div class="flex justify-between items-center">
                  <span class="font-medium text-sm md:text-base">{{
                    stat.name
                  }}</span>
                  <span
                    :class="
                      stat.count > stat.max ? 'text-red-400' : 'text-gray-300'
                    "
                    class="text-xs md:text-sm"
                  >
                    {{ stat.count }} / {{ stat.max }}
                  </span>
                </div>
                <div class="w-full bg-slate-700 rounded-full h-1.5 md:h-2">
                  <div
                    :class="stat.count > stat.max ? 'bg-red-500' : stat.color"
                    class="h-1.5 md:h-2 rounded-full transition-all duration-300"
                    :style="{
                      width: `${Math.min((stat.count / stat.max) * 100, 100)}%`,
                    }"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 搜尋卡片（含篩選） -->
          <div
            class="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 overflow-hidden"
          >
            <div class="p-3 md:p-6 border-b border-slate-700/50">
              <div class="flex gap-2 md:gap-4 mb-3 md:mb-4">
                <div class="flex-1">
                  <div class="relative">
                    <Search
                      class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"
                    />
                    <input
                      v-model="searchKeyword"
                      @input="debounceSearch"
                      type="text"
                      class="w-full pl-8 md:pl-10 pr-3 md:pr-4 py-2 md:py-3 bg-slate-700 border border-slate-600 rounded-lg text-sm md:text-base text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="搜尋卡片..."
                    />
                  </div>
                </div>
                <button
                  @click="showFilters = !showFilters"
                  :class="[
                    'flex items-center gap-1 md:gap-2 px-2 md:px-4 py-2 md:py-3 rounded-lg transition-colors duration-200',
                    showFilters
                      ? 'bg-blue-600 text-white'
                      : 'bg-slate-700 text-gray-300 hover:bg-slate-600',
                  ]"
                >
                  <Filter class="w-3 h-3 md:w-4 md:h-4" />
                  <span class="text-xs md:text-sm">篩選</span>
                  <ChevronDown
                    v-if="!showFilters"
                    class="w-3 h-3 md:w-4 md:h-4"
                  />
                  <ChevronUp v-else class="w-3 h-3 md:w-4 md:h-4" />
                </button>
              </div>

              <!-- 篩選器面板 -->
              <div
                v-if="showFilters"
                class="grid grid-cols-1 md:grid-cols-3 gap-2 md:gap-4 p-3 md:p-4 bg-slate-700/50 rounded-lg"
              >
                <div v-for="attr in simplifiedAttrList" :key="attr.attr_id">
                  <label class="block text-sm font-medium text-gray-300 mb-2">{{
                    attr.displayName
                  }}</label>
                  <select
                    v-model="attr.selected"
                    @change="onAttributeChange"
                    class="w-full bg-slate-600 border border-slate-500 rounded-lg px-2 md:px-3 py-1.5 md:py-2 text-white text-xs md:text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="-1">全部</option>
                    <option
                      v-for="(option, index) in attr.options"
                      :key="index"
                      :value="index"
                    >
                      {{ option }}
                    </option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右側：當前牌組 + 可用卡片區塊 -->
        <div class="lg:col-span-2 space-y-6">
          <!-- 當前牌組區塊（卡片放大） -->
          <div
            class="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 overflow-hidden"
          >
            <div class="bg-gradient-to-r from-purple-600 to-pink-600 p-4">
              <h2 class="text-lg font-semibold text-white">當前牌組</h2>
            </div>
            <!-- 牌組標籤 -->
            <div class="flex border-b border-slate-700/50">
              <button
                v-for="(type, index) in deckTypes"
                :key="type.id"
                @click="activeTab = index"
                :class="[
                  'flex-1 py-3 px-4 text-sm font-medium transition-colors duration-200',
                  activeTab === index
                    ? 'bg-slate-700 text-white border-b-2 border-blue-500'
                    : 'text-gray-400 hover:text-gray-300 hover:bg-slate-700/50',
                ]"
              >
                {{ type.name }}
                <span class="ml-2 text-xs"
                  >({{ deckCards[index].length }})</span
                >
              </button>
            </div>
            <!-- 牌組卡片列表（放大顯示） -->
            <div class="p-3 md:p-6">
              <div
                v-if="deckCards[activeTab].length === 0"
                class="text-center py-8 md:py-12 text-gray-400"
              >
                <FileText
                  class="w-12 h-12 md:w-16 md:h-16 mx-auto mb-4 opacity-50"
                />
                <p class="text-base md:text-lg mb-2">還沒有加入任何卡片</p>
                <p class="text-sm md:text-base">
                  從上方卡片區域選擇卡片加入牌組
                </p>
              </div>
              <div
                v-else
                class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-3 md:gap-4"
              >
                <div
                  v-for="card in getUniqueCardsInDeck()"
                  :key="card.goods_id"
                  class="relative group cursor-pointer"
                  @mouseenter="showCardTooltip(card, $event)"
                  @mouseleave="hideCardTooltip"
                  @click="selectAsCover(card)"
                >
                  <div
                    class="aspect-[3/4] bg-slate-700 rounded-lg overflow-hidden flex items-center justify-center"
                  >
                    <img
                      :src="card.goods_thumb"
                      :alt="card.goods_title"
                      class="w-full h-32 md:h-40 object-contain group-hover:scale-105 transition-transform duration-200"
                    />
                    <div
                      v-if="card.count > 1"
                      class="absolute bottom-0.5 right-0.5 md:bottom-1 md:right-1 bg-black/70 text-white text-xs px-1 rounded"
                    >
                      x{{ card.count }}
                    </div>
                    <div
                      v-if="card.ban_type > 0"
                      :class="getBanTypeClass(card.ban_type)"
                      class="absolute top-0.5 left-0.5 md:top-1 md:left-1 text-xs px-1 rounded"
                    >
                      {{ getBanTypeText(card.ban_type) }}
                    </div>
                  </div>
                  <button
                    @click.stop="removeCardFromDeck(card.goods_id)"
                    class="absolute -top-1 -right-1 md:-top-2 md:-right-2 bg-red-500 hover:bg-red-600 text-white rounded-full w-4 h-4 md:w-6 md:h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                  >
                    <X class="w-2 h-2 md:w-3 md:h-3" />
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 可用卡片區塊（原本右側內容） -->
          <div
            class="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 overflow-hidden h-[400px] md:h-[600px] lg:h-[800px] flex flex-col"
          >
            <!-- 卡片列表標題 -->
            <div
              class="px-3 md:px-6 py-2 md:py-3 border-b border-slate-700/50 flex justify-between items-center"
            >
              <h3 class="font-semibold text-sm md:text-base">可用卡片</h3>
              <span class="text-xs md:text-sm text-gray-400"
                >共 {{ totalCards }} 張</span
              >
            </div>
            <!-- 卡片列表 -->
            <div class="flex-1 overflow-auto p-2 md:p-6" @scroll="handleScroll">
              <div
                v-if="isLoading && cardsList.length === 0"
                class="flex items-center justify-center h-full"
              >
                <div
                  class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"
                ></div>
              </div>
              <div
                v-else
                class="grid grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-2 md:gap-4"
              >
                <div
                  v-for="card in cardsList"
                  :key="card.goods_id"
                  class="group cursor-pointer"
                  @click="addCardToDeck(card)"
                  @mouseenter="showCardTooltip(card, $event)"
                  @mouseleave="hideCardTooltip"
                >
                  <div class="relative aspect-[3/4]">
                    <img
                      :src="card.goods_thumb"
                      :alt="card.goods_title"
                      class="w-full h-full object-contain rounded-lg group-hover:scale-105 transition-transform duration-200"
                    />
                    <div
                      v-if="getCardCountInDeck(card.goods_id) > 0"
                      class="absolute bottom-0.5 right-0.5 md:bottom-1 md:right-1 bg-blue-600 text-white text-xs px-1 rounded"
                    >
                      x{{ getCardCountInDeck(card.goods_id) }}
                    </div>
                    <div
                      v-if="card.ban_type > 0"
                      :class="getBanTypeClass(card.ban_type)"
                      class="absolute top-0.5 left-0.5 md:top-1 md:left-1 text-xs px-1 rounded"
                    >
                      {{ getBanTypeText(card.ban_type) }}
                    </div>
                  </div>
                  <div
                    class="text-xs text-center mt-1 text-gray-300 truncate hidden md:block"
                  >
                    {{ card.goods_title }}
                  </div>
                </div>
              </div>
              <!-- 載入更多 -->
              <div v-if="isLoadingMore" class="text-center py-2 md:py-4">
                <div
                  class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto"
                ></div>
                <span class="text-xs md:text-sm text-gray-400 mt-2"
                  >載入中...</span
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 卡片詳情懸浮窗 -->
    <div
      v-if="showTooltip && tooltipCard"
      class="fixed bg-slate-800 border border-slate-600 rounded-lg p-3 md:p-4 shadow-xl z-50 max-w-xs md:max-w-sm pointer-events-none hidden md:block"
      :style="tooltipStyle"
    >
      <div class="flex gap-2 md:gap-3">
        <img
          :src="tooltipCard.goods_thumb"
          :alt="tooltipCard.goods_title"
          class="w-12 h-16 md:w-16 md:h-24 object-contain rounded flex-shrink-0"
        />
        <div class="flex-1 min-w-0">
          <h4
            class="font-semibold text-white mb-1 md:mb-2 text-sm md:text-base"
          >
            {{ tooltipCard.goods_title }}
          </h4>
          <div class="space-y-1 text-xs md:text-sm text-gray-300">
            <div class="flex items-center gap-2">
              <span class="text-blue-400">{{ tooltipCard.type_name }}</span>
              <span v-if="tooltipCard.attr_name">•</span>
              <span class="text-green-400">{{ tooltipCard.attr_name }}</span>
            </div>
            <div v-if="tooltipCard.level" class="flex items-center gap-2">
              <span>★{{ tooltipCard.level }}</span>
              <span v-if="tooltipCard.atk !== undefined">•</span>
              <span v-if="tooltipCard.atk !== undefined"
                >ATK: {{ tooltipCard.atk }}</span
              >
              <span v-if="tooltipCard.def !== undefined">•</span>
              <span v-if="tooltipCard.def !== undefined"
                >DEF: {{ tooltipCard.def }}</span
              >
            </div>
            <p
              v-if="tooltipCard.effect"
              class="text-gray-400 text-xs leading-relaxed mt-1 md:mt-2"
            >
              {{ tooltipCard.effect }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import {
  ArrowLeft,
  Search,
  Filter,
  Save,
  Share2,
  Camera,
  FileText,
  X,
  ChevronDown,
  ChevronUp,
} from "lucide-vue-next";

// 🔧 修正：正確導入 API 方法
import {
  getGoodsGroupEnvList,
  getGoodsGroupTypeList,
  getAttrList,
  getGoodsListByAttr,
  updateGoodsGroup,
  getCardGroupDetails,
} from "~/composables/useApi";

const route = useRoute();
const router = useRouter();

// 基本資料
const groupId = computed(() => Number(route.query.group_id) || 0);
const isIdentify = computed(() => Number(route.query.is_identify) || 1);
const gameId = computed(() => Number(route.query.game_id) || 1);
const isEdit = computed(() => groupId.value > 0);

// 使用 SSR 數據獲取環境列表
const { data: envListData } = await useFetch("/api/Goodsgroup/env-list", {
  method: "POST",
  body: {},
  default: () => ({ code: 500, data: [] }),
  server: false,
});

// 使用 SSR 數據獲取類型列表
const { data: typeListData } = await useFetch("/api/Goodsgroup/type-list", {
  method: "POST",
  body: {},
  default: () => ({ code: 500, data: [] }),
  server: false,
});

// 使用 SSR 數據獲取屬性列表
const { data: attrListData } = await useFetch("/api/Goods/getAttrList", {
  method: "POST",
  body: { game_id: gameId.value },
  default: () => ({ code: 500, data: [] }),
  server: false,
});

// 狀態管理
const isLoading = ref(false);
const isLoadingMore = ref(false);
const isSaving = ref(false);
const showFilters = ref(true);
const showTooltip = ref(false);
const activeTab = ref(0);

// 牌組數據
const deckData = ref({
  title: "未命名牌組",
  desc: "",
  env_id: "",
  image: "",
  game_id: gameId.value,
  is_identify: isIdentify.value,
});

// 使用 computed 處理環境列表數據
const envList = computed(() => {
  const data = envListData.value;
  if (data?.code === 200 && data?.data) {
    return data.data;
  }
  return [];
});

// 使用 computed 處理類型列表數據
const typeList = computed(() => {
  const data = typeListData.value;
  if (data?.code === 200 && data?.data) {
    return data.data;
  }
  return [];
});

// 使用 computed 處理屬性列表數據
const attrList = computed(() => {
  const data = attrListData.value;
  if (data?.code === 200 && data?.data) {
    return data.data.map((attr) => ({
      ...attr,
      selected: -1,
    }));
  }
  return [];
});

// 列表數據
const cardsList = ref([]);
const deckCards = ref([[], [], []]); // 主牌組、額外牌組、備用牌組

// 搜尋和分頁
const searchKeyword = ref("");
const currentPage = ref(1);
const totalCards = ref(0);
const hasMoreCards = ref(true);

// 懸浮提示
const tooltipCard = ref(null);
const tooltipStyle = ref({});

// 防抖計時器
let searchTimer = null;

// 牌組類型定義
const deckTypes = [
  { id: 0, name: "主牌組", max: 60, color: "bg-blue-500" },
  { id: 1, name: "額外牌組", max: 15, color: "bg-purple-500" },
  { id: 2, name: "備用牌組", max: 15, color: "bg-green-500" },
];

// 計算牌組統計
const deckStats = computed(() => {
  return deckTypes.map((type, index) => ({
    ...type,
    count: deckCards.value[index].length,
  }));
});

// 簡化篩選欄位名稱
const simplifiedAttrList = computed(() => {
  return attrList.value.map((attr) => ({
    ...attr,
    displayName: getSimplifiedAttrName(attr.attr_name),
  }));
});

// 簡化屬性名稱的函數
const getSimplifiedAttrName = (attrName) => {
  if (!attrName) return attrName;

  // 簡化常見的長名稱
  const nameMap = {
    "等級/階級/LINK": "等級",
    "等級/階級": "等級",
    "階級/LINK": "階級",
    攻擊力: "ATK",
    守備力: "DEF",
    卡片類型: "類型",
    屬性: "屬性",
    種族: "種族",
    稀有度: "稀有度",
  };

  return nameMap[attrName] || attrName;
};

// 初始化
// 客戶端初始化
onMounted(async () => {
  // SSR 數據已經在服務端獲取，這裡只需要客戶端特定的初始化
  console.log(
    "牌組編輯頁面已載入，環境數量:",
    envList.value.length,
    "類型數量:",
    typeList.value.length,
    "屬性數量:",
    attrList.value.length
  );

  try {
    isLoading.value = true;

    if (isEdit.value) {
      await loadDeckData();
    }

    await loadCardsList();
  } catch (error) {
    console.error("初始化失敗:", error);
  } finally {
    isLoading.value = false;
  }
});

// 🔧 修正：載入環境列表
const loadEnvList = async () => {
  try {
    const response = await getGoodsGroupEnvList(gameId.value);
    if (response.code === 200) {
      envList.value = response.data || [];
    }
  } catch (error) {
    console.error("載入環境列表失敗:", error);
  }
};

// 🔧 修正：載入類型列表
const loadTypeList = async () => {
  try {
    const response = await getGoodsGroupTypeList();
    if (response.code === 200) {
      typeList.value = response.data || [];
    }
  } catch (error) {
    console.error("載入類型列表失敗:", error);
  }
};

// 🔧 修正：載入屬性列表
const loadAttrList = async () => {
  try {
    const response = await getAttrList(gameId.value);
    if (response.code === 200) {
      attrList.value = (response.data || []).map((attr) => ({
        ...attr,
        selected: -1,
      }));
    }
  } catch (error) {
    console.error("載入屬性列表失敗:", error);
  }
};

// 🔧 修正：載入卡片列表
const loadCardsList = async (append = false) => {
  try {
    if (append) {
      isLoadingMore.value = true;
    } else {
      isLoading.value = true;
      currentPage.value = 1;
    }

    // 構築篩選參數
    const attrParams = {};
    attrList.value.forEach((attr) => {
      if (attr.selected !== undefined && attr.selected >= 0) {
        attrParams[attr.attr_id] = attr.options[attr.selected];
      }
    });

    const response = await getGoodsListByAttr({
      game_id: gameId.value,
      key_word: searchKeyword.value,
      params: JSON.stringify(attrParams),
      page: currentPage.value,
      page_nums: 50,
      is_identify: isIdentify.value,
    });

    if (response.code === 200) {
      const newCards = response.data?.list || [];
      if (append) {
        cardsList.value = [...cardsList.value, ...newCards];
      } else {
        cardsList.value = newCards;
      }
      totalCards.value = response.data?.total || 0;
      hasMoreCards.value = newCards.length === 50;
    }
  } catch (error) {
    console.error("載入卡片列表失敗:", error);
  } finally {
    isLoading.value = false;
    isLoadingMore.value = false;
  }
};

// 🔧 修正：載入牌組數據（編輯模式）
const loadDeckData = async () => {
  try {
    const response = await getCardGroupDetails(groupId.value);

    if (response.code === 200 && response.data) {
      deckData.value = {
        ...deckData.value,
        ...response.data,
      };

      // 處理卡片數據
      if (response.data.goods_info) {
        const goodsInfo = JSON.parse(response.data.goods_info);
        // 這裡需要根據實際數據結構處理卡片加載
        // 暫時省略，需要根據實際 API 響應調整
      }
    }
  } catch (error) {
    console.error("載入牌組數據失敗:", error);
  }
};

// 搜尋相關
const debounceSearch = () => {
  if (searchTimer) {
    clearTimeout(searchTimer);
  }
  searchTimer = setTimeout(() => {
    loadCardsList();
  }, 300);
};

const onAttributeChange = () => {
  debounceSearch();
};

// 卡片操作
const addCardToDeck = (card) => {
  const currentCount = getCardCountInDeck(card.goods_id);
  let targetDeck = activeTab.value;

  // 特殊卡片類型處理：tag_id=21 只能在額外牌組或備用牌組
  if (card.tag_id === 21 && activeTab.value === 0) {
    targetDeck = 1; // 自動切換到額外牌組
  }

  // 檢查各種限制
  if (deckCards.value[targetDeck].length >= deckTypes[targetDeck].max) {
    alert(`${deckTypes[targetDeck].name}已達上限`);
    return;
  }

  if (currentCount >= 3) {
    alert("每張卡最多只能加入3張");
    return;
  }

  if (card.ban_type === 1) {
    alert("此卡片已被禁止使用");
    return;
  }

  if (card.ban_type === 2 && currentCount >= 1) {
    alert("限制卡片只能加入1張");
    return;
  }

  if (card.ban_type === 3 && currentCount >= 2) {
    alert("準限制卡片只能加入2張");
    return;
  }

  // 添加卡片到目標牌組
  deckCards.value[targetDeck].push({ ...card });
};

const removeCardFromDeck = (cardId) => {
  const index = deckCards.value[activeTab.value].findIndex(
    (c) => c.goods_id === cardId
  );
  if (index !== -1) {
    deckCards.value[activeTab.value].splice(index, 1);
  }
};

const getCardCountInDeck = (cardId) => {
  return deckCards.value.flat().filter((c) => c.goods_id === cardId).length;
};

const getUniqueCardsInDeck = () => {
  const cardMap = new Map();
  deckCards.value[activeTab.value].forEach((card) => {
    const count = cardMap.get(card.goods_id) || 0;
    cardMap.set(card.goods_id, count + 1);
  });

  return Array.from(cardMap.entries()).map(([cardId, count]) => {
    const card = deckCards.value[activeTab.value].find(
      (c) => c.goods_id === Number(cardId)
    );
    return { ...card, count };
  });
};

// 滾動加載更多
const handleScroll = (event) => {
  const { scrollTop, scrollHeight, clientHeight } = event.target;
  if (
    scrollHeight - scrollTop - clientHeight < 100 &&
    hasMoreCards.value &&
    !isLoadingMore.value
  ) {
    currentPage.value++;
    loadCardsList(true);
  }
};

// 封面相關
const selectAsCover = (card) => {
  deckData.value.image = card.goods_thumb;
};

// 懸浮提示
const showCardTooltip = (card, event) => {
  tooltipCard.value = card;
  const rect = event.target.getBoundingClientRect();
  tooltipStyle.value = {
    left: `${rect.right + 10}px`,
    top: `${rect.top}px`,
  };
  showTooltip.value = true;
};

const hideCardTooltip = () => {
  showTooltip.value = false;
  tooltipCard.value = null;
};

// 禁限卡樣式
const getBanTypeClass = (banType) => {
  switch (banType) {
    case 1:
      return "bg-red-500 text-white";
    case 2:
      return "bg-orange-500 text-white";
    case 3:
      return "bg-yellow-500 text-black";
    default:
      return "";
  }
};

const getBanTypeText = (banType) => {
  switch (banType) {
    case 1:
      return "禁止";
    case 2:
      return "限制";
    case 3:
      return "準限";
    default:
      return "";
  }
};

// 🔧 修正：保存牌組
const saveDeck = async () => {
  if (!deckData.value.title.trim()) {
    alert("請輸入牌組名稱");
    return;
  }

  if (!deckData.value.env_id) {
    alert("請選擇環境");
    return;
  }

  if (!deckData.value.image) {
    alert("請選擇牌組封面");
    return;
  }

  const mainDeckCount = deckCards.value[0].length;
  if (mainDeckCount < 40 || mainDeckCount > 60) {
    alert("主牌組需要40-60張卡片");
    return;
  }

  try {
    isSaving.value = true;

    // 構築卡片數據
    const goodsInfo = {
      1: deckCards.value[0].concat(deckCards.value[1]), // 主牌組 + 額外牌組
      2: deckCards.value[2], // 備用牌組
    };

    const response = await updateGoodsGroup({
      group_id: groupId.value,
      title: deckData.value.title,
      desc: deckData.value.desc,
      env_id: deckData.value.env_id,
      image: deckData.value.image,
      game_id: gameId.value,
      is_identify: isIdentify.value,
      goods_info: JSON.stringify(goodsInfo),
    });

    if (response.code === 200) {
      alert("保存成功！");
      router.push("/my-groups");
    } else {
      alert(`保存失敗：${response.msg}`);
    }
  } catch (error) {
    console.error("保存牌組失敗:", error);
    alert("保存失敗，請稍後再試");
  } finally {
    isSaving.value = false;
  }
};

// 返回上一頁
const goBack = () => {
  router.back();
};

const selectCoverFromDeck = () => {
  // 刪除 console.log('請從牌組中的卡片選擇一張作為封面')
};
</script>

<style scoped>
/* 自定義滾動條 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #334155;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #64748b;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 響應式調整 */
@media (max-width: 1024px) {
  .grid.lg\\:grid-cols-3 {
    grid-template-columns: 1fr;
  }

  .grid.lg\\:grid-cols-4 {
    grid-template-columns: repeat(2, 1fr);
  }

  .grid.lg\\:grid-cols-8 {
    grid-template-columns: repeat(6, 1fr);
  }
}

@media (max-width: 768px) {
  .grid.grid-cols-6 {
    grid-template-columns: repeat(4, 1fr);
  }

  /* 手機版優化 */
  .mobile-safe-page {
    min-height: calc(100vh - 4rem) !important;
    min-height: calc(100dvh - 4rem) !important;
    max-width: 100vw !important;
    overflow-x: hidden !important;
    padding-bottom: 1rem !important;
  }

  .content-wrapper {
    height: calc(100vh - 4rem) !important;
    height: calc(100dvh - 4rem) !important;
    padding-bottom: 1rem !important;
    overflow-x: hidden !important;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch;
  }

  /* 确保所有容器不会产生水平滚动 */
  .container,
  [class*="container"] {
    max-width: 100vw !important;
    width: 100% !important;
    overflow-x: hidden !important;
    margin-left: auto !important;
    margin-right: auto !important;
  }

  .grid,
  .flex,
  [class*="grid"],
  [class*="flex"] {
    max-width: 100% !important;
    overflow-x: hidden !important;
  }

  /* 图片和媒体元素 */
  img,
  video,
  canvas,
  iframe {
    max-width: 100% !important;
    height: auto !important;
  }
}
</style>
