<!-- pages/Goodsgroup/index.vue -->
<template>
  <div class="p-2 md:p-3">
    <h2 class="text-lg md:text-xl font-bold mb-2 md:mb-4">牌組專區</h2>

    <!-- 環境選擇和搜索區域 -->
    <div class="bg-[#061224] rounded-lg p-2 md:p-3 mb-2 md:mb-3">
      <!-- 桌面版布局 -->
      <div class="hidden md:block space-y-3">
        <!-- 第一行：搜尋框 -->
        <el-input
          v-model="keyWord"
          placeholder="搜尋牌組名稱..."
          @keyup.enter="onSearchChange"
          @clear="onSearchChange"
          clearable
          size="large"
        >
          <template #suffix>
            <el-button @click="onSearchChange" :icon="Search" circle />
          </template>
        </el-input>

        <!-- 第二行：分類按鈕 + 環境選擇 -->
        <div class="flex items-center gap-3 flex-nowrap">
          <!-- 分類按鈕 -->
          <div class="flex-shrink-0">
            <el-radio-group
              v-model="isIdentify"
              @change="onCategoryChange"
              size="large"
            >
              <el-radio-button :value="0">全部</el-radio-button>
              <el-radio-button :value="2">比賽</el-radio-button>
              <el-radio-button :value="1">一般</el-radio-button>
            </el-radio-group>
          </div>

          <!-- 環境選擇下拉框 -->
          <div class="flex-shrink-0">
            <el-select
              v-model="selectedEnv"
              placeholder="選擇環境"
              @change="onEnvChange"
              style="width: 180px"
              size="large"
              clearable
            >
              <el-option
                v-for="env in envList"
                :key="env.env_id"
                :label="env.title"
                :value="env.env_id"
              />
            </el-select>
          </div>
        </div>
      </div>

      <!-- 手機版布局 -->
      <div class="md:hidden space-y-2">
        <!-- 搜尋框 -->
        <el-input
          v-model="keyWord"
          placeholder="搜尋牌組名稱..."
          @keyup.enter="onSearchChange"
          @clear="onSearchChange"
          clearable
          size="small"
        >
          <template #suffix>
            <el-button
              @click="onSearchChange"
              :icon="Search"
              circle
              size="small"
            />
          </template>
        </el-input>

        <!-- 分類按鈕 + 環境選擇 -->
        <div class="flex items-center gap-1">
          <el-radio-group
            v-model="isIdentify"
            @change="onCategoryChange"
            size="small"
            class="mobile-radio-group"
          >
            <el-radio-button :value="0">全部</el-radio-button>
            <el-radio-button :value="2">比賽</el-radio-button>
            <el-radio-button :value="1">一般</el-radio-button>
          </el-radio-group>

          <el-select
            v-model="selectedEnv"
            placeholder="選擇環境"
            @change="onEnvChange"
            class="flex-1"
            size="small"
          >
            <el-option
              v-for="env in envList"
              :key="env.env_id"
              :label="env.title"
              :value="env.env_id"
            />
          </el-select>
        </div>
      </div>

      <!-- 統計信息 - 縮小間隔 -->
      <div class="mt-2 pt-2 border-t border-gray-600/30">
        <!-- 桌面版統計信息 -->
        <div class="hidden md:flex gap-6 text-gray-400 text-sm">
          <div class="flex items-center gap-2">
            <i class="el-icon-date"></i>
            <span
              >當前環境：{{
                selectedEnv
                  ? envList.find((e) => e.env_id === selectedEnv)?.title
                  : "載入中..."
              }}</span
            >
          </div>
          <div class="flex items-center gap-2">
            <i class="el-icon-time"></i>
            <span>更新日期：{{ formatCurrentDate() }}</span>
          </div>
          <div class="flex items-center gap-2">
            <i class="el-icon-data-analysis"></i>
            <span>收錄牌組：{{ totalCount }} 套</span>
          </div>
        </div>

        <!-- 手機版緊湊統計信息 -->
        <div class="md:hidden">
          <div class="flex items-center justify-between text-gray-400 text-xs">
            <div class="flex items-center gap-1">
              <i class="el-icon-date text-xs"></i>
              <span class="truncate">{{
                selectedEnv
                  ? envList.find((e) => e.env_id === selectedEnv)?.title
                  : "載入中..."
              }}</span>
            </div>
            <div class="flex items-center gap-1">
              <i class="el-icon-time text-xs"></i>
              <span>{{ formatCurrentDate() }}</span>
            </div>
            <div class="flex items-center gap-1">
              <i class="el-icon-data-analysis text-xs"></i>
              <span>{{ totalCount }}套</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 牌組列表 -->
    <Suspense>
      <template #default>
        <div class="deck-list-grid">
          <AsyncDeckListComponent
            :loading="deckListLoading"
            :data="deckListData?.list || []"
            :totalItems="deckListData?.total || 0"
            :pageSize="pageSize"
            :currentPage="currentPage"
            @page-change="onPageChange"
            @deck-click="navigateToDetail"
            @title-click="onTitleClick"
          />
        </div>
      </template>
      <template #fallback>
        <div class="loading-state">
          <el-skeleton :rows="5" animated />
        </div>
      </template>
    </Suspense>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { useRouter } from "vue-router";
import { useAsyncData } from "#app";
import { ElMessage } from "element-plus";
import { useApi } from "~/composables/useApi";
import { ArrowLeft, Search } from "@element-plus/icons-vue";

// 初始化路由
const router = useRouter();

// 初始化API實例（在客戶端）
let GoodsgroupApi = null;
let generalApi = null;

// 使用 SSR 數據獲取環境列表
const { data: envData } = await useFetch("/api/Goodsgroup/env-list", {
  method: "POST",
  body: {},
  default: () => ({ code: 500, data: [] }),
  // 啟用 SSR 以提高首屏載入速度和 SEO
});

// 使用 SSR 數據獲取牌組列表
const { data: deckData } = await useFetch("/api/Goodsgroup/getlist", {
  method: "POST",
  body: {
    game_id: 1,
    page: 1,
    page_nums: 20,
    is_identify: 0,
  },
  default: () => ({ code: 500, data: { list: [], total: 0 } }),
  // 啟用 SSR 以提高首屏載入速度和 SEO
});

// 基本狀態
const isLoadingCardList = ref(false);
const selectedGame = ref(1);
const selectedEnv = ref(null);
const isIdentify = ref(0); // 分類選擇：0（全部）、1（一般）、2（比賽）
const keyWord = ref("");
const currentPage = ref(1);
const pageSize = ref(20);
const totalCards = ref(0);
const components = { ArrowLeft, Search };

// 使用 computed 處理環境列表數據
const envList = computed(() => {
  const data = envData.value;
  if (data?.code === 200 && data?.data) {
    return data.data;
  }
  return [];
});

// 使用 computed 處理牌組列表數據
const deckList = computed(() => {
  const data = deckData.value;
  if (data?.code === 200 && data?.data?.list) {
    return data.data.list;
  }
  return [];
});

const totalCount = computed(() => {
  const data = deckData.value;
  if (data?.code === 200 && data?.data?.total) {
    return data.data.total;
  }
  return 0;
});

// 直接使用 ref 來管理列表數據
const deckListData = ref(null);
const deckListLoading = ref(false);

// 監聽數據請求的狀態，避免重複調用
const isDeckListLoading = ref(false);

// 載入初始數據
const loadInitialData = () => {
  if (!isDeckListLoading.value) {
    loadDeckList();
  }
};

// 環境載入函數
const loadEnvironments = async () => {
  if (!generalApi) {
    console.warn("API 實例尚未初始化");
    return;
  }

  try {
    const response = await generalApi.getEnvironmentList(
      selectedGame.value || 1
    );

    if (response && response.code === 200) {
      envList.value = response.data || [];

      // 如果環境列表不為空，設置默認選中的環境
      if (envList.value.length > 0 && !selectedEnv.value) {
        selectedEnv.value = envList.value[0].env_id;

        // 確保環境ID已設置後再載入數據
        setTimeout(() => {
          loadDeckList();
        }, 100);
      } else if (envList.value.length === 0) {
        console.warn("環境列表為空");
      }
    } else {
      console.warn("環境列表API響應不成功:", response);
      envList.value = [];
    }
  } catch (error) {
    console.error("載入環境列表失敗:", error);
  }
};

// 載入牌組列表數據
const loadDeckList = async () => {
  if (isDeckListLoading.value) return;

  if (!GoodsgroupApi) {
    console.warn("API 實例尚未初始化");
    return;
  }

  deckListLoading.value = true;
  isDeckListLoading.value = true;

  try {
    const params = {
      game_id: selectedGame.value,
      key_word: keyWord.value,
      env_id: selectedEnv.value,
      page: currentPage.value,
      page_size: pageSize.value,
    };

    // 修正識別條件參數名稱
    if (isIdentify.value !== 0) {
      params.is_identify = isIdentify.value;
    }

    const response = await GoodsgroupApi.getGoodsgroupList(params);

    if (response && response.code === 200) {
      // 處理列表數據
      deckListData.value = response.data;
      totalCount.value = response.data?.total || 0;
    } else {
      console.warn("牌組列表API響應不成功:", response);
      deckListData.value = { list: [], total: 0 };
      totalCount.value = 0;
    }
  } catch (error) {
    console.error("載入牌組列表失敗:", error);
    deckListData.value = { list: [], total: 0 };
    totalCount.value = 0;
  } finally {
    setTimeout(() => {
      deckListLoading.value = false;
      isDeckListLoading.value = false;
    }, 200);
  }
};

// 搜索變更處理
const onSearchChange = () => {
  currentPage.value = 1;
  loadDeckList();
};

// 標題點擊處理
const onTitleClick = (title) => {
  keyWord.value = title;
  currentPage.value = 1;
  loadDeckList();
};

// 日期格式化
const formatDate = (date) => {
  if (!date) return "";
  const d = new Date(date);
  return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(
    2,
    "0"
  )}-${String(d.getDate()).padStart(2, "0")}`;
};

// 獲取當前日期
const formatCurrentDate = () => {
  const now = new Date();
  return formatDate(now);
};

// 頁面變更
const onPageChange = (newPage) => {
  currentPage.value = newPage;
  loadDeckList();
};

// 環境變更
const onEnvChange = (newEnv) => {
  selectedEnv.value = newEnv;
  currentPage.value = 1;
  loadDeckList();
};

// 分類變更 - 修正邏輯
const onCategoryChange = (newCategory) => {
  isIdentify.value = newCategory;
  currentPage.value = 1;
  loadDeckList();
};

// 跳轉到牌組詳情頁
const goToGoodsgroupDetails = (goodsgroupId) => {
  navigateToDetail(goodsgroupId);
};

// 添加导航到详情页的方法
const navigateToDetail = (deck) => {
  if (deck && deck.group_id) {
    router.push(`/Goodsgroup/${deck.group_id}`);
  } else {
    ElMessage.warning("無效的牌組數據");
  }
};

// 處理 SSR 數據
if (envData.value?.code === 200 && envData.value?.data?.length > 0) {
  // 設置默認環境為第一個環境
  selectedEnv.value = envData.value.data[0].env_id;
}

// 客戶端初始化
onMounted(() => {
  // 初始化API實例（在客戶端）
  GoodsgroupApi = useApi();
  generalApi = useApi();

  // SSR 數據已經在服務端獲取，這裡只需要客戶端特定的初始化
  console.log(
    "牌組列表頁面已載入，環境數量:",
    envList.value.length,
    "牌組數量:",
    deckList.value.length
  );

  // 如果環境已設置但沒有牌組數據，則載入牌組數據
  if (selectedEnv.value && deckList.value.length === 0) {
    loadDeckList();
  }
});
</script>

<style scoped>
/* 手機版優化樣式 */
@media (max-width: 768px) {
  /* 緊湊的分類按鈕 */
  .mobile-radio-group :deep(.el-radio-button__inner) {
    padding: 6px 8px;
    font-size: 12px;
    min-height: auto;
  }

  /* 緊湊的選擇框 */
  :deep(.el-select.el-select--small .el-select__wrapper) {
    min-height: 28px;
    padding: 0 8px;
  }

  :deep(.el-select.el-select--small .el-select__placeholder) {
    font-size: 12px;
  }

  /* 緊湊的搜索框 */
  :deep(.el-input--small .el-input__wrapper) {
    padding: 0 8px;
    min-height: 28px;
  }

  :deep(.el-input--small .el-input__inner) {
    font-size: 12px;
  }

  /* 統計信息區域 */
  .truncate {
    max-width: 80px;
  }
}

/* 加載狀態樣式 */
.loading-state {
  padding: 20px;
  text-align: center;
  background: rgba(6, 18, 36, 0.5);
  border-radius: 8px;
  margin: 10px 0;
}
@import "./styles.css";

/* 添加額外樣式 */
.page-header {
  position: relative;
  padding: 8px 0;
  overflow: hidden;
  background: linear-gradient(
    180deg,
    rgba(24, 28, 40, 0.9) 0%,
    rgba(9, 16, 32, 0.8) 100%
  );
  border-bottom: 1px solid rgba(255, 215, 0, 0.2);
}

.header-content {
  display: flex;
  justify-content: center;
  align-items: center;
}

.page-title {
  font-size: 24px;
  font-weight: 800;
  letter-spacing: 1px;
  margin: 0;
  text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
  background: linear-gradient(to right, #fff, #ffd700);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.meta-bar {
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  background: rgba(15, 23, 42, 0.6);
  border-radius: 10px;
  padding: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.env-selector {
  background: rgba(20, 30, 48, 0.5);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid rgba(30, 144, 255, 0.15);
  backdrop-filter: blur(8px);
}

.env-selector-title {
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  font-weight: 600;
}

.search-input :deep(.el-input__wrapper) {
  background: rgba(20, 30, 48, 0.7);
  border: 1px solid rgba(30, 144, 255, 0.2);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.search-input :deep(.el-input__wrapper):hover {
  border-color: rgba(30, 144, 255, 0.4);
  box-shadow: 0 0 10px rgba(30, 144, 255, 0.1);
}

.search-input :deep(.el-input__wrapper.is-focus) {
  border-color: #1e90ff;
  box-shadow: 0 0 15px rgba(30, 144, 255, 0.2);
}

.search-input :deep(.el-input__inner) {
  color: rgba(255, 255, 255, 0.9);
  background: transparent;
}

.search-input :deep(.el-input__inner::placeholder) {
  color: rgba(255, 255, 255, 0.5);
}

.search-input :deep(.el-button) {
  background: rgba(30, 144, 255, 0.2);
  border: none;
  color: #1e90ff;
  transition: all 0.3s ease;
}

.search-input :deep(.el-button):hover {
  background: rgba(30, 144, 255, 0.3);
  color: #00bfff;
}

.deck-list-grid {
  @apply grid gap-4;
}

/* 自定義下拉框和輸入框樣式 */
:deep(.el-input__wrapper),
:deep(.el-select .el-input__wrapper) {
  @apply bg-[#0c2442] border-[#1e90ff40] shadow-none;
}

:deep(.el-input__wrapper.is-focus),
:deep(.el-select .el-input__wrapper.is-focus) {
  @apply border-[#1e90ff] shadow-none;
}

/* 自定義單選按鈕組樣式 */
:deep(.el-radio-group) {
  @apply bg-[#0c2442] rounded-lg overflow-hidden;
}

:deep(.el-radio-button__inner) {
  @apply bg-transparent border-[#1e90ff40] text-gray-400;
}

:deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
  @apply bg-[#1e90ff20] text-[#1e90ff] border-[#1e90ff];
}

/* 加載狀態樣式 */
.loading-state {
  @apply p-4 bg-[#061224] rounded-lg;
}

:deep(.el-skeleton) {
  --el-skeleton-color: rgba(30, 144, 255, 0.1);
  --el-skeleton-to-color: rgba(30, 144, 255, 0.2);
}
</style>
