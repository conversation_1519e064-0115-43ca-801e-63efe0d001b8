<!-- pages/Goodsgroup/similardeck.vue -->
<template>
  <div class="similar-deck-page">
    <!-- 頂部導航與標題區 -->
    <div class="page-header">
      <div class="container mx-auto px-2">
        <div class="header-content">
          <button @click="goBack" class="back-button">
            <i class="el-icon-arrow-left"></i>
            <span>返回</span>
          </button>
          <h1 class="page-title">{{ title }}</h1>
        </div>
      </div>
    </div>

    <!-- 主要內容區 -->
    <div class="container mx-auto py-2 px-2">
      <!-- 載入狀態 -->
      <div v-if="isLoading" class="loading-state">
        <el-skeleton :rows="5" animated />
      </div>

      <!-- 實際內容 -->
      <div v-else>
        <!-- 牌組概覽 -->
        <div class="deck-overview mb-3">
          <div class="deck-image">
            <img :src="firstCardGroup?.image || ''" alt="代表牌組圖片" />
          </div>
          <div class="deck-info">
            <div class="info-row">
              <div class="info-item">
                <div class="info-label">環境</div>
                <div class="info-value">{{ envTitle }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">牌組數量</div>
                <div class="info-value">{{ cardGroups.length }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">佔上位比例</div>
                <div class="info-value highlight">
                  {{ percentage.toFixed(2) }}%
                </div>
              </div>
            </div>
            <div class="action-row">
              <button @click="goToDeckDetails" class="deck-details-btn">
                <i class="el-icon-view"></i>
                <span>查看牌組構築詳情</span>
              </button>
            </div>
          </div>
        </div>

        <!-- 走勢圖表 -->
        <div class="trend-chart mb-3">
          <div class="chart-header">
            <div class="section-title">使用率走勢</div>
          </div>
          <div class="chart-container">
            <canvas ref="chartCanvas"></canvas>
          </div>
        </div>

        <!-- 牌組列表 -->
        <div class="related-decks">
          <div class="section-title">相關牌組記錄</div>
          <div class="decks-list">
            <div
              v-for="item in cardGroups"
              :key="item.group_id"
              class="deck-item"
              @click="goToCardGroupDetails(item.group_id, item.user_id)"
            >
              <div class="deck-content">
                <div class="deck-desc">{{ item.desc || "無描述" }}</div>
                <div class="deck-meta">
                  <div class="user-info">
                    <div class="user-avatar">
                      <img :src="item.user_info?.headimg" alt="用戶頭像" />
                    </div>
                    <div class="user-name">
                      {{ item.user_info?.nickname || "未知用戶" }}
                    </div>
                  </div>
                  <div class="deck-env">{{ item.env_title }}</div>
                </div>
              </div>
              <div class="deck-indicator">
                <i class="el-icon-arrow-right"></i>
              </div>
            </div>
          </div>

          <!-- 暫無牌組提示 -->
          <div v-if="cardGroups.length === 0" class="empty-state">
            <i class="el-icon-warning-outline"></i>
            <p>暫無相關牌組記錄</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { useApi } from "~/composables/useApi";
import Chart from "chart.js/auto";

const route = useRoute();
const router = useRouter();
const api = useApi();

// 頁面數據
const title = ref(route.query.title || "相似牌組統計");
const envId = ref(parseInt(route.query.env_id) || 0);
const gameId = ref(parseInt(route.query.game_id) || 1); // 默認為遊戲王

// 使用 SSR 數據獲取牌組列表
const { data: cardGroupsData } = await useFetch("/api/Goodsgroup/getlist", {
  method: "POST",
  body: {
    key_word: title.value,
    env_id: envId.value,
    game_id: gameId.value,
    is_identify: 2, // 上位牌組
    page: 1,
    page_size: 20,
  },
  default: () => ({ code: 500, data: { list: [] } }),
  // 啟用 SSR 以提高首屏載入速度和 SEO
});

// 使用 SSR 數據獲取牌組百分比數據
const { data: percentageDataFromServer } = await useFetch(
  "/api/Goodsgroup/deck-percentage",
  {
    method: "POST",
    body: {
      title: title.value,
      env_id: envId.value,
      game_id: gameId.value,
    },
    default: () => ({ code: 500, data: { percentages: [] } }),
    // 啟用 SSR 以提高首屏載入速度和 SEO
  }
);

// 狀態管理
const isLoading = ref(false);
const chartCanvas = ref(null);
let chartInstance = null;

// 使用 computed 處理牌組列表數據
const cardGroups = computed(() => {
  const data = cardGroupsData.value;
  if (data?.code === 200 && data?.data?.list) {
    return data.data.list;
  }
  return [];
});

const envTitle = computed(() => {
  const groups = cardGroups.value;
  if (groups.length > 0) {
    return groups[0].env_title || "未知環境";
  }
  return "未知環境";
});

// 使用 computed 處理百分比數據
const percentageData = computed(() => {
  const data = percentageDataFromServer.value;
  if (data?.code === 200 && data?.data?.percentages) {
    return data.data.percentages;
  }
  return [];
});

const percentage = computed(() => {
  const data = percentageData.value;
  if (data.length > 0) {
    return data[data.length - 1].percentage || 0;
  }
  return 0;
});

// 計算屬性
const firstCardGroup = computed(() => cardGroups.value[0] || null);

// 日期格式化
const formatDate = (date) => {
  if (!date) return null;
  const d = new Date(date);
  return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(
    2,
    "0"
  )}-${String(d.getDate()).padStart(2, "0")}`;
};

// 獲取牌組列表
const fetchCardGroups = async () => {
  try {
    const params = {
      key_word: title.value,
      env_id: envId.value,
      game_id: gameId.value,
      is_identify: 2, // 上位牌組
      page: 1,
      page_size: 20,
    };

    const response = await api.getGoodsgroupList(params);

    if (response && response.code === 200 && response.data) {
      cardGroups.value = response.data.list || [];
      envTitle.value = cardGroups.value[0]?.env_title || "未知環境";
    } else {
      cardGroups.value = [];
      ElMessage.warning(response?.msg || "無法獲取牌組列表");
    }
  } catch (error) {
    ElMessage.error("獲取牌組列表失敗");
    cardGroups.value = [];
  }
};

// 獲取牌組百分比數據
const fetchDeckPercentage = async () => {
  try {
    const params = {
      title: title.value,
      env_id: envId.value,
      game_id: gameId.value,
    };

    const response = await api.getDeckPercentage(params);

    if (response && response.code === 200 && response.data) {
      percentageData.value = response.data.percentages || [];
      if (percentageData.value.length > 0) {
        // 使用最後一條數據的百分比
        percentage.value =
          percentageData.value[percentageData.value.length - 1].percentage || 0;
      }
    } else {
      percentageData.value = [];
      percentage.value = 0;
    }
  } catch (error) {
    percentageData.value = [];
    percentage.value = 0;
  }
};

// 渲染圖表
const renderChart = async (retryCount = 5, delay = 100) => {
  if (!percentageData.value.length || !chartCanvas.value) {
    if (retryCount > 0) {
      setTimeout(async () => {
        await nextTick();
        await renderChart(retryCount - 1, delay * 2);
      }, delay);
    }
    return;
  }

  await nextTick();

  const ctx = chartCanvas.value.getContext("2d");
  const maxPercentage = Math.max(
    ...percentageData.value.map((d) => d.percentage || 0),
    1
  );

  if (chartInstance) chartInstance.destroy();

  chartInstance = new Chart(ctx, {
    type: "line",
    data: {
      labels: percentageData.value.map((d) => d.date),
      datasets: [
        {
          data: percentageData.value.map((d) => d.percentage),
          borderColor: "#1e90ff",
          backgroundColor: "rgba(30, 144, 255, 0.1)",
          fill: true,
          tension: 0.4,
          borderWidth: 3,
        },
      ],
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        x: {
          display: true,
          grid: {
            color: "rgba(255, 255, 255, 0.1)",
          },
          ticks: {
            maxTicksLimit: 8,
            color: "rgba(255, 255, 255, 0.7)",
            callback: (value, index) => {
              const date = percentageData.value[index]?.date;
              return date ? date.slice(5) : "";
            },
          },
        },
        y: {
          max: Math.ceil(maxPercentage * 1.2),
          beginAtZero: true,
          grid: {
            color: "rgba(255, 255, 255, 0.1)",
          },
          ticks: {
            color: "rgba(255, 255, 255, 0.7)",
            callback: (val) => `${val}%`,
          },
        },
      },
      plugins: {
        legend: { display: false },
        tooltip: {
          backgroundColor: "rgba(0, 0, 20, 0.8)",
          titleColor: "#fff",
          bodyColor: "#1e90ff",
          borderColor: "#1e90ff",
          borderWidth: 1,
          callbacks: {
            label: (context) => `${context.raw}%`,
          },
        },
      },
    },
  });
};

// 跳轉到牌組詳情頁面
const goToCardGroupDetails = (groupId, userId) => {
  router.push(`/Goodsgroup/${groupId}`);
};

// 跳轉到牌組構築詳情頁面
const goToDeckDetails = () => {
  // 使用title作為參數
  let deckTitle = title.value;

  // 如果title為空，則嘗試使用其他來源
  if (!deckTitle && cardGroups.value.length > 0) {
    // 嘗試從卡組數據中找出title或其他可用的標識符
    const firstDeck = cardGroups.value[0];
    if (firstDeck.title) {
      deckTitle = firstDeck.title;
    } else if (firstDeck.name) {
      deckTitle = firstDeck.name;
    }
  }

  // 如果仍然沒有找到有效的title，使用一個默認值但打印警告
  if (!deckTitle) {
    console.warn("無法獲取有效的title，使用預設值");
    deckTitle = "未知牌組";
  }

  router.push({
    path: "/Goodsgroup/deckdetails",
    query: {
      title: deckTitle,
      env_id: envId.value,
      game_id: gameId.value,
    },
  });
};

// 返回上一頁
const goBack = () => {
  router.back();
};

// 客戶端初始化
onMounted(async () => {
  // SSR 數據已經在服務端獲取，這裡只需要客戶端特定的初始化
  console.log(
    "相似牌組統計頁面已載入，牌組數量:",
    cardGroups.value.length,
    "百分比數據點:",
    percentageData.value.length
  );

  try {
    // 渲染圖表
    renderChart();
  } catch (error) {
    console.error("圖表渲染失敗:", error);
  }
});

// 監聽百分比數據變化
watch(
  () => percentageData.value,
  async () => {
    renderChart();
  },
  { deep: true }
);
</script>

<style scoped>
/* 頁面整體佈局 */
.similar-deck-page {
  background-color: #091020;
  background-image: radial-gradient(
      circle at 20% 35%,
      rgba(30, 144, 255, 0.1) 0%,
      transparent 40%
    ),
    radial-gradient(
      circle at 80% 10%,
      rgba(90, 30, 160, 0.08) 0%,
      transparent 40%
    );
  min-height: 100vh;
  color: #fff;
  font-family: "Noto Sans TC", sans-serif;
}

/* 頁面頭部 */
.page-header {
  position: relative;
  padding: 8px 0;
  background: linear-gradient(
    180deg,
    rgba(24, 28, 40, 0.9) 0%,
    rgba(9, 16, 32, 0.8) 100%
  );
  border-bottom: 1px solid rgba(30, 144, 255, 0.2);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(30, 144, 255, 0.1);
  border: 1px solid rgba(30, 144, 255, 0.2);
  border-radius: 20px;
  padding: 5px 12px;
  color: #fff;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 14px;
}

.back-button:hover {
  background: rgba(30, 144, 255, 0.2);
  transform: translateY(-2px);
}

.page-title {
  font-size: 24px;
  font-weight: 800;
  letter-spacing: 1px;
  margin: 0;
  text-shadow: 0 0 10px rgba(30, 144, 255, 0.5);
  background: linear-gradient(to right, #fff, #1e90ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  flex: 1;
  text-align: center;
}

/* 載入狀態 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  min-height: 300px;
  background: rgba(14, 22, 40, 0.7);
  border-radius: 16px;
  border: 1px solid rgba(30, 144, 255, 0.15);
}

/* 空狀態 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  text-align: center;
  color: rgba(255, 255, 255, 0.5);
}

.empty-state i {
  font-size: 48px;
  color: rgba(30, 144, 255, 0.3);
  margin-bottom: 12px;
}

.empty-state p {
  font-size: 16px;
}

/* 牌組概覽 */
.deck-overview {
  display: flex;
  background: rgba(14, 22, 40, 0.7);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(30, 144, 255, 0.15);
  gap: 20px;
  transition: all 0.3s ease;
}

.deck-overview:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.3);
  border-color: rgba(30, 144, 255, 0.3);
}

.deck-image {
  width: 160px;
  height: 160px;
  border-radius: 12px;
  overflow: hidden;
  border: 2px solid rgba(30, 144, 255, 0.3);
  flex-shrink: 0;
  background: rgba(0, 0, 0, 0.3);
}

.deck-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.deck-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.info-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
}

.info-value {
  font-size: 24px;
  font-weight: bold;
  color: #fff;
}

.info-value.highlight {
  color: #1e90ff;
  font-size: 28px;
  text-shadow: 0 0 10px rgba(30, 144, 255, 0.5);
}

.action-row {
  margin-top: 15px;
  display: flex;
  justify-content: center;
}

.deck-details-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background: rgba(30, 144, 255, 0.2);
  border: 1px solid rgba(30, 144, 255, 0.3);
  border-radius: 20px;
  padding: 8px 16px;
  color: #fff;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
}

.deck-details-btn:hover {
  background: rgba(30, 144, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(30, 144, 255, 0.2);
}

/* 走勢圖表 */
.trend-chart {
  background: rgba(14, 22, 40, 0.7);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(30, 144, 255, 0.15);
  transition: all 0.3s ease;
}

.trend-chart:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.3);
  border-color: rgba(30, 144, 255, 0.3);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
}

.section-title {
  font-size: 20px;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.9);
  position: relative;
  display: inline-block;
  margin-bottom: 5px;
}

.section-title:after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 40px;
  height: 2px;
  background: rgba(30, 144, 255, 0.5);
}

.chart-container {
  height: 300px;
  width: 100%;
  position: relative;
}

/* 牌組列表 */
.related-decks {
  background: rgba(14, 22, 40, 0.7);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(30, 144, 255, 0.15);
}

.decks-list {
  margin-top: 15px;
}

.deck-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  border-bottom: 1px solid rgba(30, 144, 255, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  margin-bottom: 10px;
}

.deck-item:hover {
  background: rgba(30, 144, 255, 0.1);
  transform: translateY(-2px);
}

.deck-content {
  flex: 1;
}

.deck-desc {
  font-size: 16px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 8px;
}

.deck-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  overflow: hidden;
  border: 1px solid rgba(30, 144, 255, 0.3);
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-name {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

.deck-env {
  font-size: 14px;
  color: rgba(30, 144, 255, 0.8);
  padding: 2px 8px;
  background: rgba(30, 144, 255, 0.1);
  border-radius: 4px;
  border: 1px solid rgba(30, 144, 255, 0.2);
}

.deck-indicator {
  color: rgba(255, 255, 255, 0.4);
  margin-left: 10px;
  transition: all 0.3s ease;
}

.deck-item:hover .deck-indicator {
  color: #1e90ff;
  transform: translateX(5px);
}

/* 響應式調整 */
@media (max-width: 768px) {
  .deck-overview {
    flex-direction: column;
  }

  .deck-image {
    width: 100%;
    height: auto;
    aspect-ratio: 16/9;
  }

  .chart-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .date-filter {
    width: 100%;
  }

  .custom-date-picker {
    width: 100%;
  }

  .deck-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
