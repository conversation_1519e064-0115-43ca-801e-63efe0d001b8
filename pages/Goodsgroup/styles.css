/* 頁面整體佈局 */
.card-group-page {
    background-color: #091020;
    background-image: 
        radial-gradient(circle at 20% 35%, rgba(30, 144, 255, 0.15) 0%, transparent 40%),
        radial-gradient(circle at 80% 10%, rgba(90, 30, 160, 0.1) 0%, transparent 40%);
    min-height: 100vh;
    color: #fff;
    font-family: 'Noto Sans TC', sans-serif;
}
/* 頁面頭部 */
.page-header {
    position: relative;
    padding: 32px 0;
    overflow: hidden;
    background: linear-gradient(180deg, rgba(20, 30, 60, 0.9) 0%, rgba(9, 16, 32, 0.8) 100%);
    border-bottom: 1px solid rgba(30, 144, 255, 0.3);
}

.page-header::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(30, 144, 255, 0.8) 50%, 
        transparent 100%);
    box-shadow: 0 0 15px rgba(30, 144, 255, 0.7);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.back-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    color: rgba(255, 255, 255, 0.8);
    background: rgba(30, 144, 255, 0.1);
    border: 1px solid rgba(30, 144, 255, 0.3);
    border-radius: 20px;
    transition: all 0.3s ease;
    backdrop-filter: blur(8px);
    font-size: 14px;
    cursor: pointer;
}

.back-button:hover {
    background: rgba(30, 144, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.back-button .el-icon {
    font-size: 16px;
}

.header-title-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 auto;
}

.page-title {
    font-size: 36px;
    font-weight: 800;
    letter-spacing: 2px;
    margin-bottom: 8px;
    text-shadow: 0 0 20px rgba(30, 144, 255, 0.5);
    background: linear-gradient(to right, #fff, #1e90ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
}

.header-subtitle {
    color: rgba(255, 255, 255, 0.6);
    font-size: 16px;
    font-weight: 300;
    letter-spacing: 1px;
    margin-bottom: 16px;
}

/* 頂部元數據區 - 優化為單行，增加Tier表按鈕 */
.meta-bar {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 24px;
    background: rgba(20, 30, 48, 0.5);
    border-radius: 16px;
    padding: 20px;
    border: 1px solid rgba(30, 144, 255, 0.15);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(8px);
    gap: 20px;
}

.meta-item {
    display: flex;
    align-items: center;
    margin-right: 24px;
}

.meta-icon {
    width: 40px;
    height: 40px;
    border-radius: 12px;
    background: rgba(30, 144, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 18px;
    color: #1e90ff;
    border: 1px solid rgba(30, 144, 255, 0.3);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.meta-text {
    display: flex;
    flex-direction: column;
}

.meta-label {
    color: rgba(255, 255, 255, 0.5);
    font-size: 12px;
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.meta-value {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
    font-size: 16px;
}

/* Tier表按鈕樣式 */
.tier-button {
    cursor: pointer;
    transition: all 0.3s ease;
    margin-left: auto;
}

.tier-button:hover .meta-icon {
    background: rgba(30, 144, 255, 0.2);
    transform: translateY(-2px);
}

.tier-icon {
    color: gold;
}

.meta-tabs {
    margin-left: auto;
}

/* 環境選擇器 */
.env-selector {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
    background: rgba(20, 30, 48, 0.5);
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 32px;
    border: 1px solid rgba(30, 144, 255, 0.15);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(8px);
}

.env-selector-title {
    color: rgba(255, 255, 255, 0.8);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    padding-right: 16px;
    margin-right: 16px;
    font-size: 16px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.date-range-picker,
.deck-search {
    margin-left: auto;
    min-width: 320px;
}

.date-range-title,
.filter-title {
    color: rgba(255, 255, 255, 0.6);
    margin-right: 12px;
    font-size: 14px;
}

.filter-category .el-radio-group {
    display: flex;
    gap: 8px;
}

.filter-category .el-radio-button {
    margin-right: 0;
}

.filter-category .el-radio-button__inner {
    border-radius: 20px;
}

/* 主要内容网格 */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 24px;
    width: 100%;
    margin-bottom: 32px;
}

@media (max-width: 950px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
}

.charts-column, 
.popular-decks-column, 
.deck-list-section {
    width: 100%;
    display: flex;
    flex-direction: column;
}

.chart-container {
    min-height: 350px;
    height: 350px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(20, 30, 48, 0.7);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(30, 144, 255, 0.2);
    backdrop-filter: blur(8px);
    transition: all 0.3s ease;
}

.chart-wrapper {
    width: 100%;
    height: 100%;
    min-height: 350px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.chart-wrapper canvas {
    width: 100% !important;
    height: auto !important;
    max-height: 320px;
}

/* 牌組列表區域 */
.deck-list-section {
    grid-column: 1 / -1;
    width: 100%;
}

/* 卡片容器 */
.card-container {
    background: rgba(20, 30, 48, 0.7);
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(30, 144, 255, 0.2);
    backdrop-filter: blur(8px);
    transition: all 0.3s ease;
    height: 100%;
    width: 100%;
}

.card-container:hover {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
    transform: translateY(-2px);
}

.card-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 16px;
    width: 100%;
}

.section-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    background-color: rgba(30, 144, 255, 0.2);
    color: #1e90ff;
    margin-right: 12px;
    border: 1px solid rgba(30, 144, 255, 0.4);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.section-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    flex-grow: 1;
}

/* 圖表區域 */
.pie-chart-card {
    flex: 1;
    position: relative;
    width: 100%;
}

.trend-chart-card {
    flex: 1;
    position: relative;
    width: 100%;
}

/* 熱門牌組區域 */
.popular-deck-list {
    max-height: 500px;
    overflow-y: auto;
    margin-top: 20px;
    padding-right: 8px;
}

.popular-deck-list::-webkit-scrollbar {
    width: 6px;
}

.popular-deck-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
}

.popular-deck-list::-webkit-scrollbar-thumb {
    background: rgba(30, 144, 255, 0.3);
    border-radius: 3px;
}

.popular-deck-list::-webkit-scrollbar-thumb:hover {
    background: rgba(30, 144, 255, 0.5);
}

.popular-deck-item {
    display: flex;
    align-items: center;
    padding: 16px;
    background: rgba(15, 25, 40, 0.6);
    border-radius: 12px;
    margin-bottom: 12px;
    border: 1px solid rgba(30, 144, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.popular-deck-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    border-color: rgba(30, 144, 255, 0.3);
}

.popular-deck-item::after {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 0;
    background: linear-gradient(to bottom, #1e90ff, #00bfff);
    transition: width 0.3s ease;
}

.popular-deck-item:hover::after {
    width: 4px;
}

.deck-rank {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: rgba(30, 60, 90, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 16px;
    color: white;
    font-size: 18px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 1;
}

.rank-1 {
    background: linear-gradient(135deg, #ffd700, #ffa500);
    color: #fff;
}

.rank-2 {
    background: linear-gradient(135deg, #c0c0c0, #a0a0a0);
    color: #fff;
}

.rank-3 {
    background: linear-gradient(135deg, #cd7f32, #8b4513);
    color: #fff;
}

.deck-image {
    width: 70px;
    height: 70px;
    border-radius: 12px;
    overflow: hidden;
    margin-right: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.3);
    z-index: 1;
}

.deck-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.5s ease;
}

.deck-percentage {
    margin-left: auto;
    font-size: 20px;
    font-weight: bold;
    color: #1e90ff;
    background: rgba(30, 144, 255, 0.1);
    padding: 8px 16px;
    border-radius: 20px;
    border: 1px solid rgba(30, 144, 255, 0.3);
    min-width: 70px;
    text-align: center;
    z-index: 1;
}

/* 載入和空狀態 */
.loading-container,
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
}

.empty-state i {
    font-size: 48px;
    color: rgba(255, 255, 255, 0.2);
    margin-bottom: 16px;
}

.empty-state p {
    color: rgba(255, 255, 255, 0.6);
    font-size: 16px;
}

/* 個人牌組按鈕 */
.my-deck-button-container {
    display: flex;
    justify-content: center;
    margin: 32px 0;
    perspective: 1000px;
}

.my-deck-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 16px 32px;
    font-size: 16px;
    font-weight: 600;
    color: white;
    background: linear-gradient(135deg, #1e90ff, #00bfff);
    border: none;
    border-radius: 30px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 10px 25px rgba(30, 144, 255, 0.3);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.my-deck-button::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.2), transparent);
    transform: translateX(-100%);
    transition: transform 0.8s cubic-bezier(0.65, 0.05, 0.36, 1);
    pointer-events: none;
}

.my-deck-button:hover {
    transform: translateY(-5px) rotateX(10deg);
    box-shadow: 0 15px 35px rgba(30, 144, 255, 0.4);
}

.my-deck-button:hover::before {
    transform: translateX(100%);
}

.my-deck-button-content {
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    gap: 8px;
}

.my-deck-button i {
    font-size: 20px;
}

.pulse-effect {
    animation: pulse 2s infinite;
    box-shadow: 0 0 0 rgba(30, 144, 255, 0.4);
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(30, 144, 255, 0.4);
    }
    70% {
        box-shadow: 0 0 0 15px rgba(30, 144, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(30, 144, 255, 0);
    }
}

/* Tier List 樣式 */
.tier-list-dialog :deep(.el-dialog) {
    background-color: #051029;
    border-radius: 20px;
    overflow: hidden;
    border: 1px solid rgba(30, 144, 255, 0.3);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.tier-list-dialog :deep(.el-dialog__header) {
    display: none;
}

.tier-list-dialog :deep(.el-dialog__body) {
    padding: 0;
}

.tier-list-container {
    padding: 40px;
    background: linear-gradient(135deg, rgba(5, 16, 41, 0.9), rgba(10, 20, 40, 0.9));
    color: #fff;
}

.tier-list-header {
    text-align: center;
    margin-bottom: 40px;
    position: relative;
}

.tier-list-title {
    font-size: 48px;
    font-weight: 800;
    letter-spacing: 4px;
    background: linear-gradient(to right, #fff, #1e90ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 0 20px rgba(30, 144, 255, 0.5);
    margin-bottom: 8px;
    text-transform: uppercase;
}

.tier-list-subtitle {
    color: rgba(255, 255, 255, 0.6);
    font-size: 16px;
}

.tier-description {
    display: flex;
    justify-content: space-between;
    margin-bottom: 40px;
    background: rgba(14, 22, 40, 0.7);
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(30, 144, 255, 0.2);
    backdrop-filter: blur(8px);
}

.tier-explanation {
    flex: 3;
    padding-right: 20px;
}

.explanation-item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.explanation-text {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    margin-left: 16px;
    line-height: 1.5;
}

.tier-updates {
    flex: 2;
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    padding-left: 24px;
}

.update-title {
    font-size: 18px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 16px;
    letter-spacing: 1px;
    position: relative;
    padding-bottom: 8px;
}

.update-title:after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 2px;
    background: rgba(30, 144, 255, 0.5);
}

.update-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.update-icon {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 12px;
    font-size: 14px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.update-icon.up {
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.1), rgba(46, 204, 113, 0.3));
    color: #2ecc71;
    border: 1px solid rgba(46, 204, 113, 0.4);
}

.update-icon.down {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(231, 76, 60, 0.3));
    color: #e74c3c;
    border: 1px solid rgba(231, 76, 60, 0.4);
}

.update-icon.new {
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.1), rgba(52, 152, 219, 0.3));
    color: #3498db;
    border: 1px solid rgba(52, 152, 219, 0.4);
}

.update-text {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
}

.tier-group {
    margin-bottom: 40px;
    background: rgba(14, 22, 40, 0.7);
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(30, 144, 255, 0.2);
    backdrop-filter: blur(8px);
    transition: all 0.3s ease;
}

.tier-group:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.3);
    border-color: rgba(30, 144, 255, 0.4);
}

/* 不同 Tier 等級的顏色 */
.tier-1-group {
    border-left: 5px solid #ffd700;
}

.tier-2-group {
    border-left: 5px solid #c0c0c0;
}

.tier-3-group {
    border-left: 5px solid #cd7f32;
}

.tier-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 16px;
}

.tier-badge {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 800;
    margin-right: 16px;
    color: white;
    font-size: 24px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.tier-1 {
    background: linear-gradient(135deg, #ffd700, #ffa500);
    border: 2px solid rgba(255, 215, 0, 0.6);
}

.tier-2 {
    background: linear-gradient(135deg, #c0c0c0, #a0a0a0);
    border: 2px solid rgba(192, 192, 192, 0.6);
}

.tier-3 {
    background: linear-gradient(135deg, #cd7f32, #8b4513);
    border: 2px solid rgba(205, 127, 50, 0.6);
}

.tier-potential {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
    border: 2px solid rgba(155, 89, 182, 0.6);
}

.tier-title {
    font-size: 24px;
    font-weight: 700;
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
    letter-spacing: 1px;
}

.tier-line {
    flex-grow: 1;
    height: 1px;
    background: linear-gradient(to right, rgba(255, 255, 255, 0.2), transparent);
    margin-left: 16px;
}

.tier-decks-wrapper {
    max-height: 200px;
    overflow-x: auto;
    padding-bottom: 10px;
}

.tier-decks-wrapper::-webkit-scrollbar {
    height: 6px;
}

.tier-decks-wrapper::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
}

.tier-decks-wrapper::-webkit-scrollbar-thumb {
    background: rgba(30, 144, 255, 0.3);
    border-radius: 3px;
}

.tier-decks-wrapper::-webkit-scrollbar-thumb:hover {
    background: rgba(30, 144, 255, 0.5);
}

.tier-decks {
    display: flex;
    gap: 16px;
}

.tier-deck-item {
    width: 120px;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.tier-deck-item:hover {
    transform: translateY(-5px);
}

.tier-deck-image {
    width: 100px;
    height: 100px;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.3);
}

.tier-deck-item:hover .tier-deck-image {
    border-color: rgba(30, 144, 255, 0.4);
}

.tier-deck-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.tier-deck-name {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* UI 控件樣式 */
:deep(.el-radio-button__inner) {
    background: rgba(20, 30, 48, 0.7);
    border-color: rgba(30, 144, 255, 0.3);
    color: rgba(255, 255, 255, 0.7);
    transition: all 0.3s ease;
    padding: 10px 20px;
}

:deep(.el-radio-button__orig-radio:checked + .el-radio-button__inner) {
    background: linear-gradient(135deg, #1e90ff, #00bfff);
    border-color: #1e90ff;
    color: white;
    box-shadow: 0 0 10px rgba(30, 144, 255, 0.4);
    font-weight: 600;
}

:deep(.el-select .el-input__inner) {
    background: rgba(20, 30, 48, 0.7);
    border-color: rgba(30, 144, 255, 0.3);
    color: rgba(255, 255, 255, 0.9);
}

:deep(.el-select .el-input__suffix) {
    color: #1e90ff;
}

:deep(.el-select-dropdown) {
    background: rgba(12, 20, 35, 0.95);
    border: 1px solid rgba(30, 144, 255, 0.3);
}

:deep(.el-select-dropdown__item) {
    color: rgba(255, 255, 255, 0.7);
}

:deep(.el-select-dropdown__item.hover),
:deep(.el-select-dropdown__item:hover) {
    background: rgba(30, 144, 255, 0.1);
    color: #1e90ff;
}

:deep(.el-select-dropdown__item.selected) {
    background: rgba(30, 144, 255, 0.2);
    color: #1e90ff;
    font-weight: 600;
}

:deep(.el-date-editor) {
    background: rgba(20, 30, 48, 0.7);
    border-color: rgba(30, 144, 255, 0.3);
}

:deep(.el-date-editor .el-range-input) {
    background: transparent;
    color: rgba(255, 255, 255, 0.9);
}

:deep(.el-date-editor .el-range-separator) {
    color: rgba(255, 255, 255, 0.5);
}

:deep(.el-date-editor .el-range__icon) {
    color: #1e90ff;
}

:deep(.el-input__inner) {
    background: rgba(20, 30, 48, 0.7);
    border-color: rgba(30, 144, 255, 0.3);
    color: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
}

:deep(.el-input__prefix) {
    color: rgba(30, 144, 255, 0.7);
}

:deep(.el-input__suffix) {
    color: rgba(30, 144, 255, 0.7);
}

:deep(.el-input__clear) {
    color: rgba(255, 255, 255, 0.7);
    background: rgba(0, 0, 0, 0.3);
}

:deep(.el-pagination) {
    text-align: center;
    margin-top: 30px;
}

:deep(.el-pagination.is-background .el-pager li) {
    background: rgba(20, 30, 48, 0.7);
    color: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(30, 144, 255, 0.2);
}

:deep(.el-pagination.is-background .el-pager li:not(.disabled).active) {
    background: linear-gradient(135deg, #1e90ff, #00bfff);
    color: white;
    border-color: #1e90ff;
}

:deep(.el-pagination.is-background .btn-next),
:deep(.el-pagination.is-background .btn-prev) {
    background: rgba(20, 30, 48, 0.7);
    color: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(30, 144, 255, 0.2);
}

:deep(.el-pagination.is-background .btn-next:disabled),
:deep(.el-pagination.is-background .btn-prev:disabled) {
    background: rgba(20, 30, 48, 0.4);
    color: rgba(255, 255, 255, 0.3);
    border: 1px solid rgba(30, 144, 255, 0.1);
}

:deep(.el-skeleton) {
    --el-skeleton-color: rgba(30, 60, 90, 0.3);
    --el-skeleton-to-color: rgba(30, 144, 255, 0.2);
}

/* 響應式調整 */
@media (max-width: 1024px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .charts-column {
        order: 2;
    }

    .popular-decks-column {
        order: 1;
    }

    .tier-description {
        flex-direction: column;
    }

    .tier-explanation {
        padding-right: 0;
        margin-bottom: 20px;
    }

    .tier-updates {
        border-left: none;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        padding-left: 0;
        padding-top: 20px;
    }
}

/* 列表視圖修復 */
.deck-table-col {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0 8px;
}

.deck-table-col.env {
    width: 10%;
}

.deck-table-col.title {
    width: 20%;
}

.deck-table-col.desc {
    width: 45%;
}

.deck-table-col.type {
    width: 5%;
}

.deck-table-col.author {
    width: 10%;
}

.deck-table-col.time {
    width: 10%;
}

@media (max-width: 768px) {
    .meta-bar {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }

    .meta-item {
        margin-right: 0;
    }

    .meta-tabs {
        margin-left: 0;
        width: 100%;
    }

    .env-selector {
        flex-direction: column;
        align-items: flex-start;
    }

    .env-selector-title {
        padding-right: 0;
        padding-bottom: 10px;
        border-right: none;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        margin-bottom: 10px;
        width: 100%;
    }

    .deck-table-header,
    .deck-table-row {
        flex-direction: column;
        align-items: flex-start;
        padding: 16px;
    }

    .deck-table-col {
        width: 100% !important;
        margin-bottom: 8px;
        padding: 0;
    }

    .deck-table-col.title {
        font-size: 18px;
        margin-bottom: 12px;
    }

    .deck-table-col.type {
        position: absolute;
        top: 16px;
        right: 16px;
    }

    .date-range-picker,
    .deck-search {
        margin-left: 0;
        width: 100%;
        flex-direction: column;
        align-items: flex-start;
    }

    .filter-item {
        width: 100%;
        min-width: unset;
    }

    .filter-tabs,
    .filter-item.category-switch {
        margin-left: 0;
    }

    .tier-deck-item {
        width: 100px;
    }

    .tier-deck-image {
        width: 80px;
        height: 80px;
    }

    .tier-deck-name {
        font-size: 12px;
    }

    .my-deck-button {
        padding: 12px 20px;
        font-size: 14px;
    }

    .tier-list-container {
        padding: 20px;
    }

    .tier-list-title {
        font-size: 28px;
    }

    .tier-button-header {
        padding: 6px 16px;
        font-size: 14px;
    }

    .tier-button-text {
        font-size: 14px;
    }

    .tier-icon-header {
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 16px 0;
    }

    .page-title {
        font-size: 22px;
    }

    .header-subtitle {
        font-size: 12px;
    }

    .meta-icon {
        width: 32px;
        height: 32px;
        font-size: 16px;
    }

    .meta-value {
        font-size: 14px;
    }

    .stats-icon {
        width: 36px;
        height: 36px;
        font-size: 18px;
    }

    .stats-value {
        font-size: 18px;
    }

    .section-title {
        font-size: 16px;
    }

    .deck-image {
        width: 60px;
        height: 60px;
    }

    .deck-title {
        font-size: 14px;
    }

    .deck-stats {
        font-size: 12px;
    }

    .tier-group {
        padding: 15px;
    }

    .tier-badge {
        width: 40px;
        height: 40px;
        font-size: 20px;
    }

    .tier-title {
        font-size: 20px;
    }

    .explanation-item {
        margin-bottom: 8px;
    }

    .explanation-text {
        font-size: 12px;
    }

    .update-title {
        font-size: 14px;
    }

    .update-icon {
        width: 20px;
        height: 20px;
    }

    .update-text {
        font-size: 12px;
    }

    .tier-button-header {
        padding: 5px 12px;
    }

    .tier-button-text {
        font-size: 12px;
    }

    .tier-icon-header {
        font-size: 14px;
    }
}

/* 确保表格占满可用空间 */
.deck-table-view {
    width: 100%;
}

.deck-table-header,
.deck-table-row {
    width: 100%;
    display: flex;
}

/* 環境T表按鈕樣式 */
.tier-button-header {
    padding: 8px 20px;
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 140, 0, 0.3));
    border: 1px solid rgba(255, 215, 0, 0.4);
    border-radius: 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    transition: all 0.3s ease;
    margin-top: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.tier-button-header:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.3), rgba(255, 140, 0, 0.4));
}

.tier-button-text {
    color: rgba(255, 215, 0, 0.9);
    font-weight: 600;
    font-size: 16px;
    letter-spacing: 1px;
}

.tier-icon-header {
    color: gold;
    font-size: 18px;
}