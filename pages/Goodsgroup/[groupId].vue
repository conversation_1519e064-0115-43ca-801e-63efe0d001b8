<!-- pages/Goodsgroup/Groupdetail.vue -->
<template>
  <div class="deck-details-page">
    <!-- 頂部導航與標題區 -->
    <div class="page-header">
      <div class="container mx-auto px-2">
        <div class="header-content">
          <button @click="goBack" class="back-button">
            <i class="el-icon-arrow-left"></i>
            <span>返回</span>
          </button>
          <h1 class="page-title">{{ deckData.title || "牌組詳情" }}</h1>
        </div>
      </div>
    </div>

    <!-- 主要內容區 -->
    <div class="container mx-auto py-2 px-2">
      <!-- 載入狀態 -->
      <div v-if="isLoading" class="loading-state">
        <el-skeleton :rows="5" animated />
      </div>

      <!-- 空數據狀態 -->
      <div v-else-if="!deckData || !deckData.goods_info" class="empty-state">
        <i
          class="el-icon-warning-outline"
          style="font-size: 48px; color: rgba(30, 144, 255, 0.5)"
        ></i>
        <p>無法載入牌組數據</p>
      </div>

      <!-- 牌組數據載入成功 -->
      <div v-else>
        <!-- 牌組概覽 -->
        <div class="deck-overview mb-3">
          <div class="deck-image">
            <img :src="deckData.image || ''" alt="牌組圖片" />
          </div>
          <div class="deck-info">
            <div class="deck-creator">
              <div class="creator-avatar">
                <img :src="deckData.user_info?.headimg || ''" alt="用戶頭像" />
              </div>
              <div class="creator-info">
                <div class="creator-name">
                  {{ deckData.user_info?.nickname || "未知用戶" }}
                </div>
                <div class="creator-time">
                  發佈於 {{ formatDate(deckData.create_time) }}
                </div>
              </div>
            </div>
            <div class="deck-description">
              {{ deckData.desc || "暫無描述" }}
            </div>
            <div class="deck-actions">
              <el-button
                type="primary"
                size="small"
                @click="copyDeck"
                v-if="isLoggedIn"
              >
                <i class="el-icon-copy-document"></i> 複製牌組
              </el-button>
            </div>
          </div>
        </div>

        <!-- 牌組分頁 -->
        <div class="deck-tabs mb-3">
          <div
            class="deck-tab"
            :class="{ active: currentTab === 'main' }"
            @click="currentTab = 'main'"
          >
            主牌組 ({{ mainDeckCount }})
          </div>
          <div
            v-if="showExtraDeck"
            class="deck-tab"
            :class="{ active: currentTab === 'extra' }"
            @click="currentTab = 'extra'"
          >
            額外牌組 ({{ extraDeckCount }})
          </div>
          <div
            v-if="showExtraDeck"
            class="deck-tab"
            :class="{ active: currentTab === 'side' }"
            @click="currentTab = 'side'"
          >
            備牌 ({{ sideDeckCount }})
          </div>
        </div>

        <!-- 卡片網格 -->
        <div class="cards-grid mb-3">
          <div
            v-for="(card, index) in currentDeckCards"
            :key="`${card.goods_id}_${index}`"
            class="card-item-container"
          >
            <div
              class="card-item"
              @click="handleCardClick(card)"
              @mousemove="
                updateHoverPosition($event, card, `${card.goods_id}_${index}`)
              "
              @mouseleave="hideHover(`${card.goods_id}_${index}`)"
            >
              <div class="card-image">
                <img :src="card.goods_thumb || ''" alt="卡片圖片" />
                <div
                  v-if="card.ban_type"
                  class="ban-indicator"
                  :class="`ban-type-${card.ban_type}`"
                >
                  {{ getBanTypeText(card.ban_type) }}
                </div>
              </div>
            </div>
            <!-- 桌面版懸停詳情 -->
            <teleport to="body">
              <div
                class="card-hover-details desktop-hover"
                :style="
                  cardHoverStyles[`${card.goods_id}_${index}`] || {
                    display: 'none',
                  }
                "
              >
                <div class="card-hover-title">
                  {{ card.goods_title || "未知卡名" }}
                </div>
                <div class="card-hover-effect">
                  {{ card.effect || "無效果描述" }}
                </div>
              </div>
            </teleport>
          </div>
        </div>

        <!-- 手機版卡片詳情彈窗 -->
        <div
          v-if="showMobileModal"
          class="mobile-card-modal"
          @click.self="closeMobileModal"
        >
          <div class="mobile-modal-content">
            <button @click="closeMobileModal" class="mobile-modal-close">
              <span>&times;</span>
            </button>
            <div class="mobile-modal-card">
              <div class="mobile-modal-image">
                <img
                  :src="selectedCard?.goods_thumb"
                  :alt="selectedCard?.goods_title"
                />
                <div
                  v-if="selectedCard?.ban_type"
                  class="ban-indicator"
                  :class="`ban-type-${selectedCard.ban_type}`"
                >
                  {{ getBanTypeText(selectedCard.ban_type) }}
                </div>
              </div>
              <div class="mobile-modal-info">
                <h3 class="mobile-modal-title">
                  {{ selectedCard?.goods_title }}
                </h3>
                <div class="mobile-modal-effect">
                  {{ selectedCard?.effect || "無效果描述" }}
                </div>
                <button @click="goToCardDetail" class="mobile-modal-detail-btn">
                  查看詳情
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 卡片列表 -->
        <div class="cards-list-section">
          <div class="section-title">牌組列表</div>

          <!-- 主牌組列表 -->
          <div class="cards-list mb-3">
            <div class="list-header">
              <span class="list-header-title"
                >主牌組 ({{ mainDeckCount }})</span
              >
            </div>
            <div class="list-table">
              <div class="list-row header">
                <div class="list-col name">卡片名稱</div>
                <div class="list-col type">類型</div>
                <div class="list-col count">數量</div>
                <div class="list-col status">限制狀態</div>
              </div>
              <div
                v-for="(card, index) in mainDeckGrouped"
                :key="`list_main_${card.goods_id}`"
                class="list-row"
                @click="handleCardClick(card)"
                @mousemove="
                  updateHoverPosition(
                    $event,
                    card,
                    `list_main_${card.goods_id}`
                  )
                "
                @mouseleave="hideHover(`list_main_${card.goods_id}`)"
              >
                <div class="list-col name">{{ card.goods_title }}</div>
                <div class="list-col type">
                  {{ getCardTypeText(card.tag_id) }}
                </div>
                <div class="list-col count">{{ card.num }}</div>
                <div
                  class="list-col status"
                  :class="`ban-text-${card.ban_type || 0}`"
                >
                  {{ getBanTypeText(card.ban_type) }}
                </div>
                <teleport to="body">
                  <div
                    class="card-hover-details"
                    :style="
                      cardHoverStyles[`list_main_${card.goods_id}`] || {
                        display: 'none',
                      }
                    "
                  >
                    <div class="card-hover-title">
                      {{ card.goods_title || "未知卡名" }}
                    </div>
                    <div class="card-hover-effect">
                      {{ card.effect || "無效果描述" }}
                    </div>
                  </div>
                </teleport>
              </div>
            </div>
          </div>

          <!-- 額外牌組列表 -->
          <div
            v-if="showExtraDeck && extraDeckGrouped.length > 0"
            class="cards-list mb-3"
          >
            <div class="list-header">
              <span class="list-header-title"
                >額外牌組 ({{ extraDeckCount }})</span
              >
            </div>
            <div class="list-table">
              <div class="list-row header">
                <div class="list-col name">卡片名稱</div>
                <div class="list-col type">類型</div>
                <div class="list-col count">數量</div>
                <div class="list-col status">限制狀態</div>
              </div>
              <div
                v-for="(card, index) in extraDeckGrouped"
                :key="`list_extra_${card.goods_id}`"
                class="list-row"
                @click="handleCardClick(card)"
                @mousemove="
                  updateHoverPosition(
                    $event,
                    card,
                    `list_extra_${card.goods_id}`
                  )
                "
                @mouseleave="hideHover(`list_extra_${card.goods_id}`)"
              >
                <div class="list-col name">{{ card.goods_title }}</div>
                <div class="list-col type">
                  {{ getCardTypeText(card.tag_id) }}
                </div>
                <div class="list-col count">{{ card.num }}</div>
                <div
                  class="list-col status"
                  :class="`ban-text-${card.ban_type || 0}`"
                >
                  {{ getBanTypeText(card.ban_type) }}
                </div>
                <teleport to="body">
                  <div
                    class="card-hover-details"
                    :style="
                      cardHoverStyles[`list_extra_${card.goods_id}`] || {
                        display: 'none',
                      }
                    "
                  >
                    <div class="card-hover-title">
                      {{ card.goods_title || "未知卡名" }}
                    </div>
                    <div class="card-hover-effect">
                      {{ card.effect || "無效果描述" }}
                    </div>
                  </div>
                </teleport>
              </div>
            </div>
          </div>

          <!-- 備牌列表 -->
          <div
            v-if="showExtraDeck && sideDeckGrouped.length > 0"
            class="cards-list"
          >
            <div class="list-header">
              <span class="list-header-title">備牌 ({{ sideDeckCount }})</span>
            </div>
            <div class="list-table">
              <div class="list-row header">
                <div class="list-col name">卡片名稱</div>
                <div class="list-col type">類型</div>
                <div class="list-col count">數量</div>
                <div class="list-col status">限制狀態</div>
              </div>
              <div
                v-for="(card, index) in sideDeckGrouped"
                :key="`list_side_${card.goods_id}`"
                class="list-row"
                @click="handleCardClick(card)"
                @mousemove="
                  updateHoverPosition(
                    $event,
                    card,
                    `list_side_${card.goods_id}`
                  )
                "
                @mouseleave="hideHover(`list_side_${card.goods_id}`)"
              >
                <div class="list-col name">{{ card.goods_title }}</div>
                <div class="list-col type">
                  {{ getCardTypeText(card.tag_id) }}
                </div>
                <div class="list-col count">{{ card.num }}</div>
                <div
                  class="list-col status"
                  :class="`ban-text-${card.ban_type || 0}`"
                >
                  {{ getBanTypeText(card.ban_type) }}
                </div>
                <teleport to="body">
                  <div
                    class="card-hover-details"
                    :style="
                      cardHoverStyles[`list_side_${card.goods_id}`] || {
                        display: 'none',
                      }
                    "
                  >
                    <div class="card-hover-title">
                      {{ card.goods_title || "未知卡名" }}
                    </div>
                    <div class="card-hover-effect">
                      {{ card.effect || "無效果描述" }}
                    </div>
                  </div>
                </teleport>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import { useRoute, useRouter, useHead } from "#app";
import { ElMessage } from "element-plus";
import { useApi } from "~/composables/useApi";
import { useAuthStore } from "~/stores/auth";

// 初始化路由
const route = useRoute();
const router = useRouter();

// 獲取牌組ID
const groupId = computed(() => parseInt(route.params.groupId) || 0);

// 初始化API實例（在客戶端）
let api = null;
let authStore = null;

// 狀態管理
const isLoading = ref(true);
const deckData = ref({});
const currentTab = ref("main");
const cardHoverStyles = ref({});

// 手機版彈窗狀態
const showMobileModal = ref(false);
const selectedCard = ref(null);

// 檢測是否為手機設備
const isMobile = ref(false);

// 檢測設備類型
const checkDevice = () => {
  isMobile.value = window.innerWidth <= 768;
};

onMounted(() => {
  // 初始化API實例（在客戶端）
  api = useApi();
  authStore = useAuthStore();

  checkDevice();
  window.addEventListener("resize", checkDevice);
});

onUnmounted(() => {
  window.removeEventListener("resize", checkDevice);
});

// 是否已登入
const isLoggedIn = computed(() => authStore?.isAuthenticated || false);

// 顯示額外牌組和備牌選項（遊戲王特有）
const showExtraDeck = computed(() => {
  // 遊戲王(1)和萬智牌(9)需要顯示額外牌組
  return deckData.value.game_id === 1 || deckData.value.game_id === 9;
});

// 計算各區域卡片數量
const mainDeckCount = computed(() => {
  return (deckData.value.goods_info?.["1"] || [])
    .filter((card) => card.tag_id !== 21) // tag_id 21 為額外牌組卡片
    .reduce((sum, card) => sum + (parseInt(card.num) || 1), 0);
});

const extraDeckCount = computed(() => {
  return (deckData.value.goods_info?.["1"] || [])
    .filter((card) => card.tag_id === 21) // tag_id 21 為額外牌組卡片
    .reduce((sum, card) => sum + (parseInt(card.num) || 1), 0);
});

const sideDeckCount = computed(() => {
  return (deckData.value.goods_info?.["2"] || []).reduce(
    (sum, card) => sum + (parseInt(card.num) || 1),
    0
  );
});

// 根據當前標籤獲取對應牌組卡片
const currentDeckCards = computed(() => {
  let cards = [];

  if (!deckData.value.goods_info) return [];

  switch (currentTab.value) {
    case "main": // 主牌組
      cards = (deckData.value.goods_info["1"] || []).filter(
        (card) => card.tag_id !== 21
      );
      break;
    case "extra": // 額外牌組
      cards = (deckData.value.goods_info["1"] || []).filter(
        (card) => card.tag_id === 21
      );
      break;
    case "side": // 備牌
      cards = deckData.value.goods_info["2"] || [];
      break;
  }

  // 展開卡片為個別卡片實例（顯示正確數量）
  const expandedCards = [];
  for (const card of cards) {
    const numCopies = parseInt(card.num) || 1;
    for (let i = 0; i < numCopies; i++) {
      expandedCards.push({ ...card });
    }
  }

  return expandedCards;
});

// 卡片分組（用於列表顯示）
const mainDeckGrouped = computed(() => {
  if (!deckData.value.goods_info) return [];
  return (deckData.value.goods_info["1"] || [])
    .filter((card) => card.tag_id !== 21)
    .sort((a, b) => (a.tag_id || 0) - (b.tag_id || 0));
});

const extraDeckGrouped = computed(() => {
  if (!deckData.value.goods_info) return [];
  return (deckData.value.goods_info["1"] || [])
    .filter((card) => card.tag_id === 21)
    .sort((a, b) => (a.tag_id || 0) - (b.tag_id || 0));
});

const sideDeckGrouped = computed(() => {
  if (!deckData.value.goods_info) return [];
  return (deckData.value.goods_info["2"] || []).sort(
    (a, b) => (a.tag_id || 0) - (b.tag_id || 0)
  );
});

// 獲取牌組詳情數據
const fetchDeckDetails = async () => {
  if (!groupId.value) {
    ElMessage.error("無效的牌組ID");
    router.push("/Goodsgroup");
    return;
  }

  if (!api) {
    console.warn("API 實例尚未初始化");
    return;
  }

  isLoading.value = true;

  try {
    const response = await api.getCardGroupDetails(groupId.value);
    if (response && response.code === 200 && response.data) {
      deckData.value = response.data;

      // SEO標籤已經在服務端設置，無需在客戶端重複設置
    } else {
      ElMessage.error(response?.msg || "無法載入牌組詳情");
      router.push("/Goodsgroup");
    }
  } catch (error) {
    console.error("獲取牌組詳情失敗:", error);
    ElMessage.error("獲取牌組詳情失敗");
    router.push("/Goodsgroup");
  } finally {
    isLoading.value = false;
  }
};

// 懸停效果相關
const updateHoverPosition = (event, card, key) => {
  const element = event.currentTarget;
  const rect = element.getBoundingClientRect();
  const hoverWidth = 300;
  const hoverHeight = 200;
  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;

  let top = rect.bottom + 10;
  let left = rect.left;

  // 調整位置以確保懸停卡不超出視窗
  if (top + hoverHeight > viewportHeight) {
    top = rect.top - hoverHeight - 10;
  }

  if (left + hoverWidth > viewportWidth) {
    left = viewportWidth - hoverWidth - 10;
  }

  // 確保不會超出左側邊界
  if (left < 10) left = 10;

  // 創建或更新懸停詳情的樣式和內容
  cardHoverStyles.value[key] = {
    display: "block",
    position: "fixed",
    top: `${top}px`,
    left: `${left}px`,
    zIndex: 10000, // 提高z-index確保顯示在最上層
    pointerEvents: "none", // 防止懸停詳情阻擋其他交互
  };
};

const hideHover = (key) => {
  cardHoverStyles.value[key] = { display: "none" };
};

// 複製牌組
const copyDeck = () => {
  if (!isLoggedIn.value) {
    ElMessage.warning("請先登錄");
    return;
  }

  ElMessage.info("複製牌組功能即將上線");
};

// 處理卡片點擊
const handleCardClick = (card) => {
  if (isMobile.value) {
    // 手機版：顯示彈窗
    selectedCard.value = card;
    showMobileModal.value = true;
  } else {
    // 桌面版：直接跳轉
    showCardDetails(card.goods_id);
  }
};

// 顯示卡片詳情
const showCardDetails = (goodsId) => {
  router.push(`/card/${goodsId}`);
};

// 關閉手機版彈窗
const closeMobileModal = () => {
  showMobileModal.value = false;
  selectedCard.value = null;
};

// 跳轉到卡片詳情
const goToCardDetail = () => {
  if (selectedCard.value?.goods_id) {
    showCardDetails(selectedCard.value.goods_id);
    closeMobileModal();
  }
};

// 日期格式化
const formatDate = (dateString) => {
  if (!dateString) return "未知日期";

  let date;

  // 檢查是否為字符串格式的日期時間
  if (typeof dateString === "string" && dateString.includes("-")) {
    date = new Date(dateString);
  } else {
    // 處理Unix時間戳（數字）
    date = new Date(dateString * 1000);
  }

  if (isNaN(date.getTime())) {
    return "未知日期";
  }

  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
    2,
    "0"
  )}-${String(date.getDate()).padStart(2, "0")}`;
};

// 獲取禁限狀態文本
const getBanTypeText = (banType) => {
  switch (parseInt(banType)) {
    case 1:
      return "禁止";
    case 2:
      return "限制";
    case 3:
      return "準限制";
    default:
      return "";
  }
};

// 獲取卡片類型文本
const getCardTypeText = (tagId) => {
  // 簡易版卡片類型識別
  switch (parseInt(tagId)) {
    case 1:
      return "怪獸卡";
    case 2:
      return "魔法卡";
    case 3:
      return "陷阱卡";
    case 21:
      return "額外卡";
    default:
      return "其他";
  }
};

// 返回上一頁
const goBack = () => {
  router.back();
};

// 計算SEO相關數據
const seoTitle = computed(() => {
  if (deckData.value?.title) {
    return `${deckData.value.title} - 遊戲王資訊站`;
  }
  return "牌組詳情 - 遊戲王資訊站";
});

const seoDescription = computed(() => {
  if (deckData.value?.title && deckData.value?.desc) {
    const desc =
      deckData.value.desc.length > 120
        ? deckData.value.desc.slice(0, 120) + "..."
        : deckData.value.desc;
    return `${deckData.value.title} - ${desc}。包含主牌組(${mainDeckCount.value}張)、額外牌組(${extraDeckCount.value}張)、備牌(${sideDeckCount.value}張)的完整牌組列表。`;
  }
  return "遊戲王牌組詳情頁面，提供完整的牌組構築資訊，包含主牌組、額外牌組和備牌的詳細列表。";
});

const seoKeywords = computed(() => {
  const keywords = ["遊戲王", "牌組", "構築", "主牌組", "額外牌組", "備牌"];
  if (deckData.value?.title) {
    keywords.unshift(deckData.value.title);
  }
  if (deckData.value?.desc) {
    // 從描述中提取關鍵詞
    const descKeywords = deckData.value.desc
      .replace(/[^\u4e00-\u9fa5]/g, " ")
      .split(" ")
      .filter((word) => word.length > 1)
      .slice(0, 5);
    keywords.push(...descKeywords);
  }
  return keywords.join(", ");
});

const ogImage = computed(() => {
  return deckData.value?.image || "/images/og-image.jpg";
});

// 獲取運行時配置（在 setup 頂層）
const config = useRuntimeConfig();

const canonicalUrl = computed(() => {
  return `${config.public.siteUrl}/Goodsgroup/${groupId.value}`;
});

// 設置SEO標籤
useHead({
  title: seoTitle,
  meta: [
    { name: "description", content: seoDescription },
    { name: "keywords", content: seoKeywords },
    {
      name: "robots",
      content:
        "index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1",
    },
    { name: "googlebot", content: "index, follow" },
    { name: "bingbot", content: "index, follow" },
    // Open Graph
    { property: "og:title", content: seoTitle },
    { property: "og:description", content: seoDescription },
    { property: "og:type", content: "website" },
    { property: "og:url", content: canonicalUrl },
    { property: "og:image", content: ogImage },
    { property: "og:image:width", content: "1200" },
    { property: "og:image:height", content: "630" },
    {
      property: "og:image:alt",
      content: deckData.value?.title || "遊戲王牌組",
    },
    { property: "og:site_name", content: "遊戲王資訊站" },
    { property: "og:locale", content: "zh_TW" },
    // Twitter Card
    { name: "twitter:card", content: "summary_large_image" },
    { name: "twitter:title", content: seoTitle },
    { name: "twitter:description", content: seoDescription },
    { name: "twitter:image", content: ogImage },
    // 額外SEO優化
    { name: "author", content: "遊戲王資訊站" },
    { name: "application-name", content: "遊戲王資訊站" },
    { name: "format-detection", content: "telephone=no" },
  ],
  link: [
    { rel: "canonical", href: canonicalUrl },
    { rel: "preload", as: "image", href: ogImage },
  ],
  script: [
    // 結構化數據 - 牌組詳情
    {
      type: "application/ld+json",
      innerHTML: computed(() =>
        JSON.stringify({
          "@context": "https://schema.org",
          "@type": "CreativeWork",
          name: deckData.value?.title || "遊戲王牌組",
          description: deckData.value?.desc || "遊戲王牌組詳情",
          image: deckData.value?.image || "",
          url: canonicalUrl.value,
          author: {
            "@type": "Person",
            name: deckData.value?.user_info?.nickname || "未知用戶",
          },
          dateCreated: deckData.value?.create_time
            ? new Date(deckData.value.create_time * 1000).toISOString()
            : undefined,
          genre: "遊戲王牌組",
          keywords: seoKeywords.value,
          numberOfItems:
            mainDeckCount.value + extraDeckCount.value + sideDeckCount.value,
        })
      ),
    },
    // 結構化數據 - 麵包屑導航
    {
      type: "application/ld+json",
      innerHTML: JSON.stringify({
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        itemListElement: [
          {
            "@type": "ListItem",
            position: 1,
            name: "首頁",
            item: config.public.siteUrl,
          },
          {
            "@type": "ListItem",
            position: 2,
            name: "牌組列表",
            item: `${config.public.siteUrl}/Goodsgroup`,
          },
          {
            "@type": "ListItem",
            position: 3,
            name: "牌組詳情",
            item: canonicalUrl.value,
          },
        ],
      }),
    },
  ],
});

// 服務端渲染數據預取
const { data: deckDataFromServer } = await useFetch("/api/Goodsgroup/info", {
  method: "POST",
  body: { group_id: groupId.value },
  default: () => ({ code: 200, data: {} }),
});

// 如果服務端有數據，使用服務端數據
if (deckDataFromServer.value?.code === 200 && deckDataFromServer.value?.data) {
  deckData.value = deckDataFromServer.value.data;
  isLoading.value = false;
}

// 頁面載入時獲取數據（如果服務端沒有數據）
onMounted(() => {
  // SSR 數據已經在服務端獲取，這裡只需要客戶端特定的初始化
  console.log(
    "牌組詳情頁面已載入，牌組ID:",
    groupId.value,
    "標題:",
    deckData.value.title
  );

  // 如果服務端沒有完整數據，則進行客戶端獲取
  if (!deckData.value.title && api) {
    fetchDeckDetails();
  }
});
</script>

<style scoped>
/* 頁面整體佈局 */
.deck-details-page {
  background-color: #091020;
  background-image: radial-gradient(
      circle at 20% 35%,
      rgba(30, 144, 255, 0.1) 0%,
      transparent 40%
    ),
    radial-gradient(
      circle at 80% 10%,
      rgba(90, 30, 160, 0.08) 0%,
      transparent 40%
    );
  min-height: 100vh;
  color: #fff;
  font-family: "Noto Sans TC", sans-serif;
}

/* 頁面頭部 - 緊湊設計 */
.page-header {
  position: relative;
  padding: 8px 0;
  background: linear-gradient(
    180deg,
    rgba(24, 28, 40, 0.9) 0%,
    rgba(9, 16, 32, 0.8) 100%
  );
  border-bottom: 1px solid rgba(30, 144, 255, 0.2);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(30, 144, 255, 0.1);
  border: 1px solid rgba(30, 144, 255, 0.2);
  border-radius: 20px;
  padding: 5px 12px;
  color: #fff;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 14px;
}

.back-button:hover {
  background: rgba(30, 144, 255, 0.2);
  transform: translateY(-2px);
}

.page-title {
  font-size: 24px;
  font-weight: 800;
  letter-spacing: 1px;
  margin: 0;
  text-shadow: 0 0 10px rgba(30, 144, 255, 0.5);
  background: linear-gradient(to right, #fff, #1e90ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  flex: 1;
  text-align: center;
}

/* 載入和空狀態 */
.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  min-height: 300px;
  background: rgba(14, 22, 40, 0.7);
  border-radius: 16px;
  border: 1px solid rgba(30, 144, 255, 0.15);
}

.empty-state i {
  margin-bottom: 16px;
}

.empty-state p {
  color: rgba(255, 255, 255, 0.6);
  font-size: 16px;
}

/* 牌組概覽 */
.deck-overview {
  display: flex;
  background: rgba(14, 22, 40, 0.7);
  border-radius: 16px;
  padding: 15px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(30, 144, 255, 0.15);
  gap: 15px;
}

.deck-image {
  width: 120px;
  height: 120px;
  border-radius: 10px;
  overflow: hidden;
  border: 2px solid rgba(30, 144, 255, 0.3);
  flex-shrink: 0;
}

.deck-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.deck-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.deck-creator {
  display: flex;
  align-items: center;
  gap: 10px;
}

.creator-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid rgba(30, 144, 255, 0.3);
}

.creator-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.creator-info {
  display: flex;
  flex-direction: column;
}

.creator-name {
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
}

.creator-time {
  color: rgba(255, 255, 255, 0.5);
  font-size: 12px;
}

.deck-description {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  line-height: 1.5;
  background: rgba(0, 0, 0, 0.2);
  padding: 10px;
  border-radius: 8px;
  border: 1px solid rgba(30, 144, 255, 0.1);
}

.deck-actions {
  display: flex;
  gap: 10px;
  margin-top: auto;
}

/* 牌組分頁 */
.deck-tabs {
  display: flex;
  background: rgba(14, 22, 40, 0.7);
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid rgba(30, 144, 255, 0.15);
}

.deck-tab {
  flex: 1;
  padding: 10px;
  text-align: center;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
  border-right: 1px solid rgba(30, 144, 255, 0.15);
}

.deck-tab:last-child {
  border-right: none;
}

.deck-tab.active {
  background: rgba(30, 144, 255, 0.2);
  color: rgba(255, 255, 255, 0.9);
}

/* 卡片網格 */
.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 10px;
  background: rgba(14, 22, 40, 0.7);
  border-radius: 10px;
  padding: 15px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(30, 144, 255, 0.15);
}

.card-item-container {
  position: relative;
}

.card-item {
  position: relative;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid rgba(30, 144, 255, 0.1);
  height: 100%;
  width: 100%;
}

.card-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  border-color: rgba(30, 144, 255, 0.3);
}

.card-image {
  height: 140px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  position: relative;
  z-index: 1;
}

.card-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  z-index: 1;
}

.ban-indicator {
  position: absolute;
  top: 5px;
  right: 5px;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: bold;
}

.ban-type-1 {
  background: rgba(255, 0, 0, 0.7);
  color: white;
}

.ban-type-2 {
  background: rgba(255, 165, 0, 0.7);
  color: white;
}

.ban-type-3 {
  background: rgba(255, 255, 0, 0.7);
  color: black;
}

/* 卡片懸停詳情 */
.card-hover-details {
  width: 300px;
  position: fixed;
  background-color: rgba(10, 20, 40, 0.95);
  color: white;
  border: 1px solid rgba(30, 144, 255, 0.4);
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  z-index: 10000 !important;
  pointer-events: none;
  max-height: 200px;
  overflow-y: auto;
}

.card-hover-title {
  font-size: 16px;
  font-weight: bold;
  color: #1e90ff;
  margin-bottom: 10px;
  border-bottom: 1px solid rgba(30, 144, 255, 0.3);
  padding-bottom: 5px;
}

.card-hover-effect {
  font-size: 14px;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.8);
  max-height: 150px;
  overflow-y: auto;
}

/* 卡片列表區 */
.cards-list-section {
  margin-top: 20px;
}

.section-title {
  font-size: 20px;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 15px;
  position: relative;
  display: inline-block;
}

.section-title:after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 40px;
  height: 2px;
  background: rgba(30, 144, 255, 0.5);
}

.cards-list {
  background: rgba(14, 22, 40, 0.7);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(30, 144, 255, 0.15);
}

.list-header {
  background: rgba(20, 30, 60, 0.7);
  padding: 10px 15px;
  border-bottom: 1px solid rgba(30, 144, 255, 0.2);
}

.list-header-title {
  font-size: 16px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.list-table {
  width: 100%;
}

.list-row {
  display: flex;
  padding: 8px 15px;
  border-bottom: 1px solid rgba(30, 144, 255, 0.1);
  align-items: center;
  position: relative;
}

.list-row:last-child {
  border-bottom: none;
}

.list-row.header {
  background: rgba(0, 0, 0, 0.2);
  color: rgba(255, 255, 255, 0.6);
  font-weight: 600;
  font-size: 14px;
}

.list-row:not(.header) {
  cursor: pointer;
  transition: background 0.2s ease;
}

.list-row:not(.header):hover {
  background: rgba(30, 144, 255, 0.1);
}

.list-col {
  padding: 0 5px;
}

.list-col.name {
  flex: 3;
  font-weight: 500;
}

.list-col.type {
  flex: 2;
  color: rgba(255, 255, 255, 0.7);
}

.list-col.count {
  flex: 1;
  text-align: center;
}

.list-col.status {
  flex: 2;
  text-align: center;
}

.ban-text-1 {
  color: #ff6b6b;
}

.ban-text-2 {
  color: #ffa502;
}

.ban-text-3 {
  color: #ffdd59;
}

/* 響應式調整 */
@media (max-width: 768px) {
  .deck-overview {
    flex-direction: column;
  }

  .deck-image {
    width: 100%;
    height: auto;
    aspect-ratio: 16/9;
  }

  .cards-grid {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  }

  .card-image {
    height: 100px;
  }

  .list-col.type {
    display: none;
  }
}

@media (max-width: 480px) {
  .cards-grid {
    grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
    gap: 5px;
    padding: 10px;
  }

  .card-image {
    height: 90px;
  }

  .list-col.name {
    flex: 2;
  }

  .ban-indicator {
    font-size: 8px;
    padding: 1px 4px;
  }
}

/* 手機版彈窗樣式 */
.mobile-card-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.mobile-modal-content {
  position: relative;
  background: linear-gradient(
    145deg,
    rgba(20, 30, 48, 0.95),
    rgba(30, 40, 60, 0.95)
  );
  border-radius: 16px;
  width: 100%;
  max-width: 400px;
  max-height: 80vh;
  overflow-y: auto;
  border: 1px solid rgba(30, 144, 255, 0.3);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.mobile-modal-close {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: rgba(255, 255, 255, 0.8);
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
}

.mobile-modal-close:hover {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
}

.mobile-modal-close span {
  font-size: 20px;
  line-height: 1;
}

.mobile-modal-card {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.mobile-modal-image {
  position: relative;
  align-self: center;
  width: 150px;
  height: 220px;
  border-radius: 8px;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(30, 144, 255, 0.3);
}

.mobile-modal-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.mobile-modal-info {
  text-align: center;
}

.mobile-modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 12px;
  line-height: 1.3;
}

.mobile-modal-effect {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
  margin-bottom: 20px;
  max-height: 150px;
  overflow-y: auto;
  padding: 10px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  border: 1px solid rgba(30, 144, 255, 0.2);
}

.mobile-modal-detail-btn {
  background: linear-gradient(135deg, #1e90ff, #00bfff);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(30, 144, 255, 0.3);
}

.mobile-modal-detail-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(30, 144, 255, 0.4);
}

/* 隱藏桌面版懸停效果在手機上 */
@media (max-width: 768px) {
  .desktop-hover {
    display: none !important;
  }

  /* 進一步縮小手機版整體尺寸 */
  .container {
    padding: 0 8px;
  }

  .page-header {
    padding: 8px 0;
  }

  .page-title {
    font-size: 18px;
  }

  .deck-overview {
    padding: 12px;
    margin-bottom: 12px;
    display: flex !important;
    flex-direction: row !important;
    align-items: flex-start !important;
    gap: 12px !important;
  }

  .deck-image {
    width: 80px;
    height: 80px;
    order: 2;
    flex-shrink: 0;
  }

  .deck-info {
    order: 1;
    flex: 1;
    min-width: 0;
  }

  .deck-creator {
    margin-bottom: 8px !important;
  }

  .creator-avatar {
    width: 30px !important;
    height: 30px !important;
    margin-right: 8px !important;
  }

  .creator-name {
    font-size: 14px;
  }

  .creator-time {
    font-size: 11px;
  }

  .deck-description {
    font-size: 13px;
    margin: 8px 0;
    line-height: 1.4;
  }

  .deck-actions {
    margin-top: 8px;
  }

  .deck-actions .el-button {
    font-size: 12px !important;
    padding: 6px 12px !important;
  }

  .deck-tabs {
    gap: 8px;
    margin-bottom: 12px;
  }

  .deck-tab {
    padding: 8px 12px;
    font-size: 12px;
  }

  .cards-grid {
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    gap: 6px;
    padding: 8px;
    margin-bottom: 12px;
  }

  .card-image {
    height: 85px;
  }

  .section-title {
    font-size: 16px;
    margin-bottom: 10px;
  }

  .cards-list {
    margin-bottom: 12px;
  }

  .list-header {
    padding: 8px 12px;
  }

  .list-header-title {
    font-size: 14px;
  }

  .list-row {
    padding: 8px 12px;
    font-size: 13px;
  }

  .list-row.header {
    font-size: 12px;
  }
}
</style>
