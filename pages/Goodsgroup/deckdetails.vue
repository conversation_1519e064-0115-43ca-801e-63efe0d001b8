<template>
  <div class="deck-details-page">
    <!-- 頂部導航與標題區 -->
    <div class="page-header">
      <div class="container mx-auto px-2">
        <div class="header-content">
          <button @click="goBack" class="back-button">
            <i class="el-icon-arrow-left"></i>
            <span>返回</span>
          </button>
          <h1 class="page-title">上位構築詳情</h1>
        </div>
      </div>
    </div>

    <!-- 主要內容區 -->
    <div class="container mx-auto py-2 px-2">
      <!-- 載入狀態 -->
      <div v-if="isLoading" class="loading-state">
        <el-skeleton :rows="5" animated />
      </div>

      <!-- 實際內容 -->
      <div v-else>
        <el-tabs type="card" class="custom-tabs">
          <el-tab-pane label="怪獸卡">
            <!-- 主備牌切換按鈕 -->
            <div class="deck-type-controls">
              <el-button-group>
                <el-button
                  :type="currentDeckType === '主牌組' ? 'primary' : 'default'"
                  @click="currentDeckType = '主牌組'"
                  >主牌組</el-button
                >
                <el-button
                  :type="currentDeckType === '備牌' ? 'primary' : 'default'"
                  @click="currentDeckType = '備牌'"
                  >備牌</el-button
                >
              </el-button-group>
            </div>

            <div class="cards-container">
              <div class="cards-header">
                <div class="card-col-image">卡圖</div>
                <div class="card-col-name">卡名</div>
                <div v-for="i in 4" :key="i" class="card-col-count">
                  {{ i - 1 }}張
                </div>
              </div>
              <card-list-item
                v-for="card in monsterCards"
                :key="card.goodsId"
                :card="card"
                @click="goToCardDetails(card.goodsId)"
                @mouseover="showCardPreview(card, $event)"
                @mouseleave="hideCardPreview"
              />
              <div v-if="monsterCards.length === 0" class="empty-state">
                <i class="el-icon-warning-outline"></i>
                <p>暫無怪獸卡</p>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="魔法卡">
            <!-- 主備牌切換按鈕 -->
            <div class="deck-type-controls">
              <el-button-group>
                <el-button
                  :type="currentDeckType === '主牌組' ? 'primary' : 'default'"
                  @click="currentDeckType = '主牌組'"
                  >主牌組</el-button
                >
                <el-button
                  :type="currentDeckType === '備牌' ? 'primary' : 'default'"
                  @click="currentDeckType = '備牌'"
                  >備牌</el-button
                >
              </el-button-group>
            </div>

            <div class="cards-container">
              <div class="cards-header">
                <div class="card-col-image">卡圖</div>
                <div class="card-col-name">卡名</div>
                <div v-for="i in 4" :key="i" class="card-col-count">
                  {{ i - 1 }}張
                </div>
              </div>
              <card-list-item
                v-for="card in magicCards"
                :key="card.goodsId"
                :card="card"
                @click="goToCardDetails(card.goodsId)"
                @mouseover="showCardPreview(card, $event)"
                @mouseleave="hideCardPreview"
              />
              <div v-if="magicCards.length === 0" class="empty-state">
                <i class="el-icon-warning-outline"></i>
                <p>暫無魔法卡</p>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="陷阱卡">
            <!-- 主備牌切換按鈕 -->
            <div class="deck-type-controls">
              <el-button-group>
                <el-button
                  :type="currentDeckType === '主牌組' ? 'primary' : 'default'"
                  @click="currentDeckType = '主牌組'"
                  >主牌組</el-button
                >
                <el-button
                  :type="currentDeckType === '備牌' ? 'primary' : 'default'"
                  @click="currentDeckType = '備牌'"
                  >備牌</el-button
                >
              </el-button-group>
            </div>

            <div class="cards-container">
              <div class="cards-header">
                <div class="card-col-image">卡圖</div>
                <div class="card-col-name">卡名</div>
                <div v-for="i in 4" :key="i" class="card-col-count">
                  {{ i - 1 }}張
                </div>
              </div>
              <card-list-item
                v-for="card in trapCards"
                :key="card.goodsId"
                :card="card"
                @click="goToCardDetails(card.goodsId)"
                @mouseover="showCardPreview(card, $event)"
                @mouseleave="hideCardPreview"
              />
              <div v-if="trapCards.length === 0" class="empty-state">
                <i class="el-icon-warning-outline"></i>
                <p>暫無陷阱卡</p>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="額外牌組">
            <!-- 主備牌切換按鈕 -->
            <div class="deck-type-controls">
              <el-button-group>
                <el-button
                  :type="currentDeckType === '主牌組' ? 'primary' : 'default'"
                  @click="currentDeckType = '主牌組'"
                  >主牌組</el-button
                >
                <el-button
                  :type="currentDeckType === '備牌' ? 'primary' : 'default'"
                  @click="currentDeckType = '備牌'"
                  >備牌</el-button
                >
              </el-button-group>
            </div>

            <div class="cards-container">
              <div class="cards-header">
                <div class="card-col-image">卡圖</div>
                <div class="card-col-name">卡名</div>
                <div v-for="i in 4" :key="i" class="card-col-count">
                  {{ i - 1 }}張
                </div>
              </div>
              <card-list-item
                v-for="card in extraDeck"
                :key="card.goodsId"
                :card="card"
                @click="goToCardDetails(card.goodsId)"
                @mouseover="showCardPreview(card, $event)"
                @mouseleave="hideCardPreview"
              />
              <div v-if="extraDeck.length === 0" class="empty-state">
                <i class="el-icon-warning-outline"></i>
                <p>暫無額外牌組卡</p>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 卡片預覽懸浮窗 -->
    <teleport to="body">
      <div
        v-if="previewCard"
        class="card-preview"
        :style="previewStyle"
        @mouseenter="clearHideTimeout"
        @mouseleave="hideCardPreview"
      >
        <div class="preview-content">
          <div class="preview-image">
            <img :src="previewCard.imageUrl" alt="卡片圖片" />
            <div
              v-if="previewCard.banType > 0"
              class="ban-indicator"
              :class="getBanTypeClass(previewCard.banType)"
            >
              {{ previewCard.banType }}
            </div>
          </div>
          <div class="preview-details">
            <h3 class="preview-title">{{ previewCard.name }}</h3>
            <p class="preview-description">{{ previewCard.description }}</p>
          </div>
        </div>
      </div>
    </teleport>

    <!-- 核心卡片彈窗 -->
    <el-dialog
      v-model="showCoreCardsDialog"
      title="核心單卡"
      width="80%"
      :destroy-on-close="true"
      custom-class="core-cards-dialog"
      center
    >
      <div class="core-cards-grid">
        <div
          v-for="card in coreCards"
          :key="card.goodsId"
          class="core-card-item"
          @mouseover="showCardPreview(card, $event)"
          @mouseleave="hideCardPreview"
          @click="goToCardDetails(card.goodsId)"
        >
          <img :src="card.imageUrl" alt="卡片圖片" class="core-card-image" />
          <div class="core-card-stats">
            <span class="core-card-percentage">100%</span>
            <span class="core-card-count">{{ card.cardCount }}張</span>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage, ElDialog } from "element-plus";
import { useApi } from "~/composables/useApi";
import CardListItem from "~/components/card/CardListItem.vue";

const route = useRoute();
const router = useRouter();
const api = useApi();

// 頁面參數
const title = ref(route.query.title || "");
const envId = ref(parseInt(route.query.env_id) || 0);
const gameId = ref(parseInt(route.query.game_id) || 1); // 默認為遊戲王

// 使用 SSR 數據獲取主牌組詳情
const { data: mainDeckData } = await useFetch("/api/Goodsgroup/deckdetail", {
  method: "POST",
  body: {
    game_id: gameId.value,
    title: title.value,
    env_id: envId.value,
    type_id: 1, // 主牌組
  },
  default: () => ({ code: 500, data: {} }),
  server: false,
});

// 狀態管理
const isLoading = ref(false);
const currentDeckType = ref("主牌組");
const showCoreCardsDialog = ref(false);

// 使用 computed 處理主牌組數據
const monsterCards = computed(() => {
  const data = mainDeckData.value;
  if (data?.code === 200 && data?.data?.main_deck?.monster) {
    return processData(data.data.main_deck.monster);
  }
  return [];
});

const magicCards = computed(() => {
  const data = mainDeckData.value;
  if (data?.code === 200 && data?.data?.main_deck?.spell) {
    return processData(data.data.main_deck.spell);
  }
  return [];
});

const trapCards = computed(() => {
  const data = mainDeckData.value;
  if (data?.code === 200 && data?.data?.main_deck?.trap) {
    return processData(data.data.main_deck.trap);
  }
  return [];
});

const extraDeck = computed(() => {
  const data = mainDeckData.value;
  if (data?.code === 200 && data?.data?.main_deck?.extra) {
    return processData(data.data.main_deck.extra);
  }
  return [];
});

const coreCards = computed(() => getCoreCards());

// 卡片預覽相關
const previewCard = ref(null);
const previewStyle = ref({
  top: "0px",
  left: "0px",
});
let hideTimeout = null;

// 返回上一頁
const goBack = () => {
  router.back();
};

// 切換牌組類型
watch(currentDeckType, (newValue) => {
  const typeId = newValue === "主牌組" ? 1 : 2;
  fetchDeckDetails(typeId);
});

// 獲取牌組詳情數據
const fetchDeckDetails = async (typeId) => {
  isLoading.value = true;
  clearCardData(); // 清空舊數據

  try {
    const params = {
      game_id: gameId.value,
      title: title.value,
      env_id: envId.value,
      type_id: typeId,
    };

    const response = await api.getdeckdetail(params);

    if (response && response.code === 200 && response.data) {
      // 根據typeId決定使用哪個數據集
      if (typeId === 1) {
        // 主牌組
        if (response.data.main_deck) {
          monsterCards.value = processData(
            response.data.main_deck.monster || []
          );

          magicCards.value = processData(response.data.main_deck.spell || []);

          trapCards.value = processData(response.data.main_deck.trap || []);
        }

        // 處理額外牌組 (可能在main_deck.extra或data.extra)
        const extraCards =
          response.data.main_deck?.extra || response.data.extra || [];
        extraDeck.value = processData(extraCards);
      } else {
        // 備牌 (typeId === 2)
        if (response.data.side_deck) {
          monsterCards.value = processData(
            response.data.side_deck.monster || []
          );

          magicCards.value = processData(response.data.side_deck.spell || []);

          trapCards.value = processData(response.data.side_deck.trap || []);
        }

        // 處理備牌的額外牌組
        const extraCards = response.data.side_deck?.extra || [];
        extraDeck.value = processData(extraCards);
      }

      // 如果響應中有獨立的extra數據，優先使用它
      if (
        response.data.extra &&
        Array.isArray(response.data.extra) &&
        typeId === 1
      ) {
        extraDeck.value = processData(response.data.extra);
      }
    } else {
      ElMessage.warning(response?.msg || "無法獲取牌組詳情");
    }
  } catch (error) {
    ElMessage.error("獲取牌組詳情失敗");
  } finally {
    isLoading.value = false;
  }
};

// 處理卡片數據
const processData = (cardList) => {
  if (!cardList || !Array.isArray(cardList) || cardList.length === 0) {
    return [];
  }

  const uniqueCards = {};

  try {
    for (const item of cardList) {
      const goodsId = item.goods_id;
      if (!goodsId) {
        continue;
      }

      if (!uniqueCards[goodsId]) {
        uniqueCards[goodsId] = {
          goodsId: goodsId,
          name: item.goods_title
            ? item.goods_title.replace(/"/g, "").replace(/'/g, "")
            : "",
          imageUrl: item.goods_thumb || "",
          description: item.effect || "",
          percentages: Array.isArray(item.percentages)
            ? item.percentages.map((p) =>
                typeof p === "number" ? p : parseFloat(p)
              )
            : [],
          banType: item.ban_type || 0,
          cardCount: Array.isArray(item.percentages)
            ? item.percentages.indexOf(100) !== -1
              ? item.percentages.indexOf(100) + 1
              : 1
            : 1,
        };
      }
    }
  } catch (err) {}

  const result = Object.values(uniqueCards);
  return result;
};

// 清除卡片數據
const clearCardData = () => {
  monsterCards.value = [];
  magicCards.value = [];
  trapCards.value = [];
  extraDeck.value = [];
};

// 獲取核心卡片
const getCoreCards = () => {
  const cards = [];

  const addCoreCardsFromList = (cardList) => {
    for (const card of cardList) {
      if (card.percentages.includes(100)) {
        cards.push(card);
      }
    }
  };

  addCoreCardsFromList(monsterCards.value);
  addCoreCardsFromList(magicCards.value);
  addCoreCardsFromList(trapCards.value);
  addCoreCardsFromList(extraDeck.value);

  return cards;
};

// 顯示卡片預覽
const showCardPreview = (card, event) => {
  clearTimeout(hideTimeout);
  previewCard.value = card;

  nextTick(() => {
    const element = event.currentTarget;
    const rect = element.getBoundingClientRect();
    const previewElement = document.querySelector(".card-preview");

    if (!previewElement) return;

    const previewHeight = previewElement.offsetHeight;
    const previewWidth = previewElement.offsetWidth;

    // 計算位置，優先顯示在元素的右側
    let left = rect.right + 10;
    let top = rect.top;

    // 檢查是否超出可視區域右側
    if (left + previewWidth > window.innerWidth) {
      left = rect.left - previewWidth - 10;
    }

    // 檢查是否超出可視區域底部
    if (top + previewHeight > window.innerHeight) {
      top = window.innerHeight - previewHeight - 10;
    }

    // 避免頂部溢出
    if (top < 0) {
      top = 10;
    }

    previewStyle.value = {
      top: `${top}px`,
      left: `${left}px`,
    };
  });
};

// 清除隱藏計時器
const clearHideTimeout = () => {
  clearTimeout(hideTimeout);
};

// 隱藏卡片預覽
const hideCardPreview = () => {
  hideTimeout = setTimeout(() => {
    previewCard.value = null;
  }, 100);
};

// 獲取禁限制狀態類別
const getBanTypeClass = (banType) => {
  switch (banType) {
    case 1:
      return "banned";
    case 2:
      return "limited-1";
    case 3:
      return "limited-2";
    default:
      return "";
  }
};

// 獲取禁限制狀態文字
const getBanTypeText = (banType) => {
  switch (banType) {
    case 1:
      return "禁止卡";
    case 2:
      return "限制卡 (限1張)";
    case 3:
      return "限制卡 (限2張)";
    default:
      return "";
  }
};

// 跳轉到卡片詳情頁面
const goToCardDetails = (cardId) => {
  router.push(`/card/${cardId}`);
};

// 客戶端初始化
onMounted(async () => {
  // SSR 數據已經在服務端獲取，這裡只需要客戶端特定的初始化
  console.log(
    "牌組詳情頁面已載入，怪獸卡數量:",
    monsterCards.value.length,
    "魔法卡數量:",
    magicCards.value.length,
    "陷阱卡數量:",
    trapCards.value.length,
    "額外牌組數量:",
    extraDeck.value.length
  );

  // 如果 SSR 數據不完整，則進行客戶端獲取
  if (
    monsterCards.value.length === 0 &&
    magicCards.value.length === 0 &&
    trapCards.value.length === 0
  ) {
    await fetchDeckDetails(1);
  }
});
</script>

<style scoped>
/* 頁面整體佈局 */
.deck-details-page {
  background-color: #091020;
  background-image: radial-gradient(
      circle at 20% 35%,
      rgba(30, 144, 255, 0.1) 0%,
      transparent 40%
    ),
    radial-gradient(
      circle at 80% 10%,
      rgba(90, 30, 160, 0.08) 0%,
      transparent 40%
    );
  min-height: 100vh;
  color: #fff;
  font-family: "Noto Sans TC", sans-serif;
}

/* 頁面頭部 */
.page-header {
  position: relative;
  padding: 8px 0;
  background: linear-gradient(
    180deg,
    rgba(24, 28, 40, 0.9) 0%,
    rgba(9, 16, 32, 0.8) 100%
  );
  border-bottom: 1px solid rgba(30, 144, 255, 0.2);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(30, 144, 255, 0.1);
  border: 1px solid rgba(30, 144, 255, 0.2);
  border-radius: 20px;
  padding: 5px 12px;
  color: #fff;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 14px;
}

.back-button:hover {
  background: rgba(30, 144, 255, 0.2);
  transform: translateY(-2px);
}

.page-title {
  font-size: 24px;
  font-weight: 800;
  letter-spacing: 1px;
  margin: 0;
  text-shadow: 0 0 10px rgba(30, 144, 255, 0.5);
  background: linear-gradient(to right, #fff, #1e90ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  flex: 1;
  text-align: center;
}

.deck-type-controls {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 15px;
  margin-top: 10px;
}

.deck-type-controls .el-button {
  font-size: 16px;
  padding: 8px 20px;
}

/* 載入狀態 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  min-height: 300px;
  background: rgba(14, 22, 40, 0.7);
  border-radius: 16px;
  border: 1px solid rgba(30, 144, 255, 0.15);
}

/* 空狀態 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  text-align: center;
  color: rgba(255, 255, 255, 0.5);
}

.empty-state i {
  font-size: 48px;
  color: rgba(30, 144, 255, 0.3);
  margin-bottom: 12px;
}

.empty-state p {
  font-size: 16px;
}

/* 自訂標籤頁樣式 */
:deep(.custom-tabs .el-tabs__header) {
  margin-bottom: 15px;
  background: linear-gradient(
    90deg,
    rgba(30, 144, 255, 0.2),
    rgba(30, 144, 255, 0.05)
  );
  border-radius: 8px;
}

:deep(.custom-tabs .el-tabs__nav) {
  border: none;
}

:deep(.custom-tabs .el-tabs__item) {
  color: rgba(255, 255, 255, 0.7);
  font-size: 16px;
  border: none;
  transition: all 0.3s ease;
}

:deep(.custom-tabs .el-tabs__item.is-active) {
  color: #1e90ff;
  background: rgba(30, 144, 255, 0.1);
  border-radius: 8px;
}

:deep(.custom-tabs .el-tabs__active-bar) {
  background-color: #1e90ff;
  height: 3px;
}

/* 卡片列表 */
.cards-container {
  background: rgba(14, 22, 40, 0.7);
  border-radius: 16px;
  padding: 20px;
  border: 1px solid rgba(30, 144, 255, 0.15);
}

.cards-header {
  display: flex;
  align-items: center;
  padding: 10px;
  background: rgba(14, 22, 40, 0.7);
  border-radius: 10px 10px 0 0;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  border-bottom: 1px solid rgba(30, 144, 255, 0.3);
}

.card-col-image {
  width: 60px;
  text-align: center;
}

.card-col-name {
  flex: 1;
  margin-left: 8px;
}

.card-col-count {
  width: 60px;
  text-align: center;
}

/* 卡片預覽懸浮窗 */
.card-preview {
  position: fixed;
  z-index: 10000;
  pointer-events: auto; /* 允許互動 */
  max-width: 320px;
  background: rgba(0, 10, 20, 0.95);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(30, 144, 255, 0.3);
  overflow: hidden;
  transition: opacity 0.2s;
}

.preview-content {
  display: flex;
  flex-direction: column;
  padding: 10px;
}

.preview-image {
  position: relative;
  display: flex;
  justify-content: center;
  padding: 10px 0;
}

.preview-image img {
  height: 200px;
  object-fit: contain;
}

.ban-indicator {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: white;
  border: 2px solid red;
  color: red;
  font-size: 12px;
  font-weight: bold;
  display: flex;
  justify-content: center;
  align-items: center;
  line-height: 1;
}

.preview-details {
  padding: 10px;
}

.preview-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #1e90ff;
  text-align: center;
}

.preview-description {
  font-size: 14px;
  line-height: 1.4;
  color: rgba(255, 255, 255, 0.9);
  max-height: 150px;
  overflow-y: auto;
}

/* 核心卡片對話框 */
:deep(.core-cards-dialog) {
  background: rgba(14, 22, 40, 0.95);
  border: 1px solid rgba(30, 144, 255, 0.3);
  border-radius: 16px;
  color: white;
}

:deep(.core-cards-dialog .el-dialog__header) {
  border-bottom: 1px solid rgba(30, 144, 255, 0.2);
  padding: 15px 20px;
}

:deep(.core-cards-dialog .el-dialog__title) {
  color: #1e90ff;
  font-weight: bold;
  font-size: 20px;
}

:deep(.core-cards-dialog .el-dialog__headerbtn .el-dialog__close) {
  color: rgba(255, 255, 255, 0.7);
}

:deep(.core-cards-dialog .el-dialog__body) {
  padding: 20px;
}

.core-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 15px;
  max-height: 500px;
  overflow-y: auto;
}

.core-card-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.core-card-item:hover {
  transform: translateY(-5px);
  filter: brightness(1.1);
}

.core-card-image {
  width: 100%;
  aspect-ratio: 0.7;
  object-fit: contain;
}

.core-card-stats {
  margin-top: 5px;
  text-align: center;
  font-size: 14px;
}

.core-card-percentage {
  display: block;
  font-weight: bold;
  color: #1e90ff;
}

.core-card-count {
  display: block;
  color: rgba(255, 255, 255, 0.7);
}

/* 響應式調整 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: start;
    gap: 10px;
  }

  .page-title {
    text-align: left;
  }

  .deck-type-controls {
    width: 100%;
  }

  .cards-header,
  :deep(.card-list-item) {
    padding: 8px 5px;
  }

  .card-col-count {
    width: 40px;
  }
}
</style>
