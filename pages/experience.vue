<!-- pages/experience.vue -->
<template>
  <div class="experience-page py-4 px-4">
    <!-- 頁面頭部 -->
    <div class="page-header mb-4">
      <div class="header-content container mx-auto px-4">
        <h1 class="page-title">心得文章</h1>
      </div>
    </div>

    <!-- 文章類型選擇 -->
    <div class="article-type-selector mb-6">
      <div class="flex space-x-4">
        <button
          @click="selectArticleType('free')"
          :class="[
            'px-6 py-2 rounded-lg transition-colors',
            selectedType === 'free'
              ? 'bg-[#1e90ff] text-white'
              : 'bg-[rgba(30,144,255,0.1)] text-[#1e90ff] hover:bg-[rgba(30,144,255,0.2)]',
          ]"
        >
          免費文章
        </button>
        <button
          @click="selectArticleType('paid')"
          :class="[
            'px-6 py-2 rounded-lg transition-colors',
            selectedType === 'paid'
              ? 'bg-[#1e90ff] text-white'
              : 'bg-[rgba(30,144,255,0.1)] text-[#1e90ff] hover:bg-[rgba(30,144,255,0.2)]',
          ]"
        >
          付費文章
        </button>
      </div>
    </div>

    <!-- 搜尋区域 -->
    <div class="search-bar mb-4">
      <div class="relative w-full md:w-1/2 lg:w-1/3">
        <input
          type="text"
          v-model="searchQuery"
          placeholder="搜尋文章..."
          class="pl-10 pr-4 py-2 w-full bg-[rgba(30,144,255,0.1)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#1e90ff] text-white placeholder-gray-400"
        />
        <span class="absolute left-3 top-2.5 text-[#1e90ff]">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </span>
      </div>
    </div>

    <!-- 載入中提示 -->
    <div v-if="loading && !articles.length" class="loading-container">
      <div
        class="animate-spin h-12 w-12 border-t-2 border-b-2 border-[#1e90ff] rounded-full"
      ></div>
      <p class="mt-4 text-[#1e90ff]">載入中...</p>
    </div>

    <!-- 錯誤提示 -->
    <div v-else-if="error" class="empty-state">
      <i class="el-icon-warning-outline text-[#1e90ff]"></i>
      <p class="text-[#1e90ff]">{{ error }}</p>
      <button
        @click="fetchArticles"
        class="mt-4 bg-[#1e90ff] px-4 py-2 rounded hover:bg-[rgba(30,144,255,0.8)] text-white"
      >
        重試
      </button>
    </div>

    <!-- 文章列表 -->
    <div v-else class="articles-list">
      <div
        v-for="article in filteredArticles"
        :key="article.message_id"
        class="article-item mb-4 bg-[rgba(30,144,255,0.05)] rounded-lg overflow-hidden border border-[rgba(30,144,255,0.1)]"
      >
        <div class="p-4">
          <div class="flex">
            <!-- 用户头像 -->
            <div class="flex-shrink-0 mr-3">
              <img
                :src="
                  article.user_info?.headimg || '/images/default-avatar.png'
                "
                alt="用戶頭像"
                class="w-10 h-10 rounded-full border border-[rgba(30,144,255,0.2)]"
              />
            </div>

            <!-- 文章内容 -->
            <div class="flex-grow">
              <!-- 用户信息 -->
              <div class="flex items-center mb-1">
                <div class="font-semibold text-white">
                  {{ article.user_info?.nickname || "匿名用戶" }}
                </div>
                <div class="text-[#1e90ff] text-xs ml-2">
                  · {{ formatDate(article.create_time) }}
                </div>
                <div
                  v-if="selectedType === 'paid'"
                  class="ml-2 px-2 py-0.5 bg-[rgba(255,215,0,0.2)] text-[#ffd700] text-xs rounded-full"
                >
                  付費文章
                </div>
              </div>

              <!-- 桌面版布局 -->
              <div
                class="hidden md:block cursor-pointer"
                @click="navigateToArticleDetail(article.message_id)"
              >
                <div class="flex gap-4 min-h-[140px]">
                  <!-- 左側文字區域 -->
                  <div class="flex-1 min-w-0">
                    <!-- 文章标题 -->
                    <h3
                      v-if="article.title"
                      class="text-base font-bold mb-2 line-clamp-2 text-white hover:text-[#1e90ff] transition-colors"
                    >
                      {{ article.title }}
                    </h3>

                    <!-- 文章内容 -->
                    <div
                      class="text-sm text-gray-300 line-clamp-4 leading-relaxed"
                    >
                      {{ article.content }}
                    </div>
                  </div>

                  <!-- 右側圖片區域 -->
                  <div
                    v-if="article.images && article.images.length > 0"
                    class="flex-shrink-0"
                  >
                    <div class="flex gap-2">
                      <!-- 顯示前2-3張圖片 -->
                      <template
                        v-for="(img, index) in getProcessedImages(
                          article.images
                        ).slice(0, 3)"
                        :key="index"
                      >
                        <div
                          class="w-20 h-20 rounded-lg overflow-hidden bg-slate-700 relative"
                          @click.stop="openImageViewer(img)"
                        >
                          <img
                            :src="img"
                            :alt="`附圖 ${index + 1}`"
                            class="w-full h-full object-cover hover:scale-105 transition-transform duration-200"
                          />
                        </div>
                      </template>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 手機版布局（巴哈姆特風格）-->
              <div
                class="md:hidden cursor-pointer"
                @click="navigateToArticleDetail(article.message_id)"
              >
                <div class="flex gap-3 min-h-[120px]">
                  <!-- 左側文字區域 -->
                  <div class="flex-1 min-w-0">
                    <!-- 文章標題 -->
                    <h3
                      v-if="article.title"
                      class="text-sm font-bold mb-2 line-clamp-2 text-white hover:text-[#1e90ff] transition-colors"
                    >
                      {{ article.title }}
                    </h3>

                    <!-- 文章內容預覽 -->
                    <div
                      class="text-sm text-gray-300 line-clamp-3 leading-relaxed"
                    >
                      {{ article.content }}
                    </div>
                  </div>

                  <!-- 右側圖片區域 -->
                  <div
                    v-if="article.images && article.images.length > 0"
                    class="flex-shrink-0"
                  >
                    <div class="flex gap-1.5">
                      <!-- 顯示前2-3張圖片 -->
                      <template
                        v-for="(img, index) in getProcessedImages(
                          article.images
                        ).slice(0, 3)"
                        :key="index"
                      >
                        <div
                          class="w-16 h-16 rounded-lg overflow-hidden bg-slate-700 relative"
                          @click.stop="openImageViewer(img)"
                        >
                          <img
                            :src="img"
                            :alt="`附圖 ${index + 1}`"
                            class="w-full h-full object-cover hover:scale-105 transition-transform duration-200"
                          />
                        </div>
                      </template>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 文章交互 -->
              <div
                class="flex text-[#1e90ff] text-sm mt-2 pt-2 border-t border-[rgba(30,144,255,0.1)]"
              >
                <div
                  class="mr-6 flex items-center cursor-pointer hover:text-white"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                    />
                  </svg>
                  {{ article.hot }}
                </div>
                <div
                  class="mr-6 flex items-center cursor-pointer hover:text-white"
                  @click="navigateToComments(article.message_id)"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                    />
                  </svg>
                  {{ article.comment_count }}
                </div>
                <div
                  class="flex items-center cursor-pointer hover:text-white"
                  @click="shareArticle(article)"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"
                    />
                  </svg>
                  分享
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 載入更多按鈕 -->
    <div v-if="canLoadMore" class="text-center mt-4">
      <button
        @click="loadMore"
        class="my-deck-button px-6 py-2 rounded hover:bg-[rgba(30,144,255,0.8)] disabled:opacity-50"
        :disabled="loadingMore"
      >
        {{ loadingMore ? "載入中..." : "載入更多" }}
      </button>
    </div>

    <!-- 圖片查看器 -->
    <div
      v-if="showImageViewer"
      class="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50"
      @click="showImageViewer = false"
    >
      <div class="relative max-w-full max-h-full p-4">
        <img
          :src="selectedImage"
          class="max-w-full max-h-full rounded-lg shadow-xl"
          alt="查看大圖"
        />
        <button
          class="absolute top-4 right-4 bg-black bg-opacity-50 p-2 rounded-full text-white"
          @click.stop="showImageViewer = false"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import { useRouter } from "vue-router";

const router = useRouter();

// 使用 SSR 數據獲取文章列表
const { data: articlesData } = await useFetch("/api/message/list", {
  method: "POST",
  body: {
    game_id: 1,
    page: 1,
    page_nums: 10,
    category_id: 2,
  },
  default: () => ({ code: 500, data: { list: [], total_page: 1 } }),
  // 啟用 SSR 以提高首屏載入速度和 SEO
});

const loading = ref(false);
const loadingMore = ref(false);
const error = ref(null);
const searchQuery = ref("");
const selectedType = ref("free");
const showImageViewer = ref(false);
const selectedImage = ref("");
const currentPage = ref(1);

// 使用 computed 處理文章數據
const articles = computed(() => {
  const data = articlesData.value;
  if (data?.code === 200 && data?.data?.list) {
    return data.data.list;
  }
  return [];
});

const canLoadMore = computed(() => {
  const data = articlesData.value;
  if (data?.code === 200 && data?.data?.total_page) {
    return currentPage.value < data.data.total_page;
  }
  return false;
});

// 清理函數
const cleanup = () => {
  loading.value = false;
  loadingMore.value = false;
  error.value = null;
  articles.value = [];
  currentPage.value = 1;
  canLoadMore.value = true;
};

onUnmounted(() => {
  cleanup();
});

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return "未知時間";

  try {
    let date;

    // 處理字符串格式的日期時間 (例如: "2024-09-17 20:13:06")
    if (typeof timestamp === "string") {
      date = new Date(timestamp.replace(/-/g, "/")); // 將 - 替換為 / 以確保跨瀏覽器兼容性
    } else {
      // 處理 Unix 時間戳
      date = new Date(timestamp * 1000);
    }

    // 檢查是否為有效日期
    if (isNaN(date.getTime())) {
      return "未知時間";
    }

    return date.toLocaleDateString("zh-TW", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  } catch (error) {
    console.error("日期格式化錯誤:", error);
    return "時間錯誤";
  }
};

// 選擇文章類型
const selectArticleType = (type) => {
  selectedType.value = type;
  currentPage.value = 1;
  articles.value = [];
  fetchArticles();
};

// 獲取文章列表
const fetchArticles = async () => {
  if (loading.value) return;

  loading.value = true;
  error.value = null;

  try {
    const params = {
      game_id: 1,
      page: currentPage.value,
      page_nums: 10,
    };

    if (selectedType.value === "paid") {
      params.is_pay = 1;
    } else {
      params.category_id = 2;
    }

    const response = await fetch("/api/message/list", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(params),
    });
    const data = await response.json();

    if (data.code === 200 && data.data) {
      if (currentPage.value === 1) {
        articles.value = data.data.list || [];
      } else {
        articles.value = [...articles.value, ...(data.data.list || [])];
      }

      // 更新是否可以載入更多
      canLoadMore.value = currentPage.value < data.data.total_page;
    } else {
      error.value = data.msg || "載入文章失敗，請稍後再試";
    }
  } catch (err) {
    error.value = "載入文章失敗，請稍後再試";
    console.error("Error fetching articles:", err);
  } finally {
    loading.value = false;
  }
};

// 載入更多
const loadMore = async () => {
  if (loadingMore.value) return;

  loadingMore.value = true;
  currentPage.value++;
  await fetchArticles();
  loadingMore.value = false;
};

// 過濾文章
const filteredArticles = computed(() => {
  if (!searchQuery.value) return articles.value;

  const query = searchQuery.value.toLowerCase();
  return articles.value.filter(
    (article) =>
      article.title?.toLowerCase().includes(query) ||
      article.content?.toLowerCase().includes(query)
  );
});

const getProcessedImages = (images) => {
  if (!images) return [];

  // 如果 images 是字符串，則按逗號分割
  if (typeof images === "string") {
    return images
      .split(",")
      .map((img) => img.trim())
      .filter((img) => img);
  }

  // 如果 images 是數組，直接返回
  if (Array.isArray(images)) {
    return images;
  }

  return [];
};

const openImageViewer = (image) => {
  selectedImage.value = image;
  showImageViewer.value = true;
};

// 導航
const navigateToArticleDetail = (id) => {
  router.push(`/message/${id}`);
};

const navigateToComments = (id) => {
  router.push(`/message/${id}#comments`);
};

const shareArticle = (article) => {
  if (navigator.share) {
    navigator.share({
      title: article.title,
      text: article.content,
      url: window.location.origin + `/message/${article.message_id}`,
    });
  } else {
    // 複製連結到剪貼簿
    const url = window.location.origin + `/message/${article.message_id}`;
    navigator.clipboard.writeText(url);
    alert("連結已複製到剪貼簿");
  }
};

onMounted(() => {
  // SSR 數據已經在服務端獲取，這裡只需要客戶端特定的初始化
  console.log("體驗文章頁面已載入，文章數量:", articles.value.length);
});
</script>

<style scoped>
.experience-page {
  min-height: 100vh;
  background-color: #061224;
  color: #ffffff;
}

.page-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 0 10px rgba(30, 144, 255, 0.3);
}

.article-item {
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  min-height: 140px; /* 固定最小高度 */
}

.article-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(30, 144, 255, 0.1);
}

/* 統一固定高度佈局樣式 */
.article-item .p-4 {
  padding: 16px;
}

/* 文字截斷樣式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-4 {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 圖片容器樣式 */
.threads-image-container {
  aspect-ratio: 1;
  overflow: hidden;
  border-radius: 8px;
  border: 1px solid rgba(30, 144, 255, 0.1);
}

/* 手機版文字截斷樣式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 手機版文章固定高度容器 */
@media (max-width: 768px) {
  .article-item {
    min-height: 120px;
  }

  .article-item .p-4 {
    padding: 12px 16px;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: rgba(30, 144, 255, 0.05);
  border-radius: 16px;
  border: 1px solid rgba(30, 144, 255, 0.1);
}

.empty-state {
  text-align: center;
  padding: 2rem;
  background-color: rgba(30, 144, 255, 0.05);
  border-radius: 16px;
  border: 1px solid rgba(30, 144, 255, 0.1);
}

.my-deck-button {
  background-color: #1e90ff;
  color: white;
  transition: all 0.3s ease;
  border: 1px solid rgba(30, 144, 255, 0.2);
}

.my-deck-button:hover:not(:disabled) {
  background-color: rgba(30, 144, 255, 0.8);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(30, 144, 255, 0.2);
}
</style>
