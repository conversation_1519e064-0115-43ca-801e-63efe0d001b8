<template>
  <div class="sponsor-page">
    <div class="container mx-auto px-4 py-8 max-w-4xl">
      <!-- 標題區域 -->
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold bg-gradient-to-r from-white to-[#1e90ff] bg-clip-text text-transparent mb-4">
          頁面暫時不可用
        </h1>
        <p class="text-lg text-gray-300 max-w-2xl mx-auto leading-relaxed">
          贊助功能正在開發中，敬請期待！
        </p>
      </div>

      <!-- 返回首頁按鈕 -->
      <div class="text-center">
        <NuxtLink 
          to="/" 
          class="inline-flex items-center px-6 py-3 bg-[#1e90ff] hover:bg-[#1976d2] text-white font-medium rounded-lg transition-colors duration-200"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
          </svg>
          返回首頁
        </NuxtLink>
      </div>
    </div>
  </div>
</template>

<script setup>
// 設置頁面元數據
useHead({
  title: '頁面暫時不可用 - 遊戲王資訊站',
  meta: [
    { name: 'description', content: '贊助功能正在開發中，敬請期待！' },
    { name: 'robots', content: 'noindex, nofollow' }
  ]
});
</script>

<style scoped>
.sponsor-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0e1a 0%, #1a1f35 100%);
  padding-top: 2rem;
}

.container {
  position: relative;
  z-index: 1;
}

/* 背景裝飾 */
.sponsor-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 50%, rgba(30, 144, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(0, 191, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}
</style>