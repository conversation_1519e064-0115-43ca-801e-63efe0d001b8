<!-- pages/tierlist.vue -->
<template>
  <div class="tierlist-page">
    <!-- 頂部導航與標題區 - 更緊湊的單行設計 -->
    <div class="page-header">
      <div class="container mx-auto px-2">
        <div class="header-content">
          <h1 class="page-title">環境 TIER LIST</h1>
        </div>
      </div>
    </div>

    <!-- 主要內容區 -->
    <div class="container mx-auto py-2 px-2">
      <!-- 頂部信息欄 -->
      <div class="meta-bar mb-3">
        <!-- 環境選擇 -->
        <div class="meta-item">
          <div class="meta-icon">
            <i class="el-icon-date"></i>
          </div>
          <div class="meta-text">
            <div class="meta-label">當前環境</div>
            <div class="meta-value">
              {{
                selectedEnv
                  ? envList.find((e) => e.env_id === selectedEnv)?.title
                  : "載入中..."
              }}
            </div>
          </div>
        </div>

        <!-- 更新日期 -->
        <div class="meta-item">
          <div class="meta-icon">
            <i class="el-icon-time"></i>
          </div>
          <div class="meta-text">
            <div class="meta-label">更新日期</div>
            <div class="meta-value">{{ formatCurrentDate() }}</div>
          </div>
        </div>
      </div>

      <!-- 數據分析圖表區域 - 移到説明區下方 -->
      <div class="charts-container mb-3">
        <!-- 載入中狀態 -->
        <div v-if="isInitialLoading" class="chart-loading-state">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div class="chart-card">
              <h3 class="chart-title">趨勢</h3>
              <div class="chart-wrapper flex items-center justify-center">
                <div
                  class="animate-spin h-12 w-12 border-t-2 border-b-2 border-blue-500 rounded-full"
                ></div>
              </div>
            </div>
            <div class="chart-card">
              <h3 class="chart-title">分佈</h3>
              <div class="chart-wrapper flex items-center justify-center">
                <div
                  class="animate-spin h-12 w-12 border-t-2 border-b-2 border-blue-500 rounded-full"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 圖表內容 -->
        <div v-else class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- 趨勢圖 -->
          <div class="chart-card">
            <h3 class="chart-title">趨勢</h3>
            <div class="chart-wrapper">
              <AsyncChartComponent
                :loading="trendLoading"
                :data="trendChartData"
                :error="trendChartError"
                @load="loadTrendData"
              />
            </div>
          </div>

          <!-- 餅圖 -->
          <div class="chart-card">
            <h3 class="chart-title">分佈</h3>
            <div class="chart-wrapper">
              <AsyncPieChartComponent
                :loading="pieChartLoading"
                :data="pieChartData"
                @load="loadPieChartData"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 主要Tier List內容 -->
      <div v-if="isLoading" class="loading-state">
        <el-skeleton :rows="5" animated />
      </div>
      <div
        v-else-if="Object.keys(tierListData).length === 0"
        class="empty-state"
      >
        <i
          class="el-icon-warning-outline"
          style="font-size: 48px; color: rgba(255, 215, 0, 0.5)"
        ></i>
        <p>目前尚無 Tier List 數據</p>
      </div>
      <div v-else class="tier-groups-container">
        <div
          v-for="(deckGroup, tier) in tierListData"
          :key="tier"
          class="tier-group"
          :class="`tier-${tier}-group`"
        >
          <div class="tier-header">
            <div class="tier-badge" :class="`tier-${tier}`">{{ tier }}</div>
            <h3 class="tier-title">TIER {{ tier }}</h3>
            <div class="tier-line"></div>
          </div>
          <div class="tier-decks-wrapper">
            <div class="tier-decks">
              <div
                v-for="deck in deckGroup"
                :key="deck.tag_id"
                class="tier-deck-item"
                @click="goToSimilarDeck(deck)"
              >
                <div class="tier-deck-image">
                  <img :src="deck.photo" alt="牌組圖片" />
                </div>
                <div class="tier-deck-name">{{ deck.title }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from "vue";
import { ElMessage } from "element-plus";
import { useApi } from "~/composables/useApi";
import Chart from "chart.js/auto";
import { useRouter } from "vue-router";

// 初始化API實例
const GoodsgroupApi = useApi();
const generalApi = useApi();

// 使用 SSR 數據獲取環境列表
const { data: envListData } = await useFetch("/api/Goodsgroup/env-list", {
  method: "POST",
  body: {},
  default: () => ({ code: 500, data: [] }),
  server: false,
});

// 基本狀態
const selectedGame = ref(1);
const selectedEnv = ref(null);
const tierListData = ref({});
const isLoading = ref(false);
const isInitialLoading = ref(false); // SSR 數據已預載，不需要初始載入狀態
const dateRange = ref([]);
const totalCount = ref(0);

// 使用 computed 處理環境列表數據
const envList = computed(() => {
  const data = envListData.value;
  if (data?.code === 200 && data?.data) {
    return data.data;
  }
  return [];
});

// 圖表相關
const trendLoading = ref(false);
const pieChartLoading = ref(false);
const trendChartData = ref(null);
const pieChartData = ref(null);
const trendChartError = ref("");

// 監聽數據請求的狀態
const isTrendDataLoading = ref(false);
const isPieChartDataLoading = ref(false);

// 预定义颜色数组
const predefinedColors = [
  "#FF6F61", // 珊瑚红
  "#6B5B95", // 紫色
  "#88B04B", // 橄榄绿
  "#F7CAC9", // 粉红
  "#92A8D1", // 淡蓝
  "#955251", // 深红
  "#B565A7", // 紫红
  "#009B77", // 深绿
  "#DD4124", // 橙红
  "#45B8AC", // 青绿
];

// 饼图颜色数组
const pieColors = [
  "#FF6F61", // 珊瑚红
  "#6B5B95", // 紫色
  "#88B04B", // 橄榄绿
  "#F7CAC9", // 粉红
  "#92A8D1", // 淡蓝
  "#999999", // 灰色（用于"其他"）
];

// 獲取當前日期函數
const formatCurrentDate = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");
  return `${year}年${month}月${day}日`;
};

// 日期格式化
const formatDate = (date) => {
  if (!date) return null;
  const d = new Date(date);
  return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(
    2,
    "0"
  )}-${String(d.getDate()).padStart(2, "0")}`;
};

// 環境載入函數
const loadEnvironments = async () => {
  try {
    const response = await generalApi.getEnvironmentList(
      selectedGame.value || 1
    );

    if (response && response.code === 200) {
      envList.value = response.data || [];

      // 如果環境列表不為空，設置默認選中的環境
      if (envList.value.length > 0 && !selectedEnv.value) {
        selectedEnv.value = envList.value[0].env_id;

        // 確保 selectedEnv 設定完成後再載入資料
        await nextTick();

        // 再次確認 selectedEnv 已設置
        if (selectedEnv.value) {
          await Promise.all([
            loadTierListData(),
            loadTrendData(),
            loadPieChartData(),
          ]);
          // 所有數據載入完成後關閉初始載入狀態
          isInitialLoading.value = false;
        } else {
          console.warn("環境ID設置失敗");
          isInitialLoading.value = false;
        }
      } else if (envList.value.length === 0) {
        console.warn("環境列表為空");
        isInitialLoading.value = false;
      }
    } else {
      console.warn("環境列表API響應不成功:", response);
      envList.value = [];
      isInitialLoading.value = false;
    }
  } catch (error) {
    console.error("載入環境列表失敗:", error);
    isInitialLoading.value = false;
  }
};

// 載入 Tier List 數據
const loadTierListData = async () => {
  if (!selectedEnv.value) {
    ElMessage.warning("請先選擇環境");
    return;
  }

  isLoading.value = true;
  tierListData.value = {};

  try {
    const response = await GoodsgroupApi.getGoodsgroupTierList(
      selectedGame.value
    );

    if (response && response.code === 200 && response.data) {
      tierListData.value = response.data;
    } else {
      ElMessage.warning("Tier List 資料尚未更新，敬請期待");
    }
  } catch (error) {
    ElMessage.error("載入 Tier List 資料失敗");
    console.error("載入 Tier List 資料失敗:", error);
  } finally {
    isLoading.value = false;
  }
};

// 載入趨勢圖數據
const loadTrendData = async () => {
  if (isTrendDataLoading.value) return;

  // 設置載入中狀態
  trendLoading.value = true;
  isTrendDataLoading.value = true;
  trendChartError.value = "";

  // 確保環境ID已設置
  if (!selectedEnv.value && envList.value.length > 0) {
    selectedEnv.value = envList.value[0].env_id;
  }

  // 如果還是沒有環境ID，等待環境載入完成
  if (!selectedEnv.value) {
    console.log("等待環境載入完成...");
    trendLoading.value = false;
    isTrendDataLoading.value = false;
    return;
  }

  try {
    const params = {
      game_id: selectedGame.value || 1, // 確保至少有默認值
      env_id: selectedEnv.value,
    };

    if (dateRange.value && dateRange.value.length === 2) {
      params.start_time = formatDate(dateRange.value[0]);
      params.end_time = formatDate(dateRange.value[1]);
    }

    const response = await GoodsgroupApi.getDeckPercentage(params);

    if (response && response.code === 200) {
      // 立即處理數據
      processTrendChartData(response.data);

      // 處理完數據後立即更新狀態，避免loading狀態下的閃爍
      trendLoading.value = false;
      isTrendDataLoading.value = false;
    } else {
      trendChartError.value = response?.msg || "趨勢圖API錯誤";
      trendChartData.value = null;
      trendLoading.value = false;
      isTrendDataLoading.value = false;
    }
  } catch (error) {
    console.error("载入趋势图数据失败:", error);
    trendChartError.value = error?.message || "趨勢圖API請求失敗";
    trendChartData.value = null;
    trendLoading.value = false;
    isTrendDataLoading.value = false;
  }
};

// 載入餅圖數據
const loadPieChartData = async () => {
  if (isPieChartDataLoading.value) return;

  // 設置載入中狀態
  pieChartLoading.value = true;
  isPieChartDataLoading.value = true;

  // 確保環境ID已設置
  if (!selectedEnv.value && envList.value.length > 0) {
    selectedEnv.value = envList.value[0].env_id;
  }

  // 如果還是沒有環境ID，等待環境載入完成
  if (!selectedEnv.value) {
    // console.log('圓餅圖：等待環境載入完成...');
    pieChartLoading.value = false;
    isPieChartDataLoading.value = false;
    return;
  }

  try {
    const params = {
      game_id: selectedGame.value || 1, // 確保至少有默認值
      env_id: selectedEnv.value,
    };

    if (dateRange.value && dateRange.value.length === 2) {
      params.start_time = formatDate(dateRange.value[0]);
      params.end_time = formatDate(dateRange.value[1]);
    }

    const response = await GoodsgroupApi.getPieChartData(params);

    if (response && response.code === 200) {
      // 立即處理數據
      processPieChartData(response.data);

      // 處理完數據後立即更新狀態，避免loading狀態下的閃爍
      pieChartLoading.value = false;
      isPieChartDataLoading.value = false;
    } else {
      console.warn("餅圖API響應不成功:", response);
      pieChartData.value = null;
      pieChartLoading.value = false;
      isPieChartDataLoading.value = false;
    }
  } catch (error) {
    console.error("載入餅圖數據失敗:", error);
    pieChartData.value = null;
    pieChartLoading.value = false;
    isPieChartDataLoading.value = false;
  }
};

// 處理趋势图数据
const processTrendChartData = (data) => {
  // 处理可能的多层嵌套数据
  let deckData = data?.data || data;

  // 如果deckData包含percentages属性且不为空
  if (
    deckData &&
    deckData.percentages &&
    Object.keys(deckData.percentages).length > 0
  ) {
    // 构建图表所需格式
    const timeLabels = [];
    const datasets = [];
    const deckTypes = Object.keys(deckData.percentages);

    // 收集所有日期
    deckTypes.forEach((deckType) => {
      deckData.percentages[deckType].forEach((item) => {
        if (!timeLabels.includes(item.date)) {
          timeLabels.push(item.date);
        }
      });
    });

    // 排序日期
    timeLabels.sort();

    // 为每种牌组创建数据集
    deckTypes.forEach((deckType, index) => {
      const color = predefinedColors[index % predefinedColors.length];
      const dataset = {
        label: deckType,
        data: Array(timeLabels.length).fill(0),
        borderColor: color,
        backgroundColor: color,
        tension: 0.3,
        fill: false,
      };

      // 填充数据
      deckData.percentages[deckType].forEach((item) => {
        const dateIndex = timeLabels.indexOf(item.date);
        if (dateIndex !== -1) {
          dataset.data[dateIndex] = item.percentage;
        }
      });

      datasets.push(dataset);
    });

    trendChartData.value = { labels: timeLabels, datasets };
  } else {
    console.warn("趋势数据为空或格式不符合预期:", deckData);
    trendChartData.value = null;
  }
};

// 处理饼图数据
const processPieChartData = (data) => {
  // 处理可能存在的嵌套 data 结构
  const responseData = data?.data || data;

  // 确保我们有数组数据
  if (Array.isArray(responseData)) {
    // 嘗試從tierListData中獲取牌組圖片，並加入到餅圖數據中
    responseData.forEach((deck) => {
      // 從tierListData中查找匹配的牌組
      let found = false;
      for (const tier in tierListData.value) {
        const matchingDeck = tierListData.value[tier]?.find?.(
          (td) => td.title === deck.title
        );
        if (matchingDeck && matchingDeck.photo) {
          // 加入圖片URL到餅圖數據中
          deck.photo = matchingDeck.photo;
          found = true;
          break;
        }
      }

      // 如果沒有找到匹配的牌組圖片，使用默認圖片
      if (!found) {
        // 根據牌組名稱的第一個字符選擇不同的默認圖片
        const firstChar = deck.title.charAt(0).toLowerCase();
        const charCode = firstChar.charCodeAt(0) % 5; // 0-4的範圍
        deck.photo = `/images/deck-defaults/deck-${charCode}.jpg`;
      }
    });

    // 取前 5 名牌组，剩余归为"其他"
    const top5 = responseData.slice(0, 5);
    const others = responseData.slice(5);
    const othersCount = others.reduce(
      (sum, deck) => sum + (Number(deck.count) || 0),
      0
    );

    // 计算总数用于百分比
    const totalDeckCount = responseData.reduce(
      (sum, deck) => sum + (Number(deck.count) || 0),
      0
    );

    // 更新总计数
    totalCount.value = totalDeckCount;

    // 构建图表所需数据
    const labels = [];
    const data = [];

    // 加入前5名牌组
    top5.forEach((deck) => {
      labels.push(deck.title);
      data.push(Number(deck.count) || 0);
    });

    // 如果有其他牌组，将它们合并为"其他"类别
    if (othersCount > 0) {
      labels.push("其他");
      data.push(othersCount);
    }

    const chartData = {
      labels: labels,
      datasets: [
        {
          data: data,
          backgroundColor: pieColors.slice(0, labels.length),
        },
      ],
      // 保存原始數據，以便圓餅圖組件可以使用牌組圖片
      originalData: responseData,
    };

    pieChartData.value = chartData;
  } else {
    console.warn("饼图数据格式不符合预期:", responseData);
    pieChartData.value = null;
  }
};

// 客戶端初始化
onMounted(() => {
  // SSR 數據已經在服務端獲取，這裡只需要客戶端特定的初始化
  console.log("Tier List 頁面已載入，環境數量:", envList.value.length);

  // 如果有環境數據，自動選擇第一個環境並載入數據
  if (envList.value.length > 0) {
    selectedEnv.value = envList.value[0].env_id;
    // 載入初始數據
    loadTierListData();
    loadTrendData();
    loadPieChartData();
  }
});

// 監聽環境變更 - 只在用戶手動變更時觸發
watch(selectedEnv, (newValue, oldValue) => {
  // 只有在不是初始設定時才重新載入
  if (oldValue !== null && newValue !== oldValue) {
    loadTierListData();
    loadTrendData();
    loadPieChartData();
  }
});

// SEO配置
useHead({
  title: "遊戲王環境 Tier List - iWantCard",
  meta: [
    {
      name: "description",
      content:
        "查看最新遊戲王卡片牌組的 Tier List 評級。了解當前環境中的頂級牌組、強力牌組和潛力牌組。",
    },
    { property: "og:title", content: "遊戲王環境 Tier List - iWantCard" },
    {
      property: "og:description",
      content:
        "查看最新遊戲王卡片牌組的 Tier List 評級。了解當前環境中的頂級牌組、強力牌組和潛力牌組。",
    },
    { property: "og:type", content: "website" },
  ],
});

// 加入导航到牌组详情页的方法
const router = useRouter();

// 加入跳轉到相似牌組頁面的函數
const goToSimilarDeck = (deck) => {
  router.push({
    path: "/Goodsgroup/similardeck",
    query: {
      title: deck.title,
      env_id: selectedEnv.value,
      game_id: selectedGame.value,
    },
  });
};
</script>

<style scoped>
/* 頁面整體佈局 */
.tierlist-page {
  background-color: #091020;
  background-image: radial-gradient(
      circle at 20% 35%,
      rgba(255, 215, 0, 0.1) 0%,
      transparent 40%
    ),
    radial-gradient(
      circle at 80% 10%,
      rgba(255, 140, 0, 0.08) 0%,
      transparent 40%
    );
  min-height: 100vh;
  width: 100%;
  color: #fff;
  font-family: "Noto Sans TC", sans-serif;
  /* 防止雙重滾動 */
  overflow-x: hidden;
  position: relative;
}

/* 頁面頭部 - 更紧凑 */
.page-header {
  position: relative;
  padding: 8px 0; /* 進一步減少內邊距 */
  overflow: hidden;
  background: linear-gradient(
    180deg,
    rgba(24, 28, 40, 0.9) 0%,
    rgba(9, 16, 32, 0.8) 100%
  );
  border-bottom: 1px solid rgba(255, 215, 0, 0.2);
}

.header-content {
  display: flex;
  justify-content: center;
  align-items: center;
}

.page-title {
  font-size: 24px; /* 進一步縮小字體大小 */
  font-weight: 800;
  letter-spacing: 1px;
  margin: 0; /* 移除所有邊距 */
  text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
  background: linear-gradient(to right, #fff, #ffd700);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
}

/* 移除不需要的样式 */
.header-title-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 auto;
}

.header-subtitle {
  display: none; /* 隱藏副標題 */
}

/* 信息欄 */
.meta-bar {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 15px;
  background: rgba(20, 30, 48, 0.5);
  border-radius: 12px;
  padding: 10px;
  border: 1px solid rgba(255, 215, 0, 0.15);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(8px);
  gap: 15px;
}

.meta-item {
  display: flex;
  align-items: center;
  margin-right: 15px;
}

.meta-icon {
  width: 36px;
  height: 36px;
  border-radius: 10px;
  background: rgba(255, 215, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  font-size: 16px;
  color: #ffd700;
  border: 1px solid rgba(255, 215, 0, 0.3);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.meta-text {
  display: flex;
  flex-direction: column;
}

.meta-label {
  color: rgba(255, 255, 255, 0.5);
  font-size: 12px;
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.meta-value {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  font-size: 16px;
}

.meta-tabs {
  margin-left: auto;
}

/* Tier 說明區 - 更紧凑 */
.tier-description {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px; /* 减少下方边距 */
  background: rgba(14, 22, 40, 0.7);
  border-radius: 16px;
  padding: 12px; /* 减少内边距 */
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 215, 0, 0.2);
  backdrop-filter: blur(8px);
}

.tier-explanation {
  flex: 3;
  padding-right: 15px; /* 减少右侧内边距 */
}

.explanation-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px; /* 减少下方边距 */
}

.explanation-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  margin-left: 16px;
  line-height: 1.5;
}

.tier-updates {
  flex: 2;
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  padding-left: 15px; /* 减少左侧内边距 */
}

.update-title {
  font-size: 18px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 16px;
  letter-spacing: 1px;
  position: relative;
  padding-bottom: 8px;
}

.update-title:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 2px;
  background: rgba(255, 215, 0, 0.5);
}

.update-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px; /* 减少下方边距 */
}

.update-icon {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 12px;
  font-size: 14px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.update-icon.up {
  background: linear-gradient(
    135deg,
    rgba(46, 204, 113, 0.1),
    rgba(46, 204, 113, 0.3)
  );
  color: #2ecc71;
  border: 1px solid rgba(46, 204, 113, 0.4);
}

.update-icon.down {
  background: linear-gradient(
    135deg,
    rgba(231, 76, 60, 0.1),
    rgba(231, 76, 60, 0.3)
  );
  color: #e74c3c;
  border: 1px solid rgba(231, 76, 60, 0.4);
}

.update-icon.new {
  background: linear-gradient(
    135deg,
    rgba(52, 152, 219, 0.1),
    rgba(52, 152, 219, 0.3)
  );
  color: #3498db;
  border: 1px solid rgba(52, 152, 219, 0.4);
}

.update-text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

/* Tier List 內容區 */
.tier-groups-container {
  display: flex;
  flex-direction: column;
  gap: 16px; /* 减少间距使布局更紧凑 */
}

.tier-group {
  margin-bottom: 10px; /* 减少底部间距 */
  background: rgba(14, 22, 40, 0.7);
  border-radius: 16px;
  padding: 12px; /* 减少内边距 */
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 215, 0, 0.15);
  backdrop-filter: blur(8px);
  transition: all 0.3s ease;
}

.tier-group:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 215, 0, 0.3);
}

/* 不同 Tier 等級的顏色 */
.tier-1-group {
  border-left: 5px solid #ffd700;
}

.tier-2-group {
  border-left: 5px solid #c0c0c0;
}

.tier-3-group {
  border-left: 5px solid #cd7f32;
}

.tier-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px; /* 减少底部间距 */
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 10px; /* 减少内边距 */
}

.tier-badge {
  width: 40px; /* 缩小徽章尺寸 */
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 800;
  margin-right: 12px; /* 减少右侧间距 */
  color: white;
  font-size: 20px; /* 缩小字体 */
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.tier-1 {
  background: linear-gradient(135deg, #ffd700, #ffa500);
  border: 2px solid rgba(255, 215, 0, 0.6);
}

.tier-2 {
  background: linear-gradient(135deg, #c0c0c0, #a0a0a0);
  border: 2px solid rgba(192, 192, 192, 0.6);
}

.tier-3 {
  background: linear-gradient(135deg, #cd7f32, #8b4513);
  border: 2px solid rgba(205, 127, 50, 0.6);
}

.tier-potential {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
  border: 2px solid rgba(155, 89, 182, 0.6);
}

.tier-title {
  font-size: 24px;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  letter-spacing: 1px;
}

.tier-line {
  flex-grow: 1;
  height: 1px;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.2), transparent);
  margin-left: 16px;
}

.tier-decks-wrapper {
  width: 100%;
  overflow-x: auto;
  overflow-y: visible;
  padding-bottom: 10px;
  /* 防止嵌套滾動 */
  -webkit-overflow-scrolling: touch;
}

.tier-decks-wrapper::-webkit-scrollbar {
  height: 6px;
}

.tier-decks-wrapper::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.tier-decks-wrapper::-webkit-scrollbar-thumb {
  background: rgba(255, 215, 0, 0.3);
  border-radius: 3px;
}

.tier-decks-wrapper::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 215, 0, 0.5);
}

.tier-decks {
  display: flex;
  flex-wrap: wrap;
  gap: 14px; /* 减少项目间距 */
}

.tier-deck-item {
  width: 130px; /* 减少宽度 */
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px; /* 减少内边距 */
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.tier-deck-item:hover {
  transform: translateY(-5px) scale(1.03);
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 215, 0, 0.3);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.tier-deck-image {
  width: 85px; /* 减小图像尺寸 */
  height: 85px; /* 减小图像尺寸 */
  border-radius: 10px; /* 稍微减小圆角 */
  overflow: hidden;
  margin-bottom: 8px; /* 减少底部边距 */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(0, 0, 0, 0.3);
}

.tier-deck-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.tier-deck-item:hover .tier-deck-image img {
  transform: scale(1.1);
}

.tier-deck-name {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: 600;
  text-align: center;
  margin-bottom: 6px;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tier-deck-meta {
  font-size: 12px;
  color: rgba(255, 215, 0, 0.8);
  background: rgba(255, 215, 0, 0.1);
  padding: 3px 8px;
  border-radius: 10px;
  margin-top: 4px;
}

/* 載入和空狀態 */
.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  min-height: 300px;
  background: rgba(14, 22, 40, 0.7);
  border-radius: 16px;
  border: 1px solid rgba(255, 215, 0, 0.15);
}

.empty-state i {
  margin-bottom: 16px;
}

.empty-state p {
  color: rgba(255, 255, 255, 0.6);
  font-size: 16px;
}

/* 響應式調整 */
@media (max-width: 1024px) {
  .tier-description {
    flex-direction: column;
  }

  .tier-explanation {
    padding-right: 0;
    margin-bottom: 20px;
  }

  .tier-updates {
    border-left: none;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-left: 0;
    padding-top: 20px;
  }
}

@media (max-width: 768px) {
  .meta-bar {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    flex-wrap: nowrap;
    padding: 8px 12px;
    gap: 12px;
  }

  .meta-item {
    margin-right: 0;
    flex: 1;
  }

  .meta-icon {
    width: 28px;
    height: 28px;
    font-size: 14px;
    margin-right: 6px;
  }

  .meta-label {
    font-size: 10px;
    margin-bottom: 2px;
  }

  .meta-value {
    font-size: 13px;
  }

  .meta-tabs {
    margin-left: 0;
    width: 100%;
  }

  .tier-decks {
    justify-content: center;
  }

  .tier-deck-item {
    width: 110px;
    padding: 8px;
  }

  .tier-deck-image {
    width: 75px;
    height: 75px;
  }

  .tier-deck-name {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: 6px 0;
  }

  .page-title {
    font-size: 18px;
  }

  .header-subtitle {
    font-size: 10px;
  }

  .container {
    padding: 0 8px;
  }

  .meta-bar {
    margin-bottom: 8px;
    padding: 6px 8px;
  }

  .meta-icon {
    width: 20px;
    height: 20px;
    font-size: 10px;
    margin-right: 4px;
  }

  .meta-label {
    font-size: 9px;
  }

  .meta-value {
    font-size: 11px;
  }

  .tier-group {
    padding: 10px;
    margin-bottom: 8px;
  }

  .tier-badge {
    width: 32px;
    height: 32px;
    font-size: 16px;
    margin-right: 8px;
  }

  .tier-title {
    font-size: 16px;
  }

  .tier-header {
    margin-bottom: 8px;
    padding-bottom: 6px;
  }

  .explanation-text {
    font-size: 11px;
  }

  .tier-deck-item {
    width: calc(25% - 6px);
    min-width: 70px;
    padding: 4px;
  }

  .tier-deck-image {
    width: 55px;
    height: 55px;
    margin-bottom: 3px;
  }

  .tier-deck-name {
    font-size: 10px;
    line-height: 1.2;
  }

  .tier-decks {
    gap: 6px;
    justify-content: flex-start;
  }

  .charts-container {
    margin-bottom: 12px;
  }

  .charts-container {
    margin-bottom: 8px !important;
  }

  .charts-container .grid {
    gap: 0.5rem !important;
  }

  .charts-container .chart-card {
    padding: 10px !important;
    height: 350px !important;
    min-height: 350px !important;
  }

  .charts-container .chart-title {
    font-size: 11px !important;
    margin-bottom: 6px !important;
    font-weight: 500 !important;
  }

  .charts-container .chart-wrapper {
    height: 220px !important;
    max-height: 220px !important;
    position: relative !important;
    overflow: hidden !important;
  }
}

/* 加入圖表相關樣式 */
.date-range-selector {
  background: rgba(15, 23, 42, 0.6);
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.charts-container {
  margin-bottom: 20px;
}

.chart-card {
  background: rgba(15, 23, 42, 0.6);
  border-radius: 10px;
  padding: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  height: 450px; /* 增加高度 */
}

.chart-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #e2e8f0;
  margin-bottom: 15px;
  text-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
  text-align: center;
}

.chart-wrapper {
  height: 380px;
  position: relative;
}

/* 减少 grid 项目之间的间隙 */
.grid.gap-6 {
  gap: 0.75rem;
}

/* 手機版額外優化 */
@media (max-width: 480px) {
  .grid.gap-6 {
    gap: 0.5rem !important;
  }

  .grid-cols-1.lg\:grid-cols-2 {
    grid-template-columns: 1fr !important;
  }

  /* 強制覆蓋所有可能的圖表樣式 */
  .tierlist-page .chart-card {
    height: 300px !important;
    max-height: 300px !important;
    min-height: 300px !important;
    padding: 10px !important;
  }

  .tierlist-page .chart-wrapper {
    height: 220px !important;
    max-height: 220px !important;
  }

  .tierlist-page .chart-title {
    font-size: 11px !important;
    margin-bottom: 6px !important;
  }
}

/* 修復雙重滾動問題 - 所有設備 */
@media (max-width: 768px) {
  /* 頁面整體修復 */
  .tierlist-page {
    overflow-x: hidden !important;
    width: 100vw !important;
    max-width: 100% !important;
  }

  /* 容器修復 */
  .container {
    max-width: 100% !important;
    overflow-x: hidden !important;
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }

  /* 防止內容溢出 */
  * {
    max-width: 100% !important;
    box-sizing: border-box !important;
  }

  /* 圖表容器修復 */
  .charts-container {
    overflow-x: hidden !important;
  }

  /* Tier 項目修復 */
  .tier-groups-container {
    overflow-x: hidden !important;
  }

  .tier-group {
    overflow-x: hidden !important;
  }

  /* 確保所有 flex 容器不會溢出 */
  .tier-decks {
    flex-wrap: wrap !important;
    overflow-x: visible !important;
  }
}
</style>
