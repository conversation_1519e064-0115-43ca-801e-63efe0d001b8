<template>
  <div
    class="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900"
  >
    <!-- Header -->
    <div class="bg-slate-800/50 backdrop-blur-sm border-b border-slate-700/50">
      <div class="max-w-7xl mx-auto px-3 md:px-6 py-4 md:py-6">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-2 md:gap-4">
            <button
              @click="goBack"
              class="p-2 hover:bg-slate-700/50 rounded-lg transition-colors duration-200"
            >
              <ArrowLeft class="w-4 h-4 md:w-5 md:h-5 text-gray-400" />
            </button>
            <h1 class="text-lg md:text-2xl font-bold text-white">我的牌組</h1>
          </div>
          <div class="flex items-center gap-2 md:gap-3">
            <select
              v-model="selectedGameId"
              @change="onGameChange"
              class="bg-slate-700 border border-slate-600 text-white px-2 md:px-4 py-1.5 md:py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm md:text-base"
            >
              <option
                v-for="game in gameList"
                :key="game.game_id"
                :value="game.game_id"
              >
                {{ game.title }}
              </option>
            </select>
            <button
              @click="toggleViewMode"
              class="flex items-center gap-1 md:gap-2 bg-slate-700 hover:bg-slate-600 px-2 md:px-4 py-1.5 md:py-2 rounded-lg text-white transition-colors duration-200 text-sm md:text-base"
            >
              <component
                :is="isGridView ? Grid : List"
                class="w-3 h-3 md:w-4 md:h-4"
              />
              <span class="hidden sm:inline">{{
                isGridView ? "網格視圖" : "列表視圖"
              }}</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-3 md:px-6 py-4 md:py-8">
      <!-- Filters -->
      <div
        class="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 p-3 md:p-4 mb-3 md:mb-4"
      >
        <!-- 桌面版佈局 -->
        <div class="hidden md:flex flex-wrap items-center gap-4 mb-3">
          <!-- 搜尋框 -->
          <div class="flex-1 min-w-[300px]">
            <div class="relative">
              <Search
                class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"
              />
              <input
                v-model="keyWord"
                @keyup.enter="onSearch"
                type="text"
                placeholder="搜尋牌組..."
                class="w-full pl-10 pr-4 py-2.5 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          <!-- 分類按鈕組 -->
          <div class="flex gap-1">
            <button
              @click="setIdentifyFilter(0)"
              :class="[
                'px-4 py-2.5 rounded-lg font-medium transition-colors duration-200 text-sm',
                isIdentify === 0
                  ? 'bg-blue-600 text-white'
                  : 'bg-slate-700 text-gray-300 hover:bg-slate-600',
              ]"
            >
              全部
            </button>
            <button
              @click="setIdentifyFilter(2)"
              :class="[
                'px-4 py-2.5 rounded-lg font-medium transition-colors duration-200 text-sm',
                isIdentify === 2
                  ? 'bg-blue-600 text-white'
                  : 'bg-slate-700 text-gray-300 hover:bg-slate-600',
              ]"
            >
              比賽
            </button>
            <button
              @click="setIdentifyFilter(1)"
              :class="[
                'px-4 py-2.5 rounded-lg font-medium transition-colors duration-200 text-sm',
                isIdentify === 1
                  ? 'bg-blue-600 text-white'
                  : 'bg-slate-700 text-gray-300 hover:bg-slate-600',
              ]"
            >
              一般
            </button>
          </div>

          <!-- 環境篩選器 -->
          <div v-if="selectedGameId !== 0" class="flex gap-1 flex-wrap">
            <button
              v-for="env in envList"
              :key="env.env_id"
              @click="setSelectedEnv(env.env_id)"
              :class="[
                'px-3 py-2.5 rounded-lg font-medium transition-colors duration-200 text-sm',
                selectedEnvId === env.env_id
                  ? 'bg-green-600 text-white'
                  : 'bg-slate-700 text-gray-300 hover:bg-slate-600',
              ]"
            >
              {{ env.title }}
            </button>
          </div>
        </div>

        <!-- 手機版佈局 -->
        <div class="md:hidden space-y-2">
          <!-- 搜尋框 -->
          <div class="relative">
            <Search
              class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"
            />
            <input
              v-model="keyWord"
              @keyup.enter="onSearch"
              type="text"
              placeholder="搜尋牌組..."
              class="w-full pl-9 pr-4 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            />
          </div>

          <!-- 分類按鈕 + 環境選擇 -->
          <div class="flex items-center gap-1">
            <div class="flex gap-1">
              <button
                @click="setIdentifyFilter(0)"
                :class="[
                  'px-2 py-1.5 rounded text-xs font-medium transition-colors duration-200',
                  isIdentify === 0
                    ? 'bg-blue-600 text-white'
                    : 'bg-slate-700 text-gray-300',
                ]"
              >
                全部
              </button>
              <button
                @click="setIdentifyFilter(2)"
                :class="[
                  'px-2 py-1.5 rounded text-xs font-medium transition-colors duration-200',
                  isIdentify === 2
                    ? 'bg-blue-600 text-white'
                    : 'bg-slate-700 text-gray-300',
                ]"
              >
                比賽
              </button>
              <button
                @click="setIdentifyFilter(1)"
                :class="[
                  'px-2 py-1.5 rounded text-xs font-medium transition-colors duration-200',
                  isIdentify === 1
                    ? 'bg-blue-600 text-white'
                    : 'bg-slate-700 text-gray-300',
                ]"
              >
                一般
              </button>
            </div>

            <!-- 環境選擇 -->
            <select
              v-if="selectedGameId !== 0"
              v-model="selectedEnvId"
              @change="setSelectedEnv(selectedEnvId)"
              class="flex-1 bg-slate-700 border border-slate-600 text-white px-2 py-1.5 rounded text-xs focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option
                v-for="env in envList"
                :key="env.env_id"
                :value="env.env_id"
              >
                {{ env.title }}
              </option>
            </select>
          </div>
        </div>

        <!-- 統計信息 - 縮小間隔 -->
        <div class="mt-2 pt-2 border-t border-slate-700/50">
          <div class="flex items-center justify-between text-gray-400 text-sm">
            <div class="flex items-center gap-4">
              <span
                >當前環境：{{
                  envList.find((e) => e.env_id === selectedEnvId)?.title ||
                  "載入中..."
                }}</span
              >
              <span>收錄牌組：{{ totalCards }} 套</span>
            </div>
            <div class="text-xs text-gray-500">
              {{ new Date().toLocaleDateString("zh-TW") }}
            </div>
          </div>
        </div>
      </div>

      <!-- Deck List -->
      <div class="mb-8">
        <!-- Loading -->
        <div v-if="isLoading" class="flex items-center justify-center py-12">
          <div
            class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"
          ></div>
        </div>

        <!-- Empty State -->
        <div
          v-else-if="!hasCardData"
          class="text-center py-12 bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50"
        >
          <FileText class="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <div class="text-gray-400 text-lg mb-2">目前尚無符合條件的牌組</div>
          <p class="text-gray-500">嘗試調整搜尋條件或創建新的牌組</p>
        </div>

        <!-- Grid View -->
        <div
          v-else-if="isGridView"
          class="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 xl:grid-cols-7 gap-2 md:gap-4"
        >
          <div
            v-for="deck in groupData"
            :key="deck.group_id"
            @click="navigateToDeckDetail(deck)"
            class="group bg-slate-800/50 backdrop-blur-sm rounded-xl overflow-hidden border border-slate-700/50 hover:border-blue-500/50 transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/10 cursor-pointer"
          >
            <div class="relative aspect-[3/4] overflow-hidden">
              <img
                :src="deck.image || '/api/placeholder/300/400'"
                :alt="deck.title"
                class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div
                class="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent"
              />
              <div
                :class="[
                  'absolute top-2 left-2 md:top-3 md:left-3 px-1.5 md:px-2 py-0.5 md:py-1 rounded text-xs font-medium border',
                  getTypeStyle(deck.is_identify),
                ]"
              >
                {{ getIdentifyText(deck.is_identify) }}
              </div>
              <div
                class="absolute bottom-1 left-1 right-1 md:bottom-3 md:left-3 md:right-3"
              >
                <h3
                  class="text-white font-semibold text-xs md:text-sm mb-1 line-clamp-2"
                >
                  {{ deck.title }}
                </h3>
                <div
                  class="flex items-center justify-between text-xs text-gray-300"
                >
                  <span class="truncate text-xs">{{
                    deck.env_title || "無"
                  }}</span>
                  <span class="text-xs hidden md:inline">{{
                    formatTime(deck.create_time)
                  }}</span>
                </div>
              </div>
            </div>
            <div class="p-1.5 md:p-3">
              <p
                class="text-gray-400 text-xs md:text-sm mb-2 line-clamp-2 hidden md:block"
              >
                {{ deck.desc }}
              </p>
              <div
                class="flex items-center justify-between text-xs text-gray-500 mb-2 hidden md:flex"
              >
                <span>作者：{{ deck.user_info?.nickname || "未知" }}</span>
              </div>
              <div class="flex gap-1 md:gap-2">
                <button
                  @click.stop="navigateToDeckDetail(deck)"
                  class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-1.5 md:px-3 py-1 md:py-2 rounded text-xs md:text-sm font-medium transition-colors duration-200 flex items-center justify-center gap-0.5 md:gap-1"
                >
                  <Eye class="w-3 h-3 md:w-4 md:h-4" />
                  <span class="hidden sm:inline">查看</span>
                </button>
                <button
                  @click.stop="deleteDeck(deck)"
                  class="bg-slate-700 hover:bg-slate-600 text-white px-1.5 md:px-3 py-1 md:py-2 rounded text-xs md:text-sm transition-colors duration-200"
                >
                  <Trash2 class="w-3 h-3 md:w-4 md:h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- List View -->
        <div
          v-else
          class="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 overflow-hidden"
        >
          <!-- 桌面版列表頭 -->
          <div
            class="hidden md:grid grid-cols-12 gap-4 p-4 bg-slate-700/50 text-sm font-medium text-gray-300 border-b border-slate-600/50"
          >
            <div class="col-span-2">環境</div>
            <div class="col-span-3">標題</div>
            <div class="col-span-4">說明</div>
            <div class="col-span-1">類型</div>
            <div class="col-span-1">作者</div>
            <div class="col-span-1">時間</div>
          </div>

          <!-- 桌面版列表內容 -->
          <div
            v-for="deck in groupData"
            :key="deck.group_id"
            @click="handleListItemClick(deck)"
            :class="[
              'transition-colors duration-200 border-b border-slate-700/30 last:border-b-0 cursor-pointer group',
              'hidden md:grid grid-cols-12 gap-4 p-4 hover:bg-slate-700/30',
            ]"
          >
            <div class="col-span-2 text-sm text-gray-400">
              {{ deck.env_title || "無" }}
            </div>
            <div class="col-span-3">
              <h3
                class="font-medium text-white group-hover:text-blue-400 transition-colors duration-200"
              >
                {{ deck.title }}
              </h3>
            </div>
            <div class="col-span-4 text-sm text-gray-400 line-clamp-2">
              {{ deck.desc }}
            </div>
            <div class="col-span-1">
              <span
                :class="[
                  'px-2 py-1 rounded text-xs font-medium border',
                  getTypeStyle(deck.is_identify),
                ]"
              >
                {{ getIdentifyText(deck.is_identify) }}
              </span>
            </div>
            <div class="col-span-1 text-sm text-gray-400">
              {{ deck.user_info?.nickname || "未知" }}
            </div>
            <div class="col-span-1 text-sm text-gray-400">
              {{ formatTime(deck.create_time) }}
            </div>
          </div>

          <!-- 手機版列表內容 -->
          <div
            v-for="deck in groupData"
            :key="`mobile-${deck.group_id}`"
            @click="showMobileDetail(deck)"
            class="md:hidden flex items-center p-3 border-b border-slate-700/30 last:border-b-0 cursor-pointer hover:bg-slate-700/30 transition-colors duration-200"
          >
            <!-- 類型顏色標記 -->
            <div
              :class="[
                'w-1 h-12 rounded-full mr-3 flex-shrink-0',
                getTypeBgColor(deck.is_identify),
              ]"
            ></div>

            <!-- 主要內容 -->
            <div class="flex-1 min-w-0">
              <h3 class="font-medium text-white text-sm mb-1 truncate">
                {{ deck.title }}
              </h3>
              <p class="text-xs text-gray-400 mb-1 truncate">
                {{ deck.env_title || "無" }} •
                {{ deck.user_info?.nickname || "未知" }}
              </p>
              <p class="text-xs text-gray-500 line-clamp-1">
                {{ deck.desc || "無說明" }}
              </p>
            </div>

            <!-- 時間 -->
            <div class="text-xs text-gray-500 ml-2 flex-shrink-0">
              {{ formatTime(deck.create_time) }}
            </div>
          </div>
        </div>

        <!-- Pagination -->
        <div
          v-if="hasCardData && totalCards > pageSize"
          class="flex justify-center mt-8"
        >
          <div class="flex items-center gap-2">
            <button
              @click="changePage(currentPage - 1)"
              :disabled="currentPage <= 1"
              class="px-4 py-2 bg-slate-700 hover:bg-slate-600 text-white rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              上一頁
            </button>
            <div class="flex gap-1">
              <button
                v-for="page in paginationPages"
                :key="page"
                @click="changePage(page)"
                :class="[
                  'w-10 h-10 rounded-lg font-medium transition-colors duration-200',
                  currentPage === page
                    ? 'bg-blue-600 text-white'
                    : 'bg-slate-700 text-gray-300 hover:bg-slate-600',
                ]"
              >
                {{ page }}
              </button>
            </div>
            <button
              @click="changePage(currentPage + 1)"
              :disabled="currentPage >= totalPages"
              class="px-4 py-2 bg-slate-700 hover:bg-slate-600 text-white rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              下一頁
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Floating Action Button -->
    <div class="fixed bottom-20 right-4 md:bottom-8 md:right-8">
      <button
        @click="navigateToAddCardGroup"
        class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white p-3 md:p-4 rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 flex items-center gap-2"
      >
        <Plus class="w-5 h-5 md:w-6 md:h-6" />
        <span class="font-medium text-sm md:text-base hidden sm:inline"
          >新建牌組</span
        >
      </button>
    </div>

    <!-- 手機版牌組詳情彈窗 -->
    <div v-if="showMobileDetailModal" class="fixed inset-0 z-50 md:hidden">
      <!-- 背景遮罩 -->
      <div
        class="absolute inset-0 bg-black/50 backdrop-blur-sm"
        @click="closeMobileDetail"
      ></div>

      <!-- 彈窗內容 -->
      <div
        class="absolute bottom-24 left-0 right-0 bg-slate-800 rounded-t-xl border-t border-slate-700 p-4 max-h-[70vh] overflow-y-auto"
      >
        <!-- 牌組圖片和標題 -->
        <div class="flex gap-3 mb-4">
          <div
            class="w-16 h-20 flex-shrink-0 rounded overflow-hidden bg-slate-700"
          >
            <img
              :src="selectedDeck?.image || '/api/placeholder/64/80'"
              :alt="selectedDeck?.title"
              class="w-full h-full object-cover"
            />
          </div>
          <div class="flex-1 min-w-0">
            <h3 class="font-semibold text-white text-base mb-2">
              {{ selectedDeck?.title }}
            </h3>
            <div class="flex items-center gap-2 mb-1">
              <span
                :class="[
                  'px-2 py-1 rounded text-xs font-medium border',
                  getTypeStyle(selectedDeck?.is_identify),
                ]"
              >
                {{ getIdentifyText(selectedDeck?.is_identify) }}
              </span>
              <span class="text-xs text-gray-400">{{
                selectedDeck?.env_title || "無"
              }}</span>
            </div>
            <div class="text-xs text-gray-500">
              作者：{{ selectedDeck?.user_info?.nickname || "未知" }} •
              {{ formatTime(selectedDeck?.create_time) }}
            </div>
          </div>
        </div>

        <!-- 牌組說明 -->
        <div class="mb-4">
          <h4 class="text-sm font-medium text-gray-300 mb-2">牌組說明</h4>
          <p class="text-sm text-gray-400 leading-relaxed">
            {{ selectedDeck?.desc || "暫無說明" }}
          </p>
        </div>

        <!-- 操作按鈕 -->
        <div class="flex gap-2">
          <button
            @click="navigateFromMobileDetail"
            class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center gap-2"
          >
            <Eye class="w-4 h-4" />
            查看牌組
          </button>
          <button
            @click="closeMobileDetail"
            class="bg-slate-700 hover:bg-slate-600 text-white px-4 py-3 rounded-lg transition-colors duration-200"
          >
            關閉
          </button>
        </div>
      </div>
    </div>

    <!-- 登入彈窗 -->
    <ClientOnly>
      <LoginDialog v-model="showLoginDialog" @close="showLoginDialog = false" @login-success="handleLoginSuccess" />
    </ClientOnly>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useAuthStore } from "~/stores/auth";
import LoginDialog from "~/components/LoginDialog.vue";
import {
  ArrowLeft,
  Search,
  Grid,
  List,
  Eye,
  Trash2,
  Plus,
  FileText,
} from "lucide-vue-next";
import {
  getGameList,
  getGoodsGroupEnvList,
  getMyCardgroupList,
} from "~/composables/useApi";

// Router
const router = useRouter();

// 登入彈窗狀態
const showLoginDialog = ref(false);

// 初始化 store 實例（在客戶端）
let authStore = null;

// 客戶端檢查用戶登入狀態
if (process.client) {
  authStore = useAuthStore();
  if (!authStore.isAuthenticated) {
    showLoginDialog.value = true;
  }
}

// 使用 SSR 數據獲取遊戲列表
const { data: gameListData } = await useFetch("/api/game/list", {
  method: "POST",
  body: {},
  default: () => ({ code: 500, data: [] }),
  // 啟用 SSR 以提高首屏載入速度和 SEO
});

// 基本狀態
const isLoading = ref(false);
const selectedGameId = ref(0);
const selectedEnvId = ref(0);
const isIdentify = ref(0); // 新增：分類狀態 (0:全部, 1:一般, 2:比賽)
const keyWord = ref("");
const isGridView = ref(true);
const currentPage = ref(1);
const pageSize = ref(10);
const totalCards = ref(0);

// 手機版詳情彈窗狀態
const showMobileDetailModal = ref(false);
const selectedDeck = ref(null);

// 使用 computed 處理遊戲列表數據
const gameList = computed(() => {
  const data = gameListData.value;
  if (data?.code === 200 && data?.data) {
    return data.data;
  }
  return [];
});

// 資料列表
const envList = ref([]);
const groupData = ref([]);

// 計算屬性
const hasCardData = computed(() => groupData.value.length > 0);

const totalPages = computed(() => Math.ceil(totalCards.value / pageSize.value));

const paginationPages = computed(() => {
  const pages = [];
  const maxPages = 5;
  let start = Math.max(1, currentPage.value - Math.floor(maxPages / 2));
  let end = Math.min(totalPages.value, start + maxPages - 1);

  if (end - start < maxPages - 1) {
    start = Math.max(1, end - maxPages + 1);
  }

  for (let i = start; i <= end; i++) {
    pages.push(i);
  }

  return pages;
});

// 格式化時間
const formatTime = (timestamp) => {
  if (!timestamp || isNaN(Number(timestamp))) return "未知";
  const date = new Date(Number(timestamp) * 1000);
  if (isNaN(date.getTime())) return "未知";
  return date
    .toLocaleDateString("zh-TW", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
    })
    .replace(/\//g, "-");
};

// 獲取類型標籤文字
const getIdentifyText = (isIdentify) => {
  switch (isIdentify) {
    case 1:
      return "一般";
    case 2:
      return "上位";
    case 3:
      return "審核中";
    case 4:
      return "無限制";
    case 5:
      return "駁回";
    default:
      return "";
  }
};

// 獲取類型標籤樣式
const getTypeStyle = (isIdentify) => {
  switch (isIdentify) {
    case 1:
      return "bg-blue-500/20 text-blue-400 border-blue-500/30";
    case 2:
      return "bg-orange-500/20 text-orange-400 border-orange-500/30";
    case 3:
      return "bg-gray-500/20 text-gray-400 border-gray-500/30";
    case 4:
      return "bg-purple-500/20 text-purple-400 border-purple-500/30";
    case 5:
      return "bg-red-500/20 text-red-400 border-red-500/30";
    default:
      return "bg-gray-500/20 text-gray-400 border-gray-500/30";
  }
};

// 獲取類型背景顏色（手機版列表用）
const getTypeBgColor = (isIdentify) => {
  switch (isIdentify) {
    case 1:
      return "bg-blue-500";
    case 2:
      return "bg-orange-500";
    case 3:
      return "bg-gray-500";
    case 4:
      return "bg-purple-500";
    case 5:
      return "bg-red-500";
    default:
      return "bg-gray-500";
  }
};

// 方法
const goBack = () => {
  router.back();
};

const toggleViewMode = () => {
  isGridView.value = !isGridView.value;
};

// 載入遊戲列表
const loadGameList = async () => {
  try {
    const response = await getGameList(0, null);
    if (response && response.code === 200) {
      gameList.value = response.data.filter(
        (game) =>
          game.game_id !== undefined &&
          game.game_id !== null &&
          game.game_id !== 4 &&
          game.game_id !== 99 &&
          game.game_id !== 10
      );
      gameList.value.unshift({ game_id: 0, title: "全部" });
      if (gameList.value.length > 0) {
        selectedGameId.value = gameList.value[0].game_id;
        await loadEnvList();
      }
    }
  } catch (error) {
    console.error("載入遊戲列表失敗:", error);
  }
};

// 載入環境列表
const loadEnvList = async () => {
  if (selectedGameId.value === 0) {
    envList.value = [];
    selectedEnvId.value = 0;
    await loadCardListData();
    isLoading.value = false;
  } else {
    try {
      const response = await getGoodsGroupEnvList(selectedGameId.value);
      if (response && response.code === 200) {
        envList.value = response.data || [];
        if (envList.value.length > 0) {
          selectedEnvId.value = envList.value[0].env_id;
        }
        await loadCardListData();
        isLoading.value = false;
      }
    } catch (error) {
      console.error("載入環境列表失敗:", error);
    }
  }
};

// 載入牌組列表數據
const loadCardListData = async () => {
  isLoading.value = true;
  try {
    const params = {
      page: currentPage.value,
      key_word: keyWord.value,
    };

    if (selectedGameId.value !== 0) {
      params.game_id = selectedGameId.value;
      params.env_id = selectedEnvId.value;
    }

    if (isIdentify.value !== 0) {
      params.is_identify = isIdentify.value;
    }

    const response = await getMyCardgroupList(params);
    if (response && response.code === 200) {
      groupData.value = response.data?.list || [];
      totalCards.value = response.data?.total || 0;
    } else {
      groupData.value = [];
      totalCards.value = 0;
    }
  } catch (error) {
    console.error("載入牌組列表失敗:", error);
    groupData.value = [];
    totalCards.value = 0;
  } finally {
    isLoading.value = false;
  }
};

// 事件處理
const onGameChange = async () => {
  await loadEnvList();
};

const setSelectedEnv = async (envId) => {
  selectedEnvId.value = envId;
  currentPage.value = 1;
  await loadCardListData();
};

const setIdentifyFilter = async (identify) => {
  isIdentify.value = identify;
  currentPage.value = 1;
  await loadCardListData();
};

const onSearch = async () => {
  currentPage.value = 1;
  await loadCardListData();
};

const changePage = async (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
    await loadCardListData();
  }
};

const navigateToDeckDetail = (deck) => {
  router.push(`/Goodsgroup/${deck.group_id}`);
};

const navigateToAddCardGroup = () => {
  router.push({
    path: "/goodsgroup/add",
    query: {
      group_id: 0,
      is_identify: 1,
      game_id: 1,
    },
  });
};

const deleteDeck = (deck) => {
  // 實現刪除邏輯
};

// 手機版列表點擊處理
const handleListItemClick = (deck) => {
  // 桌面版直接跳轉
  navigateToDeckDetail(deck);
};

// 顯示手機版詳情
const showMobileDetail = (deck) => {
  selectedDeck.value = deck;
  showMobileDetailModal.value = true;
};

// 關閉手機版詳情
const closeMobileDetail = () => {
  showMobileDetailModal.value = false;
  selectedDeck.value = null;
};

// 從手機版詳情跳轉到牌組詳情
const navigateFromMobileDetail = () => {
  if (selectedDeck.value) {
    navigateToDeckDetail(selectedDeck.value);
    closeMobileDetail();
  }
};

// 登入成功處理
const handleLoginSuccess = async () => {
  showLoginDialog.value = false;
  // 登入成功後重新載入數據
  if (gameList.value.length > 0) {
    selectedGameId.value = gameList.value[0].game_id;
    await loadEnvList();
  }
};

// 客戶端初始化
onMounted(async () => {
  // SSR 數據已經在服務端獲取，這裡只需要客戶端特定的初始化
  console.log("牌組管理頁面已載入，遊戲數量:", gameList.value.length);
  
  // 如果用戶已登入，則載入數據
  if (process.client && authStore && authStore.isAuthenticated) {
    if (gameList.value.length > 0) {
      selectedGameId.value = gameList.value[0].game_id;
      await loadEnvList();
    }
  }
});
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
