import { createError } from 'h3';
import fetch from 'node-fetch';

// 代理獲取Google Cloud Storage圖片的API
export default defineEventHandler(async (event) => {
  try {
    // 從路徑參數中獲取文件名
    const filename = event.context.params?.filename;
    
    // 驗證文件名
    if (!filename) {
      throw createError({
        statusCode: 400,
        statusMessage: '未提供文件名'
      });
    }
    
    // 構築原始圖片URL
    const originalImageUrl = `https://storage.googleapis.com/iwantcard-upload/upload/goods/${filename}`;
    
    // 獲取圖片
    const response = await fetch(originalImageUrl);
    
    // 檢查響應
    if (!response.ok) {
      throw createError({
        statusCode: response.status,
        statusMessage: `無法獲取圖片: ${response.statusText}`
      });
    }
    
    // 獲取圖片數據 - 修正方法
    // 使用arrayBuffer來代替buffer方法
    const arrayBuffer = await response.arrayBuffer();
    const imageBuffer = Buffer.from(arrayBuffer);
    
    // 獲取圖片類型
    const contentType = response.headers.get('content-type') || 'image/jpeg';
    
    // 設置響應頭
    setResponseHeaders(event, {
      'Content-Type': contentType,
      'Cache-Control': 'public, max-age=86400' // 緩存一天
    });
    
    // 返回圖片數據
    return imageBuffer;
  } catch (error) {
    console.error('[代理圖片] 錯誤:', error);
    throw createError({
      statusCode: 500,
      statusMessage: `獲取圖片時發生錯誤: ${error.message}`
    });
  }
}); 