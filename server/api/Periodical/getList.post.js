// server/api/Periodical/getList.post.js
export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    
    // 从请求中获取头信息
    const headers = getHeaders(event);
    
    // 构建请求选项
    const requestOptions = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(headers.token ? { 'token': headers.token } : {})
      },
      body: JSON.stringify(body)
    };
    
    // 调用外部API
    const response = await fetch('https://www.iwantcard.tw/api/Periodical/getList', requestOptions);
    
    // 检查响应状态
    if (!response.ok) {
      throw new Error(`API responded with status: ${response.status}`);
    }
    
    // 解析并返回JSON响应
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('获取期数列表失败:', error);
    return {
      code: 500,
      msg: '获取期数列表失败',
      error: error.message
    };
  }
});

// 从请求中提取头信息的辅助函数
function getHeaders(event) {
  const headers = {};
  const reqHeaders = getRequestHeaders(event);
  
  // 复制相关头信息
  if (reqHeaders.token) {
    headers.token = reqHeaders.token;
  }
  
  return headers;
} 