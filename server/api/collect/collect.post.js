export default defineEventHandler(async (event) => {
    try {
        const body = await readBody(event);
        const headers = getHeaders(event);

        const requestOptions = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                ...(headers.token ? { 'token': headers.token } : {})
            },
            body: JSON.stringify(body)
        };

        const response = await fetch('https://www.iwantcard.tw/api/Collect/collect', requestOptions);

        if (!response.ok) {
            throw new Error(`API responded with status: ${response.status}`);
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error collecting/uncollecting:', error);
        return {
            code: 500,
            msg: error.message || 'Failed to collect/uncollect',
            data: null
        };
    }
}); 