export default defineEventHandler(async (event) => {
    try {
        const body = await readBody(event);
        const headers = getHeaders(event);
        const token = headers.token || event.req.headers['token'];

        if (!token) {
            throw createError({
                statusCode: 401,
                statusMessage: 'Unauthorized',
                message: '需要登入'
            });
        }

        // 构建请求参数
        const payload = new URLSearchParams();
        payload.append('user_id', body.user_id || '');
        payload.append('message_id', body.message_id || '');
        payload.append('title', body.title || '');
        payload.append('content', body.content || '');
        payload.append('game_id', body.game_id || '1');
        payload.append('category_id', body.category_id || '');
        payload.append('is_anonymous', body.is_anonymous || '0');
        payload.append('is_shop', body.is_shop || '0');
        payload.append('price', body.price || '0');
        payload.append('images', body.images || '');
        payload.append('video_url', body.video_url || '');

        // 发送请求到实际的 API
        const response = await $fetch('https://api.iwantcard.com.tw/Message/edit', {
            method: 'POST',
            body: payload,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'token': token
            }
        });

        return response;
    } catch (error) {
        console.error('編輯動態失敗:', error);
        throw createError({
            statusCode: error.statusCode || 500,
            statusMessage: error.statusMessage || 'Internal Server Error',
            message: error.message || '編輯動態失敗'
        });
    }
}); 