// server/api/message/list.js
export default defineEventHandler(async (event) => {
    // 只允許 POST 請求
    if (event.method !== 'POST') {
        throw createError({
            statusCode: 405,
            statusMessage: 'Method Not Allowed',
            message: 'Only POST method is allowed'
        });
    }

    try {
        const body = await readBody(event);
        const headers = getHeaders(event);
        
        // 从请求头中获取 token
        const token = headers.token || event.req.headers['token'];

        const requestOptions = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                ...(token ? { 'token': token } : {})
            },
            body: JSON.stringify(body)
        };

        const response = await fetch('https://www.iwantcard.tw/api/Message/getList', requestOptions);

        if (!response.ok) {
            throw new Error(`API responded with status: ${response.status}`);
        }

        const data = await response.json();
        
        return data;
    } catch (error) {
        console.error('Error fetching message list:', error);
        return {
            code: 500,
            msg: error.message || 'Failed to fetch message list',
            data: null
        };
    }
});