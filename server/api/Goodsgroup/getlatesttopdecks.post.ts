export default defineEventHandler(async (event) => {
  try {
    // 獲取請求體
    const body = await readBody(event);
    
    // 設置請求選項
    const requestOptions = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(body)
    };

    // 如果有令牌，加入到請求標頭
    if (event.node.req.headers.token) {
      requestOptions.headers.token = event.node.req.headers.token;
    }

    // 調用真實API
    const response = await fetch('https://www.iwantcard.tw/api/Goodsgroup/getlatesttopdecks', requestOptions);
    const data = await response.json();
    
    return data;
  } catch (error) {
    console.error('獲取上位牌組失敗:', error);
    
    // 返回錯誤響應
    return {
      code: 500,
      msg: '獲取上位牌組失敗',
      data: null
    };
  }
}); 