// server/api/Goodsgroup/getMyList.post.js

export default defineEventHandler(async (event) => {
    try {
        // 解析請求體 (FormData)
        const body = await readMultipartFormData(event);
        const headers = getHeaders(event);

        // 檢查用戶認證
        if (!headers.token) {
            return {
                code: 401,
                msg: '未登入或 token 無效',
                data: null
            };
        }

        // 將 FormData 轉換為適合發送的格式
        const formData = new FormData();
        
        if (body) {
            for (const field of body) {
                formData.append(field.name, field.data.toString());
            }
        }

        const requestOptions = {
            method: 'POST',
            headers: {
                // 注意：使用 FormData 時不要設置 Content-Type，讓瀏覽器自動設置
                ...(headers.token ? { 'token': headers.token } : {})
            },
            body: formData
        };

        const response = await fetch('https://www.iwantcard.tw/api/Goodsgroup/getMyList', requestOptions);

        if (!response.ok) {
            throw new Error(`API responded with status: ${response.status}`);
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error fetching my cardgroup list:', error);
        return {
            code: 500,
            msg: error.message || 'Failed to fetch my cardgroup list',
            data: null
        };
    }
});