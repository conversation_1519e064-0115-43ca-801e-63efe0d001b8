// server/api/goodsgroup/type-list.post.js

import { defineEventHandler, createError } from 'h3'

export default defineEventHandler(async (_event) => {
  try {
    const formData = new FormData()

    const response = await fetch('https://www.iwantcard.tw/api/Goodsgrouptype/getList', {
      method: 'POST',
      body: formData
    })

    if (!response.ok) {
      throw createError({
        statusCode: response.status,
        statusMessage: `API responded with status: ${response.status}`
      })
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error('獲取卡組類型列表失敗:', error)
    throw createError({
      statusCode: 500,
      statusMessage: error instanceof Error ? error.message : '獲取卡組類型列表失敗'
    })
  }
})
