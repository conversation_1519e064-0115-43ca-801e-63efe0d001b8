// server/api/goodsgroup/update.post.js

export default defineEventHandler(async (event) => {
  try {
    const headers = getHeaders(event)
    const token = headers.token

    if (!token) {
      return {
        code: 401,
        msg: '未授權，請先登入',
        data: null
      }
    }

    const body = await readBody(event)
    const { group_id, is_identify, title, desc, env_id, image, game_id, goods_info } = body

    if (!title || !env_id || !image) {
      return {
        code: 400,
        msg: '缺少必要參數: title, env_id, image',
        data: null
      }
    }

    // 使用正確的 API URL
    const apiUrl = 'https://www.iwantcard.tw/api'
    
    // 準備請求資料(使用URL編碼表單格式)
    const params = new URLSearchParams()
    params.append('group_id', (group_id || 0).toString())
    params.append('is_identify', (is_identify || 1).toString())
    params.append('title', title)
    params.append('desc', desc || '')
    params.append('env_id', env_id.toString())
    params.append('image', image)
    params.append('game_id', (game_id || 1).toString())
    params.append('goods_info', goods_info || '')

    // 使用fetch發送請求到實際的API伺服器
    const response = await fetch(`${apiUrl}/Goodsgroup/update`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'token': token
      },
      body: params.toString()
    })

    if (!response.ok) {
      throw new Error(`API responded with status: ${response.status}`)
    }

    // 解析JSON回應
    const result = await response.json()
    
    // 直接返回API的回應結果
    return result
  } catch (error) {
    console.error('更新牌組失敗:', error)
    return {
      code: 500,
      msg: error.message || '更新牌組失敗',
      data: null
    }
  }
})