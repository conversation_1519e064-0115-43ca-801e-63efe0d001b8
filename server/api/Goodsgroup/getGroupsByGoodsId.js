export default defineEventHandler(async (event) => {
    try {
        const body = await readBody(event);
        
        // 從body中獲取參數
        const { goods_id, page, page_nums } = body;
        
        // 創建新的參數對象，不套嵌
        const requestBody = JSON.stringify({
            goods_id,
            page,
            page_nums
        });
        
        // 設置請求選項
        const requestOptions = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: requestBody
        };

        // 如果有令牌，加入到請求標頭
        if (event.node.req.headers.token) {
            requestOptions.headers.token = event.node.req.headers.token;
        }

        // 從原始API獲取數據
        const response = await fetch('https://www.iwantcard.tw/api/Goodsgroup/getGroupsByGoodsId', requestOptions);
        const data = await response.json();
        
        // 處理響應格式，確保始終為同一格式
        let result;
        
        // 原始API返回格式為 {total, total_page, list}
        if (data && data.list && Array.isArray(data.list)) {
            result = {
                code: 200,
                msg: '成功',
                data: {
                    list: data.list,
                    total_page: data.total_page || 1
                }
            };
        } else if (data && data.code === 200) {
            // 如果已經是標準格式
            if (data.data && Array.isArray(data.data.list)) {
                result = data;
            } else if (Array.isArray(data.data)) {
                result = {
                    code: 200,
                    msg: '成功',
                    data: {
                        list: data.data,
                        total_page: 1
                    }
                };
            } else {
                result = {
                    code: 200,
                    msg: '無相關牌組',
                    data: {
                        list: [],
                        total_page: 1
                    }
                };
            }
        } else {
            // 沒有找到有效數據
            result = {
                code: 200,
                msg: '無相關牌組',
                data: {
                    list: [],
                    total_page: 1
                }
            };
        }
        
        return result;
    } catch (error) {
        console.error('獲取包含該卡的牌組列表失敗:', error);
        
        return {
            code: 500,
            msg: '獲取包含該卡的牌組列表失敗',
            data: {
                list: [],
                total_page: 1
            }
        };
    }
}); 