export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    
    // 檢查必要參數
    if (!body.game_id || !body.title || !body.env_id || !body.type_id) {
      return {
        code: 200,
        msg: "缺少必要參數",
        data: null
      };
    }
        
    // 設置請求選項
    const requestOptions = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: new URLSearchParams(body)
    };
    
    // 如果有令牌，加入到請求標頭
    if (event.node.req.headers.token) {
      requestOptions.headers.token = event.node.req.headers.token;
    }
    
    // 從原始API獲取數據
    const response = await fetch('https://www.iwantcard.tw/api/Goodsgroup/getdeckdetail', requestOptions);
    const data = await response.json();
    
    return data;
  } catch (error) {
    console.error("獲取牌組構築詳情失敗:", error);
    return {
      code: 500,
      msg: "伺服器錯誤",
      data: null
    };
  }
}); 