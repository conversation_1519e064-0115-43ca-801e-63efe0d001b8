// server/api/goodsgroup/info.post.js

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const groupId = body.group_id

    if (!groupId) {
      return {
        code: 400,
        msg: '缺少必要參數: group_id',
        data: null
      }
    }

    // 使用正確的 API URL
    const apiUrl = 'https://www.iwantcard.tw/api'
    
    // 準備請求資料(使用URL編碼表單格式)
    const params = new URLSearchParams()
    params.append('group_id', groupId.toString())

    // 使用fetch發送請求到實際的API伺服器
    const response = await fetch(`${apiUrl}/Goodsgroup/getInfo`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: params.toString()
    })

    if (!response.ok) {
      throw new Error(`API responded with status: ${response.status}`)
    }

    // 解析JSON回應
    const result = await response.json()
    
    // 直接返回API的回應結果
    return result
  } catch (error) {
    console.error('獲取卡組詳情失敗:', error)
    return {
      code: 500,
      msg: error.message || '獲取卡組詳情失敗',
      data: null
    }
  }
})