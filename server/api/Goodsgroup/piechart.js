export default defineEventHandler(async (event) => {
    try {
        const body = await readBody(event);
        const headers = getHeaders(event);

        // 更严格的参数检查
        if (!body.game_id || !body.env_id) {
            console.warn('Missing required parameters for piechart API:', JSON.stringify(body));
            return {
                code: 200, // 返回200但带有错误信息
                msg: 'Missing required parameters',
                data: [] // 返回空数组而不是null
            };
        }

        const requestOptions = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                ...(headers.token ? { 'token': headers.token } : {})
            },
            body: new URLSearchParams(body)
        };

        let response;
        try {
            response = await fetch('https://www.iwantcard.tw/api/Goodsgroup/getPieChartData', requestOptions);
        } catch (fetchError) {
            console.error('Network error when fetching pie chart data:', fetchError);
            return {
                code: 200, // 返回200但带有错误信息
                msg: 'Network error when connecting to external API',
                data: [] // 返回空数组而不是null
            };
        }

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`PieChart API responded with status: ${response.status}, details: ${errorText}`);
            
            // 返回空数据而不是500错误
            return {
                code: 200,
                msg: `External API error: ${response.status}`,
                data: [] // 返回空数组而不是null
            };
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error in piechart API:', error);
        
        // 即使发生错误也返回200和空数据
        return {
            code: 200,
            msg: error.message || 'Failed to fetch pie chart data',
            data: [] // 返回空数组而不是null
        };
    }
});
