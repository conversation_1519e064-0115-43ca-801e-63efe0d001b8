export default defineEventHandler(async (event) => {
    try {
        // 讀取請求體
        const body = await readBody(event);

        // 從請求中獲取頭信息
        const headers = getHeaders(event);

        // 構築請求選項
        const requestOptions = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                ...(headers.token ? { 'token': headers.token } : {})
            },
            body: JSON.stringify(body)
        };

        // 調用外部API
        const response = await fetch('https://www.iwantcard.tw/api/Goodsgroup/getList', requestOptions);

        // 檢查響應狀態
        if (!response.ok) {
            throw new Error(`API responded with status: ${response.status}`);
        }

        // 解析並返回JSON響應
        const data = await response.json();

        return data;
    } catch (error) {
        console.error('Error fetching card group list:', error);

        // 創建和返回錯誤
        return {
            code: 500,
            msg: error.message || 'Failed to fetch card group list',
            data: null
        };
    }
});

// 從請求中提取頭信息的輔助函數
function getHeaders(event) {
    const headers = {};
    const reqHeaders = getRequestHeaders(event);

    // 複製相關頭信息
    if (reqHeaders.token) {
        headers.token = reqHeaders.token;
    }

    return headers;
}
