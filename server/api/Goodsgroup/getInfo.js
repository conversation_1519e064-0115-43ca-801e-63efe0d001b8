export default defineEventHandler(async (event) => {
    try {
        const body = await readBody(event);
        
        // 設置請求選項
        const requestOptions = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(body)
        };

        // 如果有令牌，加入到請求標頭
        if (event.node.req.headers.token) {
            requestOptions.headers.token = event.node.req.headers.token;
        }

        // 從原始API獲取數據
        const response = await fetch('https://www.iwantcard.tw/api/Goodsgroup/getInfo', requestOptions);
        const data = await response.json();
        
        return data;
    } catch (error) {
        console.error('獲取牌組詳情失敗:', error);
        
        return {
            code: 500,
            msg: '獲取牌組詳情失敗',
            data: null
        };
    }
}); 