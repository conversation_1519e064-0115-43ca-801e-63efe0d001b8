export default defineEventHandler(async (event) => {
    try {
        const body = await readMultipartFormData(event);
        const headers = getHeaders(event);
        const token = headers.token || event.req.headers['token'];

        if (!token) {
            throw createError({
                statusCode: 401,
                statusMessage: 'Unauthorized',
                message: '需要登入'
            });
        }

        if (!body || !body.length) {
            throw createError({
                statusCode: 400,
                statusMessage: 'Bad Request',
                message: '未找到上傳的文件'
            });
        }

        const file = body[0];
        if (!file.type || !file.type.startsWith('image/')) {
            throw createError({
                statusCode: 400,
                statusMessage: 'Bad Request',
                message: '只允許上傳圖片文件'
            });
        }

        // 构建 FormData
        const formData = new FormData();
        formData.append('file', new Blob([file.data], { type: file.type }), file.filename);

        // 发送请求到实际的 API
        const response = await $fetch('https://api.iwantcard.com.tw/upload/image', {
            method: 'POST',
            body: formData,
            headers: {
                'token': token
            }
        });

        return response;
    } catch (error) {
        console.error('上傳圖片失敗:', error);
        throw createError({
            statusCode: error.statusCode || 500,
            statusMessage: error.statusMessage || 'Internal Server Error',
            message: error.message || '上傳圖片失敗'
        });
    }
}); 