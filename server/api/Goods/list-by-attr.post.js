// server/api/goods/list-by-attr.post.js

import { defineEventHandler, readBody, createError } from 'h3'

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const { game_id, key_word, params, page, page_nums, is_identify } = body

    if (!game_id) {
      throw createError({
        statusCode: 400,
        statusMessage: '缺少必要參數: game_id'
      })
    }

    const formData = new FormData()
    formData.append('game_id', game_id)
    formData.append('key_word', key_word || '')
    formData.append('params', params || '')
    formData.append('page', page || 1)
    formData.append('page_nums', page_nums || 50)
    formData.append('is_identify', is_identify || 1)

    const response = await fetch('https://www.iwantcard.tw/api/Goods/getListByAttr', {
      method: 'POST',
      body: formData
    })

    if (!response.ok) {
      throw createError({
        statusCode: response.status,
        statusMessage: `API responded with status: ${response.status}`
      })
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error('根據屬性獲取卡片列表失敗:', error)
    throw createError({
      statusCode: 500,
      statusMessage: error instanceof Error ? error.message : '根據屬性獲取卡片列表失敗'
    })
  }
})
