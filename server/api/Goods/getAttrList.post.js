// server/api/Goods/getAttrList.post.js

import { defineEventHandler, readBody, createError } from 'h3'

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const { game_id } = body

    // 驗證參數
    if (!game_id) {
      throw createError({
        statusCode: 400,
        statusMessage: 'game_id is required'
      })
    }

    // 構築 FormData
    const formData = new FormData()
    formData.append('game_id', game_id)

    // 從 headers 中獲取 token
    const headers = getHeaders(event)
    const token = headers.token || headers.authorization?.replace('Bearer ', '')

    const requestHeaders = {
      'Content-Type': 'application/x-www-form-urlencoded'
    }

    if (token) {
      requestHeaders['token'] = token
    }

    // 調用真實 API
    const response = await fetch('https://www.iwantcard.tw/api/Goods/getAttrList', {
      method: 'POST',
      headers: requestHeaders,
      body: `game_id=${game_id}`
    })

    if (!response.ok) {
      throw createError({
        statusCode: response.status,
        statusMessage: `API responded with status: ${response.status}`
      })
    }

    const data = await response.json()
    return data

  } catch (error) {
    console.error('獲取屬性列表失敗:', error)
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: error instanceof Error ? error.message : '獲取屬性列表失敗'
    })
  }
})