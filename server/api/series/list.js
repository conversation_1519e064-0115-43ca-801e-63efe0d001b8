// server/api/series/list.js
export default defineEventHandler(async (event) => {
    try {
        // 讀取請求體
        const body = await readBody(event);
        
        // 記錄請求參數用於調試
        // console.log('Series list API 請求參數:', JSON.stringify(body, null, 2));

        // 從請求中獲取頭信息
        const headers = getHeaders(event);

        // 構築請求選項
        const requestOptions = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                ...(headers.token ? { 'token': headers.token } : {})
            },
            body: JSON.stringify(body)
        };

        // 根據是否有periodical_id參數決定使用哪個API端點
        const apiUrl = body.periodical_id 
            ? 'https://www.iwantcard.tw/api/Series/getList' 
            : 'https://www.iwantcard.tw/api/Series/getList';

        // console.log('正在調用外部 API:', apiUrl);
        // console.log('請求選項:', JSON.stringify({
        //     method: requestOptions.method,
        //     headers: requestOptions.headers,
        //     bodySize: requestOptions.body.length
        // }, null, 2));

        // 調用外部API
        const response = await fetch(apiUrl, requestOptions);

        // console.log('外部 API 響應狀態:', response.status, response.statusText);

        // 檢查響應狀態
        if (!response.ok) {
            // 嘗試讀取錯誤響應內容
            let errorText = '';
            try {
                errorText = await response.text();
                // console.log('外部 API 錯誤響應內容:', errorText);
            } catch (e) {
                // console.log('無法讀取錯誤響應內容');
            }
            
            throw new Error(`外部 API 響應錯誤 - 狀態: ${response.status} ${response.statusText}${errorText ? ', 內容: ' + errorText : ''}`);
        }

        // 解析並返回JSON響應
        const data = await response.json();
        // console.log('外部 API 響應成功，數據類型:', typeof data, '數據鍵:', Object.keys(data || {}));
        
        return data;
    } catch (error) {
        console.error('Series list API 錯誤詳情:');
        console.error('- 錯誤類型:', error.name);
        console.error('- 錯誤信息:', error.message);
        console.error('- 錯誤堆疊:', error.stack);

        // 創建和返回錯誤
        return {
            code: 500,
            msg: error.message || 'Failed to fetch series list',
            data: null,
            debug: {
                errorType: error.name,
                timestamp: new Date().toISOString()
            }
        };
    }
});

// 從請求中提取頭信息的輔助函數
function getHeaders(event) {
    const headers = {};
    const reqHeaders = getRequestHeaders(event);

    // 複製相關頭信息
    if (reqHeaders.token) {
        headers.token = reqHeaders.token;
    }

    return headers;
}
