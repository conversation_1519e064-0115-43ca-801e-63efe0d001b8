export default defineEventHandler(async (event) => {
  try {
    // 獲取請求體
    const body = await readBody(event);
    
    // 設置請求選項
    const requestOptions = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(body)
    };

    // 如果有令牌，添加到請求標頭
    if (event.node.req.headers.token) {
      requestOptions.headers.token = event.node.req.headers.token;
    }

    // 調用真實API
    const response = await fetch('https://www.iwantcard.tw/api/Series/getInfo', requestOptions);
    
    // 檢查HTTP狀態
    if (!response.ok) {
      return {
        code: response.status,
        msg: `API請求失敗: ${response.statusText}`,
        data: null
      };
    }
    
    const data = await response.json();
    if (data.code !== 200) {
      return {
        code: data.code,
        msg: data.msg,
        data: null
      };
    }
    
    return data;
  } catch (error) {
    console.error('獲取系列詳情失敗:', error.message, error.stack);
    
    // 返回錯誤響應
    return {
      code: 500,
      msg: `獲取系列詳情失敗: ${error.message}`,
      data: null
    };
  }
});
