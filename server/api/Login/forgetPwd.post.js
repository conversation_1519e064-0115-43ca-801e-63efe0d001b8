export default defineEventHandler(async (event) => {
  try {
    // 取得請求資料
    const body = await readBody(event);
    const { pwd, key, code, email } = body;

    // 使用外部API服務處理忘記密碼
    const apiUrl = process.env.NUXT_PUBLIC_API_BASE_URL || 'https://api.yugioh-deckbuilder.com';
    
    // 準備請求資料(使用URL編碼表單格式)
    const params = new URLSearchParams();
    params.append('pwd', pwd || '');
    params.append('key', key || '');
    params.append('code', code || '');
    params.append('email', email || '');

    const response = await fetch(`${apiUrl}/Login/forgetPwd`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: params
    });

    // 解析JSON回應
    const result = await response.json();
    
    // 直接返回API的回應結果
    return result;
  } catch (error) {
    console.error('忘記密碼處理錯誤:', error);
    return {
      code: 500,
      msg: '伺服器內部錯誤',
      data: null
    };
  }
}); 