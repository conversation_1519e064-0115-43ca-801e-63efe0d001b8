export default defineEventHandler(async (event) => {
  try {
    // 取得請求資料
    const body = await readBody(event);
    const { key, tel } = body;

    // 使用外部API服務處理發送簡訊驗證碼
    const apiUrl = process.env.NUXT_PUBLIC_API_BASE_URL || 'https://api.yugioh-deckbuilder.com';
    
    // 準備請求資料(使用URL編碼表單格式)
    const params = new URLSearchParams();
    params.append('key', key || '');
    params.append('tel', tel || '');

    const response = await fetch(`${apiUrl}/Login/sendSms`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: params
    });

    // 解析JSON回應
    const result = await response.json();
    
    // 直接返回API的回應結果
    return result;
  } catch (error) {
    console.error('發送簡訊驗證碼處理錯誤:', error);
    return {
      code: 500,
      msg: '伺服器內部錯誤',
      data: null
    };
  }
}); 