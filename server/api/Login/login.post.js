// server/api/Login/login.post.js

export default defineEventHandler(async (event) => {
  try {
    // 取得請求資料
    const body = await readBody(event);
    const { email, pwd, type } = body;

    // 使用正確的 API URL (與您的 env-list.js 一致)
    const apiUrl = 'https://www.iwantcard.tw/api';
    
    // 準備請求資料(使用URL編碼表單格式)
    const params = new URLSearchParams();
    params.append('type', type || '4');
    params.append('email', email || '');
    params.append('pwd', pwd || '');

    // 使用fetch發送請求到實際的API伺服器
    const response = await fetch(`${apiUrl}/Login/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: params.toString()
    });

    if (!response.ok) {
      throw new Error(`API responded with status: ${response.status}`);
    }

    // 解析JSON回應
    const result = await response.json();
    
    // 直接返回API的回應結果
    return result;
  } catch (error) {
    console.error('登入處理錯誤:', error);
    return {
      code: 500,
      msg: error.message || '伺服器內部錯誤',
      data: null
    };
  }
});