export default defineEventHandler(async (event) => {
  try {
    // 取得請求標頭中的token
    const headers = getRequestHeaders(event);
    const token = headers.token || '';

    // 使用外部API服務處理登出
    const apiUrl = process.env.NUXT_PUBLIC_API_BASE_URL || 'https://api.yugioh-deckbuilder.com';
    
    // 呼叫實際的登出API
    const response = await fetch(`${apiUrl}/Login/logout`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'token': token
      },
      body: new URLSearchParams()
    });

    // 解析JSON回應
    const result = await response.json();
    
    // 直接返回API的回應結果
    return result;
  } catch (error) {
    console.error('登出處理錯誤:', error);
    return {
      code: 500,
      msg: '伺服器內部錯誤',
      data: null
    };
  }
}); 