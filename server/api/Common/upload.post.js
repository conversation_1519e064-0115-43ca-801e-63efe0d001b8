export default defineEventHandler(async (event) => {
    try {
        const body = await readMultipartFormData(event);
        const headers = getHeaders(event);
        const token = headers.token || event.req.headers['token'];

        if (!token) {
            throw createError({
                statusCode: 401,
                statusMessage: 'Unauthorized',
                message: '需要登入'
            });
        }

        if (!body || !body.length) {
            throw createError({
                statusCode: 400,
                statusMessage: 'Bad Request',
                message: '未找到上傳的文件'
            });
        }

        // 构建 FormData
        const formData = new FormData();
        
        // 遍历所有字段
        for (const part of body) {
            if (part.name === 'img') {
                formData.append('img', new Blob([part.data], { type: part.type }), part.filename);
            } else if (part.name === 'dir') {
                formData.append('dir', part.data.toString());
            }
        }

        // 发送请求到实际的 API
        const response = await $fetch('https://api.iwantcard.com.tw/Common/upload', {
            method: 'POST',
            body: formData,
            headers: {
                'token': token
            }
        });

        return response;
    } catch (error) {
        console.error('上傳圖片失敗:', error);
        throw createError({
            statusCode: error.statusCode || 500,
            statusMessage: error.statusMessage || 'Internal Server Error',
            message: error.message || '上傳圖片失敗'
        });
    }
}); 