export default defineEventHandler(async (event) => {
    try {
        const body = await readBody(event);
        const headers = getHeaders(event);
        const token = headers.token || event.req.headers['token'];

        if (!token) {
            throw createError({
                statusCode: 401,
                statusMessage: 'Unauthorized',
                message: '需要登入'
            });
        }

        // 构建请求参数
        const payload = new URLSearchParams();
        payload.append('type', body.type || '1');
        payload.append('value_id', body.value_id || '');
        payload.append('comment', body.comment || '');

        // 发送请求到实际的 API
        const response = await $fetch('https://api.iwantcard.com.tw/Comment/addComment', {
            method: 'POST',
            body: payload,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'token': token
            }
        });

        return response;
    } catch (error) {
        console.error('發表評論失敗:', error);
        throw createError({
            statusCode: error.statusCode || 500,
            statusMessage: error.statusMessage || 'Internal Server Error',
            message: error.message || '發表評論失敗'
        });
    }
}); 