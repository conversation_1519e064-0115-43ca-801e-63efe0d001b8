export default defineEventHandler(async (event) => {
    try {
        const body = await readBody(event);
        const headers = getHeaders(event);
        const token = headers.token || event.req.headers['token'];

        if (!token) {
            throw createError({
                statusCode: 401,
                statusMessage: 'Unauthorized',
                message: '需要登入'
            });
        }

        // 构建请求参数
        const payload = new URLSearchParams();
        payload.append('comment_id', body.comment_id || '');

        // 发送请求到实际的 API
        const response = await $fetch('https://api.iwantcard.com.tw/Comment/delete', {
            method: 'POST',
            body: payload,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'token': token
            }
        });

        return response;
    } catch (error) {
        console.error('刪除評論失敗:', error);
        throw createError({
            statusCode: error.statusCode || 500,
            statusMessage: error.statusMessage || 'Internal Server Error',
            message: error.message || '刪除評論失敗'
        });
    }
}); 