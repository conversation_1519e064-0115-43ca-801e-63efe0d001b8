import { defineStore } from 'pinia';
import { useI18n } from 'vue-i18n';

export const useDeckStore = defineStore('deck', {
  state: () => ({
    cards: [],
  }),
  getters: {
    monsterCount: (state) => {
      const { t } = useI18n();
      return state.cards.filter(c => c.type === t('card.monster')).length;
    },
    spellCount: (state) => {
      const { t } = useI18n();
      return state.cards.filter(c => c.type === t('card.spell')).length;
    },
    trapCount: (state) => {
      const { t } = useI18n();
      return state.cards.filter(c => c.type === t('card.trap')).length;
    },
    totalCount: (state) => state.cards.length,
  },
  actions: {
    addCard(card) {
      const { t } = useI18n();
      // 檢查是否已達到同名卡的上限
      if (this.cards.filter(c => c.id === card.id).length < 3) {
        this.cards.push(card);
      } else {
        alert(t('deck.maxCardLimitReached'));
      }
    },
    removeCard(index) {
      this.cards.splice(index, 1);
    },
    clearDeck() {
      this.cards = [];
    },
    saveDeck(name) {
      // 實際應用中，這裡會與後端API交互
    }
  }
});