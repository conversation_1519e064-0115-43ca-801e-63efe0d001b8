import { defineStore } from 'pinia';

export const useCardStore = defineStore('card', {
  state: () => ({
    cards: [
      { id: 1, name: '青眼白龍', type: '怪獸', attribute: '光', level: 8, atk: 3000, def: 2500, desc: '這張傳說中的龍擁有無與倫比的力量。很少有人能面對它並且存活下來。', image: '/images/cards/blue-eyes.jpg' },
      { id: 2, name: '黑魔導師', type: '怪獸', attribute: '暗', level: 7, atk: 2500, def: 2100, desc: '傳說中的魔術師，精通各種魔法，特別在黑魔法方面的造詣無人能及。', image: '/images/cards/dark-magician.jpg' },
      { id: 3, name: '真紅眼黑龍', type: '怪獸', attribute: '暗', level: 7, atk: 2400, def: 2000, desc: '擁有潛力的傳說中龍，其真正的力量尚未被完全喚醒。', image: '/images/cards/red-eyes.jpg' },
      { id: 4, name: '栗子球', type: '怪獸', attribute: '暗', level: 1, atk: 300, def: 200, desc: '如果這張卡被攻擊，投擲一枚硬幣並猜正反面。猜中的場合，對方怪獸破壞。', image: '/images/cards/kuriboh.jpg' },
      { id: 5, name: '強欲之壺', type: '魔法', desc: '從你的牌組抽2張卡。', image: '/images/cards/pot-of-greed.jpg' },
      { id: 6, name: '神聖防護罩', type: '陷阱', desc: '宣告發動時，這個回合對方怪獸的攻擊無效。', image: '/images/cards/holy-barrier.jpg' },
    ],
    detailCard: null
  }),
  actions: {
    showCardDetail(card) {
      this.detailCard = card;
    },
    hideCardDetail() {
      this.detailCard = null;
    },
    async fetchCards() {
      // 實際應用中，這裡會從後端API獲取卡片資料
      // const response = await fetch('/api/cards');
      // this.cards = await response.json();
    }
  }
});