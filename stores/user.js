import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  state: () => ({
    id: null,
    username: null,
    avatar: null,
    isLoggedIn: false,
    preferences: {
      locale: 'zh-tw',
      theme: 'dark'
    }
  }),
  actions: {
    login(userData) {
      this.id = userData.id;
      this.username = userData.username;
      this.avatar = userData.avatar;
      this.isLoggedIn = true;

      if (userData.preferences) {
        this.preferences = { ...this.preferences, ...userData.preferences };
      }
    },
    logout() {
      this.id = null;
      this.username = null;
      this.avatar = null;
      this.isLoggedIn = false;

      const locale = this.preferences.locale;
      this.preferences = {
        locale,
        theme: 'dark'
      };
    },
    updatePreference(key, value) {
      this.preferences[key] = value;
    }
  },
  persist: true  // ✅ 這樣寫就不會報 SSR 的錯
})
