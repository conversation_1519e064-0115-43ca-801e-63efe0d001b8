// stores/auth.js
import { defineStore } from 'pinia';

export const useAuthStore = defineStore('auth', {
    state: () => {
        // 默認值
        const state = {
            token: null,
            user: null,
            turnstileVerified: null
        };
        
        // 僅在客戶端執行localStorage相關操作
        if (process.client) {
            try {
                const storedUserInfo = localStorage.getItem('user_info');
                if (storedUserInfo) {
                    state.user = JSON.parse(storedUserInfo);
                }
                state.token = localStorage.getItem('auth_token') || null;
                state.turnstileVerified = localStorage.getItem('turnstileVerified');
            } catch (e) {
                console.error('Error accessing localStorage:', e);
            }
        }
        
        return state;
    },

    getters: {
        isAuthenticated: (state) => !!state.token && !!state.user,
        isTurnstileVerified: (state) => state.turnstileVerified === 'true',
        userType: (state) => state.user?.user_type || null
    },

    actions: {
        setAuth({ token, user }) {
            if (!token || !user) {
                console.error('Invalid auth data:', { token, user });
                return;
            }

            // 确保用户信息中包含头像
            if (user && !user.headimg) {
                user.headimg = this.user?.headimg || '/images/default-avatar.png';
            }

            this.token = token;
            this.user = user;
            
            // 僅在客戶端執行localStorage相關操作
            if (process.client) {
                try {
                    localStorage.setItem('auth_token', token);
                    localStorage.setItem('user_info', JSON.stringify(user));
                    // 触发用户信息更新事件
                    window.dispatchEvent(new CustomEvent('user-info-updated', { detail: user }));
                } catch (e) {
                    console.error('Error saving auth data:', e);
                }
            }
        },

        updateUserInfo(newUserInfo) {
            if (!this.user || !newUserInfo) return;
            
            // 保留原有的头像，除非新数据中提供了新的头像
            const updatedUser = {
                ...this.user,
                ...newUserInfo,
                headimg: newUserInfo.headimg || this.user.headimg || '/images/default-avatar.png'
            };

            this.setAuth({
                token: this.token,
                user: updatedUser
            });

            // 触发用户信息更新事件
            if (process.client) {
                window.dispatchEvent(new CustomEvent('user-info-updated', { detail: updatedUser }));
            }
        },

        logout() {
            this.token = null;
            this.user = null;
            
            // 僅在客戶端執行localStorage相關操作
            if (process.client) {
                try {
                    localStorage.removeItem('auth_token');
                    localStorage.removeItem('user_info');
                    localStorage.removeItem('savedEmail');
                    localStorage.removeItem('savedPassword');
                    // 触发用户信息更新事件
                    window.dispatchEvent(new CustomEvent('user-info-updated', { detail: null }));
                } catch (e) {
                    console.error('Error during logout:', e);
                }
            }
        },

        initAuth() {
            // 僅在客戶端執行localStorage相關操作
            if (process.client) {
                try {
                    const token = localStorage.getItem('auth_token');
                    const storedUserInfo = localStorage.getItem('user_info');
                    if (token && storedUserInfo) {
                        const userInfo = JSON.parse(storedUserInfo);
                        if (userInfo) {
                            // 确保用户信息中包含头像
                            if (!userInfo.headimg) {
                                userInfo.headimg = '/images/default-avatar.png';
                            }
                            this.setAuth({ token, user: userInfo });
                        }
                    }
                } catch (e) {
                    console.error('Error during auth initialization:', e);
                    this.logout();
                }
            }
        }
    },

    // 啟用持久化
    persist: true
});