import { defineStore } from 'pinia';

export const useSearchStore = defineStore('search', {
  state: () => ({
    query: '',
    filter: {
      type: null,
      attribute: null,
      level: null,
      minAtk: null,
      maxAtk: null,
      minDef: null,
      maxDef: null
    }
  }),
  actions: {
    setQuery(query) {
      this.query = query;
    },
    clearFilters() {
      this.filter = {
        type: null,
        attribute: null,
        level: null,
        minAtk: null,
        maxAtk: null,
        minDef: null,
        maxDef: null
      };
    },
    applyFilter(filterName, value) {
      this.filter[filterName] = value;
    }
  }
});