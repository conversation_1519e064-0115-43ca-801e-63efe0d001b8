/**
 * 手機版滾動修復插件
 * 自動在每個頁面應用滾動修復
 */

import { useMobileScrollFix } from '~/utils/fixDoubleScroll.js'

export default defineNuxtPlugin((nuxtApp) => {
  // 只在客戶端執行
  if (process.client) {
    // 初始化滾動修復
    let scrollFix = null
    
    // 應用修復的函數
    const applyFixes = () => {
      // 延遲執行確保DOM完全渲染
      setTimeout(() => {
        if (!scrollFix) {
          scrollFix = useMobileScrollFix({
            autoFix: false,
            enableHorizontalScrollPrevention: true
          })
        }
        scrollFix.init()
      }, 100)
    }
    
    // 頁面準備好後應用修復
    nuxtApp.hook('app:mounted', () => {
      applyFixes()
    })
    
    // 路由變化時重新應用修復
    nuxtApp.$router.afterEach((to, from) => {
      setTimeout(() => {
        applyFixes()
      }, 200)
    })
    
    // 視窗大小變化時重新應用修復
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', () => {
        if (window.innerWidth <= 767) {
          applyFixes()
        }
      })
    }
  }
}) 