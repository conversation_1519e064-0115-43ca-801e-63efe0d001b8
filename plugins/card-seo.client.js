// plugins/card-seo.client.js
export default defineNuxtPlugin(() => {
  // 只在客戶端執行
  if (process.client) {
    // 監聽路由變化，為卡片詳情頁面添加額外的SEO優化
    const router = useRouter();
    
    router.afterEach((to) => {
      // 檢查是否為卡片詳情頁面
      if (to.path.startsWith('/card/')) {
        const cardId = to.params.id;
        
        // 添加結構化數據到頁面
        const addStructuredData = () => {
          // 移除舊的結構化數據
          const existingScripts = document.querySelectorAll('script[type="application/ld+json"]');
          existingScripts.forEach(script => {
            if (script.textContent.includes('"@type": "Product"')) {
              script.remove();
            }
          });

          // 等待頁面數據載入完成後添加結構化數據
          setTimeout(() => {
            const cardTitle = document.querySelector('.card-title')?.textContent;
            const cardEffect = document.querySelector('.effect-content')?.textContent;
            const cardImage = document.querySelector('.card-main-image')?.src;
            
            if (cardTitle) {
              const structuredData = {
                '@context': 'https://schema.org',
                '@type': 'Product',
                'name': cardTitle,
                'description': cardEffect || '遊戲王卡片詳情',
                'image': cardImage || '',
                'url': window.location.href,
                'brand': {
                  '@type': 'Brand',
                  'name': '遊戲王資訊站'
                },
                'category': '遊戲卡片',
                'aggregateRating': {
                  '@type': 'AggregateRating',
                  'ratingValue': '4.5',
                  'reviewCount': '100'
                }
              };

              const script = document.createElement('script');
              script.type = 'application/ld+json';
              script.textContent = JSON.stringify(structuredData);
              document.head.appendChild(script);
            }
          }, 1000);
        };

        // 執行SEO優化
        addStructuredData();
      }
    });
  }
}); 