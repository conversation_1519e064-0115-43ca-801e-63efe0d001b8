// plugins/user-navigation.client.js

export default defineNuxtPlugin((nuxtApp) => {
  // 創建一個全局變量，用於追蹤用戶發起的導航操作
  window.userInitiatedNavigation = false;
  
  // 監聽所有可能的用戶交互
  document.addEventListener('click', () => {
    window.userInitiatedNavigation = true;
    
    // 200ms後重置標記
    setTimeout(() => {
      window.userInitiatedNavigation = false;
    }, 200);
  });
  
  // 監聽通過鍵盤觸發的導航
  document.addEventListener('keydown', (e) => {
    // 檢查常見的導航鍵組合
    if (e.key === 'Enter' || e.key === 'Escape' || (e.ctrlKey && e.key === 'Enter')) {
      window.userInitiatedNavigation = true;
      
      // 200ms後重置標記
      setTimeout(() => {
        window.userInitiatedNavigation = false;
      }, 200);
    }
  });
}); 