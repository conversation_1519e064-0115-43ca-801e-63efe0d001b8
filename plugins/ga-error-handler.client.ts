export default defineNuxtPlugin(() => {
  // 只在客戶端和生產環境執行
  if (process.client && process.env.NODE_ENV === 'production') {
    // 攔截 Google Analytics 錯誤
    const originalFetch = window.fetch;
    window.fetch = function(input: RequestInfo | URL, init?: RequestInit) {
      const url = typeof input === 'string' ? input : input.toString();
      
      // 如果是 Google Analytics 請求且失敗，靜默處理
      if (url.includes('google-analytics.com') || url.includes('googletagmanager.com')) {
        return originalFetch(input, init).catch((error) => {
          // 靜默處理 GA 錯誤，不顯示在控制台
          console.debug('Google Analytics request failed:', error);
          return new Response(null, { status: 200 });
        });
      }
      
      return originalFetch(input, init);
    };

    // 攔截 console.error 來過濾 GA 相關錯誤
    const originalConsoleError = console.error;
    console.error = function(...args) {
      const message = args.join(' ');
      if (message.includes('google-analytics.com') || 
          message.includes('googletagmanager.com') ||
          message.includes('gtag')) {
        // 靜默處理 GA 相關錯誤
        console.debug('Google Analytics error suppressed:', message);
        return;
      }
      originalConsoleError.apply(console, args);
    };
  }
}); 