# 卡片詳情頁面 SEO 優化指南

## 已實現的 SEO 優化功能

### 1. 動態 SEO 標籤設置
- **頁面標題**: 格式為 `{卡片名稱} - 遊戲王資訊站`
- **Meta Description**: 包含卡片名稱、效果描述和關鍵詞
- **Meta Keywords**: 自動從卡片名稱和效果中提取關鍵詞
- **Canonical URL**: 設置正確的規範化URL

### 2. Open Graph 標籤
- `og:title`: 卡片名稱
- `og:description`: 卡片效果描述
- `og:image`: 卡片圖片
- `og:type`: website
- `og:url`: 當前頁面URL
- `og:site_name`: 遊戲王資訊站
- `og:locale`: zh_TW

### 3. Twitter Card 標籤
- `twitter:card`: summary_large_image
- `twitter:title`: 卡片名稱
- `twitter:description`: 卡片效果描述
- `twitter:image`: 卡片圖片

### 4. 結構化數據 (Schema.org)
- **Product Schema**: 包含卡片名稱、描述、圖片、品牌等
- **BreadcrumbList Schema**: 麵包屑導航結構化數據
- **AggregateRating Schema**: 評分和評論數量
- **Offer Schema**: 價格信息（如果有）

### 5. Sitemap 生成
- 動態將卡片詳情頁面加入 sitemap
- 設置適當的更新頻率和優先級
- 包含最後修改時間

### 6. 服務端渲染 (SSR)
- 卡片數據在服務端預取
- SEO 標籤在服務端生成
- 改善搜索引擎爬取效果

## 技術實現細節

### API 端點
- `/api/Goods/getInfoDetail`: 獲取卡片詳情（現有API，服務端渲染用）
- `/api/Goods/getList`: 獲取卡片列表（現有API，sitemap生成用）

### 文件結構
```
pages/card/[id].vue                    # 卡片詳情頁面
server/api/Goods/getInfoDetail.js      # 卡片詳情API（現有）
server/api/Goods/getList.js           # 卡片列表API（現有）
plugins/card-seo.client.js            # SEO優化插件
sitemapConfig.js                      # Sitemap配置
```

### SEO 標籤生成邏輯
1. **標題生成**: `{卡片名稱} - 遊戲王資訊站`
2. **描述生成**: `{卡片名稱} - {效果描述}。查看卡片效果、價格、收錄系列及相關牌組。`
3. **關鍵詞提取**: 從卡片名稱和效果中自動提取中文關鍵詞

## 搜索引擎優化建議

### 1. Google 搜尋優化
- 確保頁面載入速度快
- 使用適當的標題層級 (H1, H2, H3)
- 添加內部連結到相關卡片
- 優化圖片 alt 屬性

### 2. 內容優化
- 確保卡片效果描述完整
- 添加相關牌組資訊
- 包含價格和收錄系列資訊
- 提供卡片圖片

### 3. 技術優化
- 啟用 Gzip 壓縮
- 使用 CDN 加速圖片載入
- 實現圖片懶載入
- 優化 CSS 和 JavaScript

## 監控和測試

### 1. Google Search Console
- 提交 sitemap
- 監控索引狀態
- 檢查搜尋表現

### 2. 結構化數據測試
- 使用 Google 結構化數據測試工具
- 驗證 Schema.org 標記

### 3. 頁面速度測試
- Google PageSpeed Insights
- GTmetrix
- WebPageTest

## 預期效果

### 搜尋結果改善
- 卡片名稱搜尋時更容易被找到
- 搜尋結果顯示豐富的摘要
- 提高點擊率 (CTR)

### 用戶體驗改善
- 社交媒體分享時顯示更好的預覽
- 搜尋結果包含卡片圖片
- 提供結構化的卡片資訊

### 技術指標改善
- 提高頁面載入速度
- 改善 Core Web Vitals
- 提高搜尋引擎排名

## 維護建議

1. **定期更新**: 確保卡片數據最新
2. **監控錯誤**: 檢查 API 響應和頁面載入
3. **優化內容**: 根據用戶行為調整內容
4. **技術更新**: 保持 SEO 最佳實踐

## 注意事項

1. **API 限制**: 注意外部 API 的請求限制
2. **圖片優化**: 確保卡片圖片載入速度
3. **錯誤處理**: 處理卡片不存在的情況
4. **快取策略**: 實施適當的快取機制 