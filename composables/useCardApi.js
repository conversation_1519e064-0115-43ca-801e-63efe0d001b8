import { useAuthStore } from '~/stores/auth';

export const useCardApi = () => {
    const authStore = useAuthStore();

    const getHeaders = () => {
        const headers = {
            'Content-Type': 'application/json'
        };

        if (authStore.token) {
            headers.token = authStore.token;
        }

        return headers;
    };

    const api = {
        // 獲取卡片詳情
        async getInfoDetail(goodsId) {
            return $fetch('/api/Goods/getInfoDetail', {
                method: 'POST',
                body: { goods_id: goodsId },
                headers: getHeaders()
            });
        },

        // 獲取卡片收錄信息
        async getGoodsInfoByGoodsId(goodsId) {
            return $fetch('/api/Trade/getGoodsInfoByGoodsId', {
                method: 'POST',
                body: { goods_id: goodsId },
                headers: getHeaders()
            });
        },

        // 獲取包含該卡的牌組列表
        async getGroupsByGoodsId(goodsId, page = 1, pageSize = 20) {
            return $fetch('/api/Goodsgroup/getGroupsByGoodsId', {
                method: 'POST',
                body: {
                    goods_id: goodsId,
                    page: page,
                    page_nums: pageSize
                },
                headers: getHeaders()
            });
        }
    };

    return api;
}; 