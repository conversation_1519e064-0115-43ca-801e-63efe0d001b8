import { useAuthStore } from '~/stores/auth';

// 定义 API 响应类型
interface ApiResponse<T = any> {
  code: number;
  msg?: string;
  data?: T;
}

// 定义用户类型
interface User {
  user_id: number;
  email?: string;
  nickname?: string;
  [key: string]: any;
}

// 定义注册数据类型
interface RegistrationData {
  email: string;
  phone: string;
  birthday?: string;
  nickname: string;
  credential: string;
  type?: number;
  country?: string;
}

export function useApi() {
  const authStore = useAuthStore();

  // 获取认证头信息
  const getAuthHeaders = () => {
    const headers: Record<string, string> = {};

    if (authStore.token) {
      headers['token'] = authStore.token;
    }

    return headers;
  };

  // 统一的错误处理
  const handleApiError = (error: any, defaultMessage: string) => {
    console.error('API Error:', error);
    return {
      code: 500,
      msg: error.message || defaultMessage,
      data: null
    };
  };

  // 创建通用请求方法
  const createRequest = async (endpoint: string, options: any = {}): Promise<ApiResponse> => {
    try {
      const url = `/api${endpoint}`;

      // 检查是否是 FormData 或 URLSearchParams
      const isFormData = options.body instanceof FormData;
      const isUrlEncoded = options.headers?.['Content-Type'] === 'application/x-www-form-urlencoded';
      
      // 获取认证头
      const headers = {
        ...getAuthHeaders(),
        ...(isFormData || isUrlEncoded ? {} : { 'Content-Type': 'application/json' }),
        ...options.headers
      };


      
      // $fetch 已经返回解析后的数据，不需要再调用 .json()
      const data = await $fetch<ApiResponse>(url, {
        ...options,
        headers,
        body: isFormData || isUrlEncoded ? options.body : options.body
      });

      return data;
    } catch (error) {
      return handleApiError(error, `API請求失敗: ${endpoint}`);
    }
  };

  // 创建表单请求
  const createFormRequest = async (endpoint: string, data: any, requireAuth = false) => {
    if (requireAuth && !authStore.token) {
      throw new Error('未登入');
    }

    const formData = new FormData();
    for (const key in data) {
      if (data[key] !== null && data[key] !== undefined) {
        formData.append(key, data[key]);
      }
    }

    return createRequest(endpoint, {
      method: 'POST',
      body: formData
    });
  };

  // 创建需要认证的表单请求
  const createAuthFormRequest = async (endpoint: string, data: any) => {
    if (!authStore.token) {
      throw new Error('未登入');
    }

    const formData = new FormData();
    for (const key in data) {
      if (data[key] !== null && data[key] !== undefined) {
        formData.append(key, data[key]);
      }
    }

    return createRequest(endpoint, {
      method: 'POST',
      body: formData
    });
  };

  return {
    // 登录相关
    async login(email: string, password: string) {
      const params = new URLSearchParams();
      params.append('type', '4');
      params.append('email', email);
      params.append('pwd', password);

      return createRequest('/Login/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: params.toString()
      });
    },

    async refreshLoginToken() {
      if (!authStore.token || !authStore.user) {
        console.error('token無效');
        authStore.logout();
        window.dispatchEvent(new CustomEvent('show-login-modal'));
        throw new Error('token 無效');
      }

      try {
        const user = authStore.user as User;
        const response = await createFormRequest('/Login/refresh', {
          user_id: user.user_id
        }, true);

        if (response.code === 200) {
          authStore.setAuth({
            token: response.data.token,
            user: authStore.user
          });
          return response.data.token;
        } else {
          window.location.href = '/';
          throw new Error(response.msg || '刷新登入token失敗');
        }
      } catch (error) {
        console.error('刷新登入token失敗:', error);
        window.location.href = '/';
        throw error;
      }
    },

    async register(userData: any) {
      return createFormRequest('/Login/register', userData);
    },

    async getVerify(type: number) {
      return createFormRequest('/Login/getVerify', { type });
    },

    async sendEmailCode(key: string, email: string) {
      return createFormRequest('/Login/sendEmailCode', { key, email });
    },

    async forgetPwd(pwd: string, key: string, code: string, email: string) {
      return createFormRequest('/Login/forgetPwd', { pwd, key, code, email });
    },

    async googleLogin(id_token: string, email: string) {
      return createFormRequest('/Login/googleLogin', { id_token, email });
    },

    async completeGoogleRegistration(registrationData: RegistrationData) {
      const requestData: any = {
        email: registrationData.email,
        phone: registrationData.phone,
        birthday: registrationData.birthday || '',
        nickname: registrationData.nickname,
        credential: registrationData.credential
      };

      if (registrationData.type) {
        requestData.type = registrationData.type;
      }

      if (registrationData.type === 2 && registrationData.country) {
        requestData.country = registrationData.country;
      }

      return createFormRequest('/Login/completeGoogleRegistration', requestData, true);
    },

    // 用户相关 - 移除不存在的 API 端點
    getUserInfo: () => Promise.resolve({ code: 200, data: null }),

    logout() {
      authStore.logout();
    },

    // 期数和系列相关
    async getPeriodicalList(params = {}) {
      const defaultParams = {
        game_id: 1
      };
      return createRequest('/Periodical/getList', {
        method: 'POST',
        body: JSON.stringify({ ...defaultParams, ...params })
      });
    },

    async getSeriesList(params = {}) {
      const defaultParams = {
        game_id: 1,
        type: 1,
        new: false,
        page: 1
      };
      return createRequest('/series/list', {
        method: 'POST',
        body: JSON.stringify({ ...defaultParams, ...params })
      });
    },

    async getSeriesByPeriodical(periodicalId: number | string, params = {}) {
      const defaultParams = {
        game_id: 1,
        periodical_id: periodicalId,
        type: 1,
        new: false,
        page: 1
      };
      return createRequest('/series/list', {
        method: 'POST',
        body: JSON.stringify({ ...defaultParams, ...params })
      });
    },

    // 动态相关
    async getMessageList(params = {}) {
      const defaultParams = {
        page: 1,
        page_size: 20,
        game_id: 1,
        category_id: "",
        is_anonymous: 0,
        is_shop: 0
      };
      return createRequest('/message/list', {
        method: 'POST',
        body: JSON.stringify({ ...defaultParams, ...params })
      });
    },

    // 发布动态
    async messageAdd(data: any) {
      return createRequest('/message/add', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: new URLSearchParams({
          game_id: data.game_id || '1',
          category_id: data.category_id || '',
          is_shop: data.is_shop || '0',
          is_anonymous: data.is_anonymous || '0',
          title: data.title || '',
          content: data.content || '',
          price: data.price || '0',
          images: data.images || '',
          video_url: data.video_url || ''
        }).toString()
      });
    },

    // 编辑动态
    async editMessage(data: any) {
      return createRequest('/message/edit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: new URLSearchParams({
          user_id: data.user_id || '',
          message_id: data.message_id || '',
          title: data.title || '',
          content: data.content || '',
          game_id: data.game_id || '1',
          category_id: data.category_id || '',
          is_anonymous: data.is_anonymous || '0',
          is_shop: data.is_shop || '0',
          price: data.price || '0',
          images: data.images || '',
          video_url: data.video_url || ''
        }).toString()
      });
    },

    // 删除动态
    async messageDelete(messageId: number | string) {
      return createRequest('/message/del', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: new URLSearchParams({
          message_id: messageId.toString()
        }).toString()
      });
    },

    // 加入评论
    async addCommentByMessage(type: number | string, valueId: number | string, comment: string) {
      return createRequest('/comment/add', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: new URLSearchParams({
          type: type.toString(),
          value_id: valueId.toString(),
          comment: comment
        }).toString()
      });
    },

    // 删除评论
    async deleteComment(commentId: number | string) {
      return createRequest('/comment/delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: new URLSearchParams({
          comment_id: commentId.toString()
        }).toString()
      });
    },

    // 游戏相关
    async getGameList(params = {}) {
      return createRequest('/game/list', {
        method: 'POST',
        body: JSON.stringify(params)
      });
    },

    // 牌组相关
    async getGoodsgroupList(params = {}) {
      const defaultParams = {
        game_id: 1,
        key_word: '',
        env_id: null,
        page: 1,
        page_size: 20,
      };
      return createRequest('/Goodsgroup/getlist', {
        method: 'POST',
        body: JSON.stringify({ ...defaultParams, ...params })
      });
    },

    async getEnvironmentList(gameId: number) {
      return createRequest('/Goodsgroup/env-list', {
        method: 'POST',
        body: JSON.stringify({ game_id: gameId })
      });
    },

    // 獲取卡組環境列表 - 使用現有的 env-list API
    async getGoodsGroupEnvList(gameId: number | string) {
      try {
        return await createRequest('/Goodsgroup/env-list', {
          method: 'POST',
          body: JSON.stringify({ game_id: gameId })
        });
      } catch (error) {
        console.error('獲取卡組環境列表失敗:', error);
        return handleApiError(error, '獲取卡組環境列表失敗');
      }
    },

    // 獲取我的牌組列表 - 新增的方法（需要登入）
    async getMyCardgroupList(params: any) {
      try {
        if (!authStore.token) {
          throw new Error('未登入');
        }
        
        return await createAuthFormRequest('/Goodsgroup/getMyList', params);
      } catch (error) {
        console.error('獲取我的牌組列表失敗:', error);
        return handleApiError(error, '獲取我的牌組列表失敗');
      }
    },

    // 🆕 新增：獲取卡組類型列表
    async getGoodsGroupTypeList() {
      try {
        return await createRequest('/Goodsgroup/type-list', {
          method: 'POST'
        });
      } catch (error) {
        console.error('獲取卡組類型列表失敗:', error);
        return handleApiError(error, '獲取卡組類型列表失敗');
      }
    },

    // 🆕 新增：獲取卡片屬性列表
    async getAttrList(gameId: number | string) {
      try {
        if (!authStore.token) {
          throw new Error('未登入');
        }
        
        return await createRequest('/Goods/getAttrList', {
          method: 'POST',
          body: JSON.stringify({ game_id: gameId })
        });
      } catch (error) {
        console.error('獲取卡片屬性列表失敗:', error);
        return handleApiError(error, '獲取卡片屬性列表失敗');
      }
    },

    // 🆕 新增：根據屬性獲取卡片列表
    async getGoodsListByAttr(params: any) {
      try {
        const {
          game_id,
          key_word = '',
          params: attrParams = '',
          page = 1,
          page_nums = 50,
          is_identify = 1
        } = params;

        if (!game_id) {
          throw new Error('缺少必要參數: game_id');
        }

        return await createRequest('/Goods/list-by-attr', {
          method: 'POST',
          body: JSON.stringify({
            game_id,
            key_word,
            params: attrParams,
            page,
            page_nums,
            is_identify
          })
        });
      } catch (error) {
        console.error('根據屬性獲取卡片列表失敗:', error);
        return handleApiError(error, '根據屬性獲取卡片列表失敗');
      }
    },

    // 🆕 新增：更新牌組
    async updateGoodsGroup(data: any) {
      try {
        if (!authStore.token) {
          throw new Error('未登入');
        }

        const {
          group_id = 0,
          is_identify = 1,
          title,
          desc = '',
          env_id,
          image,
          game_id = 1,
          goods_info = ''
        } = data;

        if (!title || !env_id || !image) {
          throw new Error('缺少必要參數: title, env_id, image');
        }

        return await createRequest('/Goodsgroup/update', {
          method: 'POST',
          body: JSON.stringify({
            group_id,
            is_identify,
            title,
            desc,
            env_id,
            image,
            game_id,
            goods_info
          })
        });
      } catch (error) {
        console.error('更新牌組失敗:', error);
        return handleApiError(error, '更新牌組失敗');
      }
    },

    async getPieChartData(params = {}) {
      const defaultParams = {
        game_id: 1,
        env_id: null,
        start_date: null,
        end_date: null,
      };
      return createRequest('/Goodsgroup/piechart', {
        method: 'POST',
        body: JSON.stringify({ ...defaultParams, ...params })
      });
    },

    async getDeckPercentage(params = {}) {
      const defaultParams = {
        title: '',
        env_id: null,
        game_id: 1,
        start_date: null,
        end_date: null,
      };
      return createRequest('/Goodsgroup/percentage', {
        method: 'POST',
        body: JSON.stringify({ ...defaultParams, ...params })
      });
    },

    async getGoodsgroupTierList(gameId: number) {
      return createRequest('/Goodsgroup/tier-list', {
        method: 'POST',
        body: JSON.stringify({ game_id: gameId })
      });
    },

    // 🆕 更新：獲取卡組詳情 - 使用新的 API 路徑
    async getCardGroupDetails(groupId: number) {
      try {
        return await createRequest('/Goodsgroup/info', {
          method: 'POST',
          body: JSON.stringify({ group_id: groupId })
        });
      } catch (error) {
        console.error('獲取卡組詳情失敗:', error);
        return handleApiError(error, '獲取卡組詳情失敗');
      }
    },

    async getdeckdetail(params = {}) {
      const defaultParams = {
        game_id: 1,
        title: '',
        env_id: 0,
        type_id: 1
      };
      return createRequest('/Goodsgroup/getdeckdetail', {
        method: 'POST',
        body: JSON.stringify({ ...defaultParams, ...params })
      });
    },

    // 卡片相关
    async getGoodsList(params: any) {
      const payload = {
        game_id: params.gameId || params.game_id || '',
        type: params.type || 1,
        series_id: params.series_id !== undefined ? parseInt(params.series_id) :
                  params.seriesId !== undefined ? parseInt(params.seriesId) : '',
        rare_id: params.rareId || params.rare_id || 0,
        page: params.page || 1,
        page_nums: params.pageSize || params.page_nums || 20,
        order_type: params.orderType || params.order_type || 0,
        order_sort: params.orderSort || params.order_sort || 0,
        periodical_id: params.periodicalId || params.periodical_id || 0,
        key_word: params.key_word || '',
        show_type: params.show_type || 0,
        is_peek: params.isPeek || params.is_peek || 0,
        hot: params.hot === 1 ? 1 : 0,
        filter_no_sale: params.filter_no_sale === true
      };

      return createRequest('/Goods/getList', {
        method: 'POST',
        body: JSON.stringify(payload)
      });
    },

    // 获取卡片详情
    async getCardDetail(cardId: number) {
      try {
        const response = await createRequest('/Goods/getInfo', {
          method: 'POST',
          body: JSON.stringify({ goods_id: cardId })
        });

        if (response && (response.code === 200 || response.code === 0)) {
          return response;
        } else {
          console.error('獲取卡片詳情失敗，返回錯誤:', response);
          return {
            code: response?.code || 500,
            msg: response?.msg || '獲取卡片詳情失敗',
            data: null
          };
        }
      } catch (error) {
        return handleApiError(error, '獲取卡片詳情失敗');
      }
    },

    // 获取卡片价格历史
    async getCardPriceHistory(cardId: number) {
      try {
        const response = await createRequest('/Goods/priceHistory', {
          method: 'POST',
          body: JSON.stringify({ goods_id: cardId })
        });

        if (response && (response.code === 200 || response.code === 0)) {
          return response;
        } else {
          console.error('獲取卡片價格歷史失敗，返回錯誤:', response);
          return {
            code: response?.code || 500,
            msg: response?.msg || '獲取卡片價格歷史失敗',
            data: null
          };
        }
      } catch (error) {
        return handleApiError(error, '獲取卡片價格歷史失敗');
      }
    },

    // 获取卡片相关牌组
    async getCardRelatedDecks(cardId: number, params = {}) {
      const defaultParams = {
        goods_id: cardId,
        page: 1,
        page_size: 20
      };

      return createRequest('/Goodsgroup/getRelatedDecks', {
        method: 'POST',
        body: JSON.stringify({ ...defaultParams, ...params })
      });
    },

    // 获取卡片相关动态
    async getCardRelatedMessages(cardId: number, params = {}) {
      const defaultParams = {
        goods_id: cardId,
        page: 1,
        page_size: 20
      };

      return createRequest('/message/getRelatedMessages', {
        method: 'POST',
        body: JSON.stringify({ ...defaultParams, ...params })
      });
    },

    // 获取卡片收藏状态
    async getCardCollectStatus(cardId: number) {
      try {
        const response = await createRequest('/collect/status', {
          method: 'POST',
          body: JSON.stringify({
            type: 1, // 1: 卡片收藏
            target_id: cardId
          })
        });

        return response;
      } catch (error) {
        return handleApiError(error, '獲取卡片收藏狀態失敗');
      }
    },

    // 收藏/取消收藏卡片
    async toggleCardCollect(cardId: number, status: number) {
      try {
        const response = await createRequest('/collect/collect', {
          method: 'POST',
          body: JSON.stringify({
            type: 1, // 1: 卡片收藏
            target_id: cardId,
            status: status // 1: 收藏, 2: 取消收藏
          })
        });

        return response;
      } catch (error) {
        return handleApiError(error, '收藏操作失敗');
      }
    },

    // 获取卡片评论列表
    async getCardComments(cardId: number, params = {}) {
      const defaultParams = {
        goods_id: cardId,
        page: 1,
        page_size: 20
      };

      return createRequest('/comment/listbygoods', {
        method: 'POST',
        body: JSON.stringify({ ...defaultParams, ...params })
      });
    },

    // 加入卡片评论
    async addCardComment(cardId: number, comment: string) {
      try {
        const response = await createRequest('/comment/add', {
          method: 'POST',
          body: JSON.stringify({
            type: 2, // 2: 卡片评论
            goods_id: cardId,
            comment: comment
          })
        });

        return response;
      } catch (error) {
        return handleApiError(error, '加入評論失敗');
      }
    },

    // 删除卡片评论
    async deleteCardComment(commentId: number) {
      try {
        const response = await createRequest('/comment/delete', {
          method: 'POST',
          body: JSON.stringify({ comment_id: commentId })
        });

        return response;
      } catch (error) {
        return handleApiError(error, '刪除評論失敗');
      }
    },

    // 系列相关
    async getSeriesInfo(seriesId: number) {
      try {
        const response = await createRequest('/series/getInfo', {
          method: 'POST',
          body: JSON.stringify({ series_id: seriesId })
        });

        if (response && (response.code === 200 || response.code === 0)) {
          return response;
        } else {
          console.error('獲取系列詳情失敗，返回錯誤:', response);
          return {
            code: response?.code || 500,
            msg: response?.msg || '獲取系列詳情失敗',
            data: null
          };
        }
      } catch (error) {
        return handleApiError(error, '獲取系列詳情失敗');
      }
    },

    async getSeriesRareList(seriesId: number, type: number) {
      try {
        const response = await createRequest('/Rare/getSeriesRareList', {
          method: 'POST',
          body: JSON.stringify({
            series_id: parseInt(String(seriesId)),
            type: type
          })
        });
        return response;
      } catch (error) {
        return handleApiError(error, '獲取系列稀有度列表失敗');
      }
    },

    // 简化版商品列表获取
    async getGoodsListSimple(params = {}) {
      const defaultParams = {
        game_id: 1,
        page: 1,
        page_size: 20
      };
      return createRequest('/Goods/getList', {
        method: 'POST',
        body: JSON.stringify({ ...defaultParams, ...params })
      });
    },

    // 上传图片
    async uploadImage(file: File) {
      const formData = new FormData();
      formData.append('dir', 'headimg');
      formData.append('img', file);

      return createRequest('/Common/upload', {
        method: 'POST',
        body: formData
      });
    }
  };
}

// 导出直接可用的函数
export const login = async (email: string, password: string) => {
  const api = useApi();
  return await api.login(email, password);
};

export const getVerify = async (type: number) => {
  const api = useApi();
  return await api.getVerify(type);
};

export const sendEmailCode = async (key: string, email: string) => {
  const api = useApi();
  return await api.sendEmailCode(key, email);
};

export const forgetPwd = async (pwd: string, key: string, code: string, email: string) => {
  const api = useApi();
  return await api.forgetPwd(pwd, key, code, email);
};

export const register = async (userData: any) => {
  const api = useApi();
  return await api.register(userData);
};

export const googleLogin = async (id_token: string, email: string) => {
  const api = useApi();
  return await api.googleLogin(id_token, email);
};

export const completeGoogleRegistration = async (registrationData: any) => {
  const api = useApi();
  return await api.completeGoogleRegistration(registrationData);
};

export const getUserInfo = async () => {
  const api = useApi();
  return await api.getUserInfo();
};

// 新增的導出函數
export const getGoodsGroupEnvList = async (gameId: number | string) => {
  const api = useApi();
  return await api.getGoodsGroupEnvList(gameId);
};

export const getMyCardgroupList = async (params: any) => {
  const api = useApi();
  return await api.getMyCardgroupList(params);
};

export const getGameList = async (params = {}) => {
  const api = useApi();
  return await api.getGameList(params);
};

// 🆕 新增的導出函數
export const getGoodsGroupTypeList = async () => {
  const api = useApi();
  return await api.getGoodsGroupTypeList();
};

export const getAttrList = async (gameId: number | string) => {
  const api = useApi();
  return await api.getAttrList(gameId);
};

export const getGoodsListByAttr = async (params: any) => {
  const api = useApi();
  return await api.getGoodsListByAttr(params);
};

export const updateGoodsGroup = async (data: any) => {
  const api = useApi();
  return await api.updateGoodsGroup(data);
};

export const getCardGroupDetails = async (groupId: number) => {
  const api = useApi();
  return await api.getCardGroupDetails(groupId);
};