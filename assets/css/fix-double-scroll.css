/* 修復雙重滾動問題的通用CSS */

/* 全局修復 */
html, body {
    overflow-x: hidden !important;
    max-width: 100vw !important;
    box-sizing: border-box;
}

*, *::before, *::after {
    box-sizing: border-box;
}

/* 手機版修復 */
@media (max-width: 767px) {
    /* 確保整個應用不會產生水平滾動 */
    #__nuxt, #app, .app-layout {
        width: 100vw !important;
        max-width: 100vw !important;
        overflow-x: hidden !important;
    }
    
    /* 主內容區域 - 關鍵修復 */
    .main-content {
        height: 100vh !important;
        height: 100dvh !important; /* 動態視窗高度，更精確 */
        padding-bottom: 0 !important; /* 移除底部padding */
        overflow-x: hidden !important;
        overflow-y: auto !important;
        -webkit-overflow-scrolling: touch;
    }
    
    /* 內容包裝器 - 確保正確的滾動區域 */
    .content-wrapper {
        height: calc(100vh - 4rem) !important; /* 減去底部導航高度 */
        height: calc(100dvh - 4rem) !important;
        padding-bottom: 1rem !important; /* 只保留少量底部間距 */
        overflow-x: hidden !important;
        overflow-y: auto !important;
        -webkit-overflow-scrolling: touch;
    }
    
    /* 底部導航 - 真正的狀態列樣式 */
    .mobile-bottom-nav {
        position: fixed !important;
        bottom: 0 !important;
        left: 0 !important;
        right: 0 !important;
        height: 4rem !important; /* 64px */
        z-index: 9999 !important;
        background-color: #061224 !important;
        border-top: 1px solid rgba(30, 144, 255, 0.15) !important;
        box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1) !important;
        backdrop-filter: blur(10px) !important;
    }
    
    /* 底部導航內容 */
    .bottom-nav {
        height: 100% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: space-around !important;
        padding: 0.5rem 0 !important;
        background: transparent !important;
    }
    
    /* 頁面安全區域 */
    .mobile-safe-page {
        min-height: calc(100vh - 4rem) !important;
        min-height: calc(100dvh - 4rem) !important;
        max-width: 100vw !important;
        overflow-x: hidden !important;
        padding-bottom: 1rem !important; /* 確保內容不被遮住 */
    }
    
    /* 頁面容器 */
    .page-container {
        width: 100vw !important;
        max-width: 100vw !important;
        padding-left: 0.75rem !important;
        padding-right: 0.75rem !important;
        overflow-x: hidden !important;
    }
    
    /* 所有可能導致水平滾動的元素 */
    .container, [class*="container"] {
        max-width: 100vw !important;
        width: 100% !important;
        overflow-x: hidden !important;
        margin-left: auto !important;
        margin-right: auto !important;
    }
    
    /* Grid 和 Flex 容器 */
    .grid, .flex, [class*="grid"], [class*="flex"] {
        max-width: 100% !important;
        overflow-x: hidden !important;
    }
    
    /* 圖片和媒體元素 */
    img, video, canvas, iframe {
        max-width: 100% !important;
        height: auto !important;
    }
    
    /* 表格響應式 */
    table {
        width: 100% !important;
        table-layout: fixed !important;
        overflow-x: auto !important;
    }
    
    /* 文字內容防溢出 */
    pre, code {
        overflow-x: auto !important;
        max-width: 100% !important;
        word-wrap: break-word !important;
        white-space: pre-wrap !important;
    }
    
    /* 特殊頁面調整 */
    .deck-page .content-wrapper,
    .social-page .content-wrapper,
    .goodsgroup-page .content-wrapper {
        height: calc(100vh - 4rem) !important;
        height: calc(100dvh - 4rem) !important;
    }
    
    /* 模態框和彈窗調整 */
    .modal, .dialog, .popup {
        max-height: calc(100vh - 4rem) !important;
        max-height: calc(100dvh - 4rem) !important;
        overflow-y: auto !important;
        overflow-x: hidden !important;
    }
    
    /* 修復評論區域（針對message頁面的特殊處理） */
    .mobile-bottom-comment {
        bottom: 4rem !important; /* 底部導航高度 */
        max-width: 100vw !important;
        left: 0 !important;
        right: 0 !important;
    }
    
    /* 固定按鈕通用修復 - 確保不被底部導航遮住 */
    .fixed[class*="bottom-"] {
        bottom: 5rem !important; /* 底部導航高度 + 安全間距 */
    }
    
    /* 特定的固定按鈕修復 */
    .fixed.bottom-8 {
        bottom: 5rem !important; /* 原本 bottom-8 (2rem) + 底部導航 (4rem) + 間距 */
    }
    
    .fixed.bottom-4 {
        bottom: 5rem !important;
    }
    
    .fixed.bottom-6 {
        bottom: 5rem !important;
    }
    
    /* 浮動操作按鈕 (FAB) 特殊處理 */
    [class*="floating"], 
    .fab, 
    .action-button {
        bottom: 5rem !important;
    }
    
    /* 手機版彈窗通用修復 */
    .absolute[class*="bottom-"] {
        bottom: 4.5rem !important; /* 略高於底部導航 */
    }
    
    /* 特定底部彈窗修復 */
    .absolute.bottom-20 {
        bottom: 4.5rem !important; /* 調整到底部導航上方 */
    }
    
    .absolute.bottom-16 {
        bottom: 4.5rem !important;
    }
    
    .absolute.bottom-24 {
        bottom: 4.5rem !important;
    }
    
    /* Toast 和通知修復 */
    .toast, .notification, .alert {
        bottom: 5rem !important;
    }
    
    /* 確保滾動平滑 */
    .main-content,
    .content-wrapper,
    .mobile-safe-page {
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch;
    }
    
    /* 隱藏水平滾動條但保持功能 */
    ::-webkit-scrollbar:horizontal {
        display: none;
    }
    
    /* 防止橡皮筋效果影響滾動 */
    body {
        overscroll-behavior-x: none;
        overscroll-behavior-y: auto;
    }
}

/* 桌面版保持原有行為 */
@media (min-width: 768px) {
    .mobile-bottom-nav {
        display: none !important;
    }
    
    .content-wrapper {
        padding-bottom: 0 !important;
        height: 100% !important;
    }
    
    .main-content {
        padding-left: 16rem !important; /* 側邊欄寬度 */
    }
    
    .mobile-bottom-comment {
        left: 16rem !important;
        bottom: 0 !important;
    }
    
    /* 桌面版保持原有固定按鈕位置 */
    .fixed[class*="bottom-"] {
        bottom: initial !important;
    }
    
    .fixed.bottom-8 {
        bottom: 2rem !important; /* 恢復原有位置 */
    }
    
    .fixed.bottom-4 {
        bottom: 1rem !important;
    }
    
    .fixed.bottom-6 {
        bottom: 1.5rem !important;
    }
}

/* 通用滾動改善 */
.scroll-smooth {
    scroll-behavior: smooth;
}

.scroll-lock {
    overflow: hidden !important;
}

/* 調試用 - 可以暫時啟用來檢查佈局 */
/*
.debug-layout * {
    outline: 1px solid rgba(255, 0, 0, 0.2) !important;
}
*/