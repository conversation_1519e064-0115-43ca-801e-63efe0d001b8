@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局自定義樣式 */
:root {
    --primary-color: #3b82f6;
    --secondary-color: #10b981;
    --accent-color: #8b5cf6;
    --background-dark: #111827;
    --background-light: #1f2937;
    --text-light: #f3f4f6;
    --text-muted: #9ca3af;
}

body {
    font-family: 'Noto Sans TC', sans-serif, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto;
    color: var(--text-light);
    background-color: var(--background-dark);
}

/* 自定義遊戲王專用類別 */
.card-hover {
    @apply transition-transform duration-200 ease-in-out;
}

.card-hover:hover {
    @apply transform scale-105;
}

.card-shadow {
    @apply shadow-lg;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.35);
}

/* 滾動條樣式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #374151;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #4b5563;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #6b7280;
}

/* 動畫效果 */
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}