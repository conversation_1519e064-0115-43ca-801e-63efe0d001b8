{"name": "yugioh-deckbuilder", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev --host 0.0.0.0", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxtjs/i18n": "^9.5.3", "@nuxtjs/pwa": "^3.3.5", "@nuxtjs/tailwindcss": "^6.14.0", "@pinia/nuxt": "^0.11.0", "@vite-pwa/nuxt": "^1.0.1", "chart.js": "^4.4.9", "element-plus": "^2.9.9", "lucide-vue-next": "^0.503.0", "node-fetch": "2", "nuxt": "^3.17.0", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.2.0", "vanilla-tilt": "^1.8.1", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e", "devDependencies": {"@element-plus/nuxt": "^1.1.1", "@nuxtjs/sitemap": "^7.2.10"}, "engines": {"yarn": ">= 1.22.0", "npm": "please-use-yarn"}}