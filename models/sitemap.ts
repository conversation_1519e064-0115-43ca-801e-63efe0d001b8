// modules/sitemap.ts
import { defineNuxtModule } from 'nuxt/kit'
import sitemapConfig from '../sitemapConfig'

export default defineNuxtModule({
    setup(options, nuxt) {
        // 這裡可以加入您的 sitemap 模組特定配置
        nuxt.hook('modules:before', () => {
            const sitemapModuleIndex = nuxt.options.modules.findIndex(
                module => module === '@nuxtjs/sitemap' ||
                    (Array.isArray(module) && module[0] === '@nuxtjs/sitemap')
            )

            if (sitemapModuleIndex !== -1) {
                // 替換為帶配置的模組定義
                nuxt.options.modules[sitemapModuleIndex] = ['@nuxtjs/sitemap', sitemapConfig]
            }
        })
    }
})