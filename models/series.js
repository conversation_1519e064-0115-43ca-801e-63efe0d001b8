// models/series.js
export class SeriesItem {
    constructor(data) {
        this.id = data.series_id;
        this.title = data.title;
        this.image = data.goods_thumb;
        this.goodsSn = data.goods_sn;
        this.gameId = data.game_id;
        this.gameName = data.game_title;
        this.createTime = this.formatDate(data.create_time);
        this.preorderDate = data.preorder_date ? this.formatDate(data.preorder_date) : null;
    }

    // 將時間戳轉換為日期格式
    formatDate(timestamp) {
        if (!timestamp) return '';
        
        let date;
        
        // 檢查是否為字符串格式的日期時間
        if (typeof timestamp === 'string' && timestamp.includes('-')) {
            date = new Date(timestamp);
        } else {
            // 處理Unix時間戳（數字）
            date = new Date(parseInt(timestamp) * 1000);
        }
        
        if (isNaN(date.getTime())) {
            return '';
        }
        
        return date.toISOString().split('T')[0]; // 返回 YYYY-MM-DD 格式
    }
}