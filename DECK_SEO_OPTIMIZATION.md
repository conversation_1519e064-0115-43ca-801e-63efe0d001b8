# 牌組詳情頁面 SEO 優化指南

## 已實現的 SEO 優化功能

### 1. 動態 SEO 標籤設置
- **頁面標題**: 格式為 `{牌組名稱} - 遊戲王資訊站`
- **Meta Description**: 包含牌組名稱、描述和卡片數量統計
- **Meta Keywords**: 自動從牌組名稱和描述中提取關鍵詞
- **Canonical URL**: 設置正確的規範化URL

### 2. Open Graph 標籤
- `og:title`: 牌組名稱
- `og:description`: 牌組描述和統計資訊
- `og:image`: 牌組圖片
- `og:type`: website
- `og:url`: 當前頁面URL
- `og:site_name`: 遊戲王資訊站
- `og:locale`: zh_TW

### 3. Twitter Card 標籤
- `twitter:card`: summary_large_image
- `twitter:title`: 牌組名稱
- `twitter:description`: 牌組描述和統計資訊
- `twitter:image`: 牌組圖片

### 4. 結構化數據 (Schema.org)
- **CreativeWork Schema**: 包含牌組名稱、描述、圖片、作者等
- **BreadcrumbList Schema**: 麵包屑導航結構化數據
- **Person Schema**: 牌組創建者資訊
- **統計資訊**: 主牌組、額外牌組、備牌數量

### 5. Sitemap 生成
- 動態將牌組詳情頁面加入 sitemap
- 設置適當的更新頻率和優先級
- 包含最後修改時間

### 6. 服務端渲染 (SSR)
- 牌組數據在服務端預取
- SEO 標籤在服務端生成
- 改善搜索引擎爬取效果

## 技術實現細節

### API 端點
- `/api/Goodsgroup/info`: 獲取牌組詳情（現有API，服務端渲染用）
- `/api/Goodsgroup/getlist`: 獲取牌組列表（現有API，sitemap生成用）

### 文件結構
```
pages/Goodsgroup/[groupId].vue        # 牌組詳情頁面
server/api/Goodsgroup/info.js         # 牌組詳情API（現有）
server/api/Goodsgroup/getlist.js      # 牌組列表API（現有）
sitemapConfig.js                     # Sitemap配置
```

### SEO 標籤生成邏輯
1. **標題生成**: `{牌組名稱} - 遊戲王資訊站`
2. **描述生成**: `{牌組名稱} - {描述}。包含主牌組(X張)、額外牌組(X張)、備牌(X張)的完整牌組列表。`
3. **關鍵詞提取**: 從牌組名稱和描述中自動提取中文關鍵詞

## 牌組詳情頁面特色

### 1. 完整的牌組資訊
- 牌組名稱和描述
- 創建者資訊
- 創建時間
- 牌組圖片

### 2. 詳細的卡片統計
- 主牌組卡片數量
- 額外牌組卡片數量
- 備牌卡片數量
- 卡片類型分類

### 3. 卡片詳情展示
- 卡片圖片網格顯示
- 卡片列表詳細資訊
- 禁限制狀態標示
- 卡片效果懸停預覽

## 搜索引擎優化建議

### 1. Google 搜尋優化
- 確保頁面載入速度快
- 使用適當的標題層級 (H1, H2, H3)
- 添加內部連結到相關卡片
- 優化圖片 alt 屬性

### 2. 內容優化
- 確保牌組描述完整
- 添加相關卡片資訊
- 包含創建者資訊
- 提供牌組圖片

### 3. 技術優化
- 啟用 Gzip 壓縮
- 使用 CDN 加速圖片載入
- 實現圖片懶載入
- 優化 CSS 和 JavaScript

## 監控和測試

### 1. Google Search Console
- 提交 sitemap
- 監控索引狀態
- 檢查搜尋表現

### 2. 結構化數據測試
- 使用 Google 結構化數據測試工具
- 驗證 Schema.org 標記

### 3. 頁面速度測試
- Google PageSpeed Insights
- GTmetrix
- WebPageTest

## 預期效果

### 搜尋結果改善
- 牌組名稱搜尋時更容易被找到
- 搜尋結果顯示豐富的摘要
- 提高點擊率 (CTR)

### 用戶體驗改善
- 社交媒體分享時顯示更好的預覽
- 搜尋結果包含牌組圖片
- 提供結構化的牌組資訊

### 技術指標改善
- 提高頁面載入速度
- 改善 Core Web Vitals
- 提高搜尋引擎排名

## 維護建議

1. **定期更新**: 確保牌組數據最新
2. **監控錯誤**: 檢查 API 響應和頁面載入
3. **優化內容**: 根據用戶行為調整內容
4. **技術更新**: 保持 SEO 最佳實踐

## 注意事項

1. **API 限制**: 注意外部 API 的請求限制
2. **圖片優化**: 確保牌組圖片載入速度
3. **錯誤處理**: 處理牌組不存在的情況
4. **快取策略**: 實施適當的快取機制

## 與卡片詳情頁面的協同效應

### 1. 交叉連結
- 牌組詳情頁面連結到相關卡片
- 卡片詳情頁面顯示包含該卡的牌組

### 2. 內容互補
- 牌組提供整體構築思路
- 卡片提供詳細效果說明

### 3. SEO 協同
- 共同提升遊戲王相關搜尋排名
- 增加網站內部連結結構
- 提供完整的遊戲王內容生態 