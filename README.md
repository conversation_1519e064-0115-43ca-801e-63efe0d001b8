# 遊戲王卡片構築器

一個專為遊戲王玩家設計的卡片構築和管理平台。

## 功能特色

- 🃏 卡片搜尋與瀏覽
- 📚 卡片系列管理
- 🎯 牌組構築工具
- 📊 牌組統計分析
- 🌐 多語言支援
- 📱 響應式設計

## 安裝與設定

確保已安裝依賴套件：

```bash
# npm
npm install

# pnpm
pnpm install

# yarn
yarn install

# bun
bun install
```

## 開發伺服器

在 `http://localhost:3000` 啟動開發伺服器：

```bash
# npm
npm run dev

# pnpm
pnpm dev

# yarn
yarn dev

# bun
bun run dev
```

## 生產環境建置

建置應用程式用於生產環境：

```bash
# npm
npm run build

# pnpm
pnpm build

# yarn
yarn build

# bun
bun run build
```

本地預覽生產建置：

```bash
# npm
npm run preview

# pnpm
pnpm preview

# yarn
yarn preview

# bun
bun run preview
```

## 技術架構

- 前端框架：Vue.js 3
- UI 框架：Element Plus
- 樣式框架：Tailwind CSS
- 狀態管理：Pinia
- 國際化：Vue I18n
- PWA 支援

## 授權

本專案採用 MIT 授權條款。
