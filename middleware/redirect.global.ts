export default defineNuxtRouteMiddleware((to, from) => {
  if (to.path === '/seriesdetail' && to.query.id) {
    return navigateTo(`/series/${to.query.id}`, { redirectCode: 301 });
  }

  // 重定向舊的 /seriesDetail/:id 路由到 /series/:id
  if (to.path.startsWith('/seriesDetail/')) {
    const seriesId = to.path.split('/')[2];
    return navigateTo(`/series/${seriesId}`, { redirectCode: 301 });
  }

  return;
});