// sitemapConfig.js - 放在專案根目錄
export default {
    siteUrl: process.env.NUXT_PUBLIC_SITE_URL || 'https://ygo.iwantcard.tw',
    gzip: true,
    exclude: ['/admin/**', '/api/**', '/_nuxt/**', '/__sitemap__/**', '/sponsor/**'],
    i18n: true, // 啟用 i18n 支持
    // 添加更多SEO優化選項
    defaults: {
        changefreq: 'weekly',
        priority: 0.8,
        lastmod: new Date(),
    },
    // 自定義路由配置
    routes: async () => {
        // 這裡可以動態添加更多路由
        const routes = [
            {
                url: '/',
                changefreq: 'daily',
                priority: 1.0,
                lastmod: new Date(),
            },
            {
                url: '/deck',
                changefreq: 'daily',
                priority: 0.9,
                lastmod: new Date(),
            },
            {
                url: '/Goodsgroup',
                changefreq: 'daily',
                priority: 0.9,
                lastmod: new Date(),
            },
            {
                url: '/series',
                changefreq: 'weekly',
                priority: 0.8,
                lastmod: new Date(),
            },
            {
                url: '/news',
                changefreq: 'daily',
                priority: 0.7,
                lastmod: new Date(),
            }
        ];

        // 動態添加卡片詳情頁面
        try {
            const cardsResponse = await fetch(`${process.env.NUXT_PUBLIC_SITE_URL || 'https://ygo.iwantcard.tw'}/api/Goods/getList`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    game_id: 1,
                    page: 1,
                    page_nums: 1000
                })
            });
            
            if (cardsResponse.ok) {
                const cardsData = await cardsResponse.json();
                if (cardsData.code === 200 && cardsData.data && cardsData.data.list) {
                    // 添加前100張卡片到sitemap（避免sitemap過大）
                    const cardRoutes = cardsData.data.list.slice(0, 100).map(card => ({
                        url: `/card/${card.goods_id}`,
                        changefreq: 'weekly',
                        priority: 0.7,
                        lastmod: new Date(),
                    }));
                    routes.push(...cardRoutes);
                }
            }
        } catch (error) {
            console.error('獲取卡片列表失敗:', error);
        }

        // 動態添加牌組詳情頁面
        try {
            const decksResponse = await fetch(`${process.env.NUXT_PUBLIC_SITE_URL || 'https://ygo.iwantcard.tw'}/api/Goodsgroup/getlist`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    game_id: 1,
                    page: 1,
                    page_nums: 100
                })
            });
            
            if (decksResponse.ok) {
                const decksData = await decksResponse.json();
                if (decksData.code === 200 && decksData.data && decksData.data.list) {
                    // 添加前50個牌組到sitemap
                    const deckRoutes = decksData.data.list.slice(0, 50).map(deck => ({
                        url: `/Goodsgroup/${deck.group_id}`,
                        changefreq: 'weekly',
                        priority: 0.8,
                        lastmod: new Date(),
                    }));
                    routes.push(...deckRoutes);
                }
            }
        } catch (error) {
            console.error('獲取牌組列表失敗:', error);
        }

        return routes;
    }
}