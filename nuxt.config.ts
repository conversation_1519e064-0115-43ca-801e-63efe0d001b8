// 遊戲王卡片構築器配置檔案

export default defineNuxtConfig({
  // 定義運行時配置，包括網站 URL
  runtimeConfig: {
    public: {
      siteUrl: process.env.NUXT_PUBLIC_SITE_URL || 'http://localhost:3000',
      apiBase: process.env.API_BASE || 'http://localhost:3000/api',
    }
  },

  modules: [
    '@nuxtjs/i18n',
    '@nuxtjs/tailwindcss',
    '@pinia/nuxt',
    '@element-plus/nuxt',
    '@nuxtjs/sitemap',
    '@vite-pwa/nuxt'
  ],

  // 加入性能優化配置
  vite: {
    build: {
      chunkSizeWarningLimit: 1000,
      rollupOptions: {
        output: {
          manualChunks: {
            'chart': ['chart.js'],
            'element-plus': ['element-plus']
          }
        }
      }
    },
    optimizeDeps: {
      include: ['chart.js', 'element-plus']
    }
  },

  i18n: {
    strategy: 'prefix_except_default',
    defaultLocale: 'zh-tw',
    lazy: true,
    locales: [
      { 
        code: 'en', 
        name: 'English',
        file: 'en.json'
      },
      { 
        code: 'zh-tw', 
        name: '繁體中文',
        file: 'zh-TW.json'
      },
      { 
        code: 'zh-cn', 
        name: '简体中文',
        file: 'zh-CN.json'
      }
    ],
    langDir: 'locales/',
    bundle: {
      optimizeTranslationDirective: false
    }
  },

  // 應用頭部配置 - 擴展 SEO 元素
  app: {
    head: {
      title: '遊戲王卡片系列 - 牌組構築與分享平台',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no' },
        { name: 'theme-color', content: '#1e90ff' },
        { name: 'apple-mobile-web-app-capable', content: 'yes' },
        { name: 'apple-mobile-web-app-status-bar-style', content: 'black-translucent' },
        { name: 'description', content: '遊戲王牌組構築與分享平台，集成iCard愛卡社群。提供卡片查詢、牌組構築、分享功能，支援多語言。' },
        { name: 'keywords', content: '遊戲王,卡片,牌組,決鬥,iCard愛卡,牌組構築,卡片查詢,遊戲王攻略' },
        { name: 'author', content: 'iCard愛卡' },
        { name: 'format-detection', content: 'telephone=no' },
        { name: 'robots', content: 'index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1' },
        { name: 'googlebot', content: 'index, follow' },
        { name: 'bingbot', content: 'index, follow' },
        // Open Graph
        { property: 'og:title', content: '遊戲王牌組構築器 - iCard愛卡' },
        { property: 'og:description', content: '遊戲王牌組構築與分享平台，集成iCard愛卡社群。提供卡片查詢、牌組構築、分享功能。' },
        { property: 'og:type', content: 'website' },
        { property: 'og:site_name', content: '遊戲王卡片系列' },
        { property: 'og:locale', content: 'zh_TW' },
        // 動態 URL 將通過插件設置
        { property: 'og:url', content: process.env.NUXT_PUBLIC_SITE_URL || 'https://ygo.iwantcard.tw' },
        { property: 'og:image', content: `${process.env.NUXT_PUBLIC_SITE_URL || 'https://ygo.iwantcard.tw'}/images/og-image.jpg` },
        { property: 'og:image:width', content: '1200' },
        { property: 'og:image:height', content: '630' },
        { property: 'og:image:alt', content: '遊戲王卡片系列 - 牌組構築平台' },
        // Twitter Card
        { name: 'twitter:card', content: 'summary_large_image' },
        { name: 'twitter:title', content: '遊戲王牌組構築器 - iCard愛卡' },
        { name: 'twitter:description', content: '遊戲王牌組構築與分享平台，集成iCard愛卡社群。' },
        { name: 'twitter:image', content: `${process.env.NUXT_PUBLIC_SITE_URL || 'https://ygo.iwantcard.tw'}/images/og-image.jpg` },
        { name: 'mobile-web-app-capable', content: 'yes' },
        // 額外的SEO優化
        { name: 'application-name', content: '遊戲王卡片系列' },
        { name: 'msapplication-TileColor', content: '#1e90ff' },
        { name: 'msapplication-config', content: '/browserconfig.xml' },
      ],
      link: [
        { rel: 'icon', type: 'image/png', href: '/images/icon.png' },
        { rel: 'canonical', href: process.env.NUXT_PUBLIC_SITE_URL || 'https://ygo.iwantcard.tw' },
        { rel: 'manifest', href: '/manifest.json' },
        { rel: 'apple-touch-icon', href: '/images/icon.png' },
        { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
        { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: '' },
        { rel: 'dns-prefetch', href: 'https://www.iwantcard.tw' },
      ],
      script: [
        // Google Analytics 4 (GA4) - 只在生產環境載入
        ...(process.env.NODE_ENV === 'production' ? [
          {
            src: 'https://www.googletagmanager.com/gtag/js?id=G-TF1J8E8CVV',
            async: true,
            defer: true
          },
          {
            innerHTML: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-TF1J8E8CVV', {
                send_page_view: true,
                anonymize_ip: true,
                debug_mode: false
              });
            `,
            type: 'text/javascript',
            tagPosition: 'bodyClose' as const
          }
        ] : []),
        // 結構化數據
        {
          type: 'application/ld+json',
          innerHTML: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'WebSite',
            'name': '遊戲王卡片系列',
            'description': '遊戲王牌組構築與分享平台，集成iCard愛卡社群',
            'url': process.env.NUXT_PUBLIC_SITE_URL || 'https://ygo.iwantcard.tw',
            'potentialAction': {
              '@type': 'SearchAction',
              'target': {
                '@type': 'EntryPoint',
                'urlTemplate': `${process.env.NUXT_PUBLIC_SITE_URL || 'https://ygo.iwantcard.tw'}/search?q={search_term_string}`
              },
              'query-input': 'required name=search_term_string'
            }
          })
        }
      ]
    }
  },

  css: [
    '~/assets/css/main.css',
    '~/assets/css/fix-double-scroll.css'
  ],

  build: {
    transpile: ['lucide-vue-next']
  },

  // 解決相容性日期警告
  nitro: {
    compatibilityDate: '2025-05-25',
    prerender: {
      crawlLinks: false,       // 關閉自動爬連結
      failOnError: false,      // 避免 build 因預渲染錯誤終止
      routes: [
        '/',
        '/series',
        '/deck',
        '/Goodsgroup',
        '/series/1',
        '/series/2',
        '/series/3'
        // '/sponsor' // 暫時隱藏贊助頁面
      ]
    },
    storage: {
      'redis': {
        driver: 'redis',
      },
      'cache': {
        driver: 'fs',
        base: './.cache'
      }
    },
    // 修復預載問題
    devProxy: {
      '/_nuxt/builds/meta/dev.json': {
        target: 'http://localhost:3000',
        changeOrigin: true
      }
    }
  },

  // 啟用 SSR 以提高 SEO 效果
  ssr: true,

  // 路由設置
  router: {
    options: {
      strict: true
    }
  },

  // 移除實驗性功能
  experimental: {
    // 啟用渲染優化
    renderJsonPayloads: true,
    // 啟用本地資源別名
    localLayerAliases: true,
    // 啟用異步上下文
    asyncContext: true,
    // 啟用組件緩存
    componentIslands: true,
    // 啟用路由的暫存緩存
    payloadExtraction: true,
    // 修復中間件重複宣告問題
    asyncEntry: true
  },

  pwa: {
    registerType: 'autoUpdate',
    manifest: {
      name: '遊戲王卡片系列',
      short_name: '遊戲王',
      description: '遊戲王卡片系列查詢與收藏',
      theme_color: '#1e90ff',
      background_color: '#1a1a1a',
      display: 'standalone',
      orientation: 'portrait',
      scope: '/',
      start_url: '/',
      icons: [
        {
          src: '/images/icon.png',
          sizes: '72x72',
          type: 'image/png'
        },
        {
          src: '/images/icon.png',
          sizes: '96x96',
          type: 'image/png'
        },
        {
          src: '/images/icon.png',
          sizes: '128x128',
          type: 'image/png'
        },
        {
          src: '/images/icon.png',
          sizes: '144x144',
          type: 'image/png'
        },
        {
          src: '/images/icon.png',
          sizes: '152x152',
          type: 'image/png'
        },
        {
          src: '/images/icon.png',
          sizes: '192x192',
          type: 'image/png'
        },
        {
          src: '/images/icon.png',
          sizes: '384x384',
          type: 'image/png'
        },
        {
          src: '/images/icon.png',
          sizes: '512x512',
          type: 'image/png'
        }
      ]
    },
    workbox: {
      navigateFallback: '/',
      runtimeCaching: [
        {
          urlPattern: 'https://www.iwantcard.tw/api/.*',
          handler: 'NetworkFirst',
          method: 'GET',
          options: {
            cacheName: 'api-cache',
            expiration: {
              maxEntries: 100,
              maxAgeSeconds: 24 * 60 * 60 // 24 hours
            },
            cacheableResponse: {
              statuses: [0, 200]
            }
          }
        }
      ]
    }
  }
})