// utils/logger.ts
interface Logger {
  log: (...args: any[]) => void
  info: (...args: any[]) => void
  warn: (...args: any[]) => void
  error: (...args: any[]) => void
  debug: (...args: any[]) => void
}

const createLogger = (): Logger => {
  const isProduction = process.env.NODE_ENV === 'production'

  return {
    log: isProduction ? () => {} : console.log,
    info: isProduction ? () => {} : console.info,
    warn: isProduction ? () => {} : console.warn,
    error: console.error, // 错误日志在生产环境中仍然显示
    debug: isProduction ? () => {} : console.debug
  }
}

export const logger = createLogger()

// 使用示例：
// import { logger } from '~/utils/logger'
// logger.log('这条日志在生产环境中不会显示')
// logger.error('这条错误日志会在所有环境中显示')
