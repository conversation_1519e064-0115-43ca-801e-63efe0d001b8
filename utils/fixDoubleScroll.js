/**
 * 手機版雙重滾動問題根治工具函數
 * 配合 fix-double-scroll.css 使用
 */

/**
 * 檢測是否為手機設備
 */
export function isMobileDevice() {
    return window.innerWidth <= 767;
}

/**
 * 初始化手機版安全滾動
 */
export function initMobileSafeScroll() {
    if (!isMobileDevice()) return;
    
    // 設置視窗高度CSS變數
    const vh = window.innerHeight * 0.01;
    document.documentElement.style.setProperty('--vh', `${vh}px`);
    
    // 監聽視窗大小變化（處理手機旋轉、虛擬鍵盤等）
    const handleResize = () => {
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);
    };
    
    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleResize);
    
    return () => {
        window.removeEventListener('resize', handleResize);
        window.removeEventListener('orientationchange', handleResize);
    };
}

/**
 * 確保底部導航正確定位
 */
export function ensureBottomNavFixed() {
    if (!isMobileDevice()) return;
    
    const bottomNav = document.querySelector('.mobile-bottom-nav');
    if (!bottomNav) return;
    
    // 確保底部導航的樣式正確
    bottomNav.style.position = 'fixed';
    bottomNav.style.bottom = '0';
    bottomNav.style.left = '0';
    bottomNav.style.right = '0';
    bottomNav.style.zIndex = '9999';
    bottomNav.style.height = '4rem';
    
    // 確保內容區域有正確的高度
    const contentWrapper = document.querySelector('.content-wrapper');
    if (contentWrapper) {
        contentWrapper.style.paddingBottom = '1rem';
        contentWrapper.style.maxHeight = 'calc(100vh - 4rem)';
        contentWrapper.style.maxHeight = 'calc(100dvh - 4rem)';
        contentWrapper.style.overflowY = 'auto';
        contentWrapper.style.overflowX = 'hidden';
    }
}

/**
 * 修復頁面滾動到底部的問題
 */
export function fixScrollToBottom() {
    if (!isMobileDevice()) return;
    
    const mainContent = document.querySelector('.main-content');
    const contentWrapper = document.querySelector('.content-wrapper');
    
    if (mainContent) {
        mainContent.style.height = '100vh';
        mainContent.style.height = '100dvh';
        mainContent.style.overflowY = 'auto';
        mainContent.style.overflowX = 'hidden';
    }
    
    if (contentWrapper) {
        // 確保內容包裝器不會延伸到底部導航下方
        contentWrapper.style.minHeight = 'calc(100vh - 4rem)';
        contentWrapper.style.minHeight = 'calc(100dvh - 4rem)';
        contentWrapper.style.paddingBottom = '1rem';
    }
}

/**
 * 處理特殊頁面的滾動問題（如評論頁面）
 */
export function fixSpecialPageScroll(pageType = 'default') {
    if (!isMobileDevice()) return;
    
    const bottomComment = document.querySelector('.mobile-bottom-comment');
    
    switch (pageType) {
        case 'message':
        case 'comment':
            // 評論頁面特殊處理
            if (bottomComment) {
                bottomComment.style.bottom = '4rem'; // 底部導航高度
                bottomComment.style.left = '0';
                bottomComment.style.right = '0';
                bottomComment.style.maxWidth = '100vw';
            }
            
            // 調整內容區域高度
            const messageContent = document.querySelector('.content-wrapper');
            if (messageContent) {
                messageContent.style.paddingBottom = '8rem'; // 底部導航 + 評論框
            }
            break;
            
        case 'modal':
        case 'popup':
            // 模態框和彈窗處理
            const modals = document.querySelectorAll('.modal, .dialog, .popup');
            modals.forEach(modal => {
                modal.style.maxHeight = 'calc(100vh - 4rem)';
                modal.style.maxHeight = 'calc(100dvh - 4rem)';
                modal.style.overflowY = 'auto';
                modal.style.overflowX = 'hidden';
            });
            break;
    }
}

/**
 * 防止水平滾動的事件處理器
 */
export function preventHorizontalScroll(element) {
    if (!element) return;
    
    const handleWheel = (e) => {
        // 如果是水平滾動，防止事件
        if (Math.abs(e.deltaX) > Math.abs(e.deltaY)) {
            e.preventDefault();
        }
    };
    
    const handleTouchMove = (e) => {
        // 防止水平滑動
        const touch = e.touches[0];
        const startX = touch.clientX;
        const startY = touch.clientY;
        
        // 如果主要是水平滑動，防止事件
        const deltaX = Math.abs(touch.clientX - startX);
        const deltaY = Math.abs(touch.clientY - startY);
        
        if (deltaX > deltaY && deltaX > 10) {
            e.preventDefault();
        }
    };
    
    element.addEventListener('wheel', handleWheel, { passive: false });
    element.addEventListener('touchmove', handleTouchMove, { passive: false });
    
    return () => {
        element.removeEventListener('wheel', handleWheel);
        element.removeEventListener('touchmove', handleTouchMove);
    };
}

/**
 * Vue 3 Composable: 手機版滾動修復
 */
export function useMobileScrollFix(options = {}) {
    const { 
        pageType = 'default',
        enableHorizontalScrollPrevention = true,
        autoFix = true 
    } = options;
    
    let cleanupFunctions = [];
    
    const init = () => {
        if (!isMobileDevice()) return;
        
        // 初始化安全滾動
        const cleanupResize = initMobileSafeScroll();
        if (cleanupResize) cleanupFunctions.push(cleanupResize);
        
        // 確保底部導航正確
        ensureBottomNavFixed();
        
        // 修復滾動到底部的問題
        fixScrollToBottom();
        
        // 處理特殊頁面
        fixSpecialPageScroll(pageType);
        
        // 防止水平滾動
        if (enableHorizontalScrollPrevention) {
            const body = document.body;
            const cleanupHorizontal = preventHorizontalScroll(body);
            if (cleanupHorizontal) cleanupFunctions.push(cleanupHorizontal);
        }
    };
    
    const cleanup = () => {
        cleanupFunctions.forEach(fn => fn && fn());
        cleanupFunctions = [];
    };
    
    if (autoFix) {
        // 確保DOM載入完成後執行
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', init);
        } else {
            setTimeout(init, 100);
        }
    }
    
    return {
        init,
        cleanup,
        isMobileDevice,
        ensureBottomNavFixed,
        fixScrollToBottom,
        fixSpecialPageScroll
    };
}

/**
 * 自動修復函數 - 在支援的環境中自動執行
 */
export function autoFixMobileScroll() {
    if (typeof window === 'undefined') return;
    
    const { init } = useMobileScrollFix({ autoFix: false });
    
    // DOM載入完成後自動執行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
    // 也在頁面完全載入後再執行一次
    window.addEventListener('load', init);
}

// 如果在瀏覽器環境中，自動執行修復
if (typeof window !== 'undefined') {
    autoFixMobileScroll();
}